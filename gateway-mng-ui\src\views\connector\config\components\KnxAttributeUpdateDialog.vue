<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑属性更新' : '添加属性更新'"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="updateForm" :rules="rules" ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="键名" prop="key" required>
            <el-input 
              v-model="updateForm.key" 
              placeholder="brightness"
            />
            <div class="field-hint">属性的键名标识</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="KNX群组地址" prop="groupAddress" required>
            <el-input 
              v-model="updateForm.groupAddress" 
              placeholder="1/0/9"
            />
            <div class="field-hint">要写入的KNX群组地址</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名筛选器" prop="deviceNameFilter" required>
            <el-input 
              v-model="updateForm.deviceNameFilter" 
              placeholder=".*"
            />
            <div class="field-hint">正则表达式，用于匹配设备名称</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据类型" prop="dataType" required>
            <el-select v-model="updateForm.dataType" placeholder="请选择数据类型">
              <!-- 基础数据类型 -->
              <el-option-group label="基础数据类型">
                <el-option label="布尔值 (bool)" value="bool" />
                <el-option label="整数 (int)" value="int" />
                <el-option label="浮点数 (float)" value="float" />
                <el-option label="字符串 (string)" value="string" />
              </el-option-group>
              
              <!-- KNX专用数据类型 -->
              <el-option-group label="KNX数据类型">
                <el-option label="温度 (temperature)" value="temperature" />
                <el-option label="湿度 (humidity)" value="humidity" />
                <el-option label="亮度 (brightness)" value="brightness" />
                <el-option label="百分比 U8 (precent_U8)" value="precent_U8" />
                <el-option label="百分比 V8 (precent_V8)" value="precent_V8" />
                <el-option label="角度 (angle)" value="angle" />
                <el-option label="色彩 (color)" value="color" />
                <el-option label="时间 (time)" value="time" />
                <el-option label="日期 (date)" value="date" />
              </el-option-group>
            </el-select>
            <div class="field-hint">属性值的数据类型</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 配置说明 -->
      <div class="config-help">
        <el-alert
          title="属性更新配置说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>功能说明:</strong></p>
            <p>属性更新配置用于将IoTCloud平台的属性值写入到KNX设备中，实现设备控制功能。</p>
            
            <p><strong>配置参数:</strong></p>
            <ul>
              <li><strong>键名</strong> - IoTCloud设备属性的键名</li>
              <li><strong>群组地址</strong> - KNX设备要写入的群组地址</li>
              <li><strong>设备名筛选器</strong> - 正则表达式，用于匹配要更新的设备</li>
              <li><strong>数据类型</strong> - 写入KNX的数据类型格式</li>
            </ul>
            
            <p><strong>使用场景:</strong></p>
            <ul>
              <li>调光控制: key="brightness", groupAddress="1/0/9", dataType="precent_U8"</li>
              <li>开关控制: key="switch", groupAddress="1/0/1", dataType="bool"</li>
              <li>温度设定: key="setpoint", groupAddress="1/0/8", dataType="temperature"</li>
            </ul>
          </div>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  attributeUpdate: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 更新表单数据
const updateForm = reactive({
  deviceNameFilter: '.*',
  dataType: 'precent_U8',
  groupAddress: '1/0/9',
  key: 'brightness'
})

// 表单验证规则
const rules = reactive({
  key: [
    { required: true, message: '请输入键名', trigger: 'blur' }
  ],
  groupAddress: [
    { required: true, message: '请输入KNX群组地址', trigger: 'blur' },
    { 
      pattern: /^(\d+\/\d+\/\d+|\d+)$/,
      message: '群组地址格式：x/y/z 或 xyz',
      trigger: 'blur'
    }
  ],
  deviceNameFilter: [
    { required: true, message: '请输入设备名筛选器', trigger: 'blur' }
  ],
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.attributeUpdate) {
    // 加载属性更新数据
    Object.assign(updateForm, {
      deviceNameFilter: '.*',
      dataType: 'precent_U8',
      groupAddress: '1/0/9',
      key: 'brightness',
      ...props.attributeUpdate
    })
  } else if (newValue) {
    // 重置为默认值
    Object.assign(updateForm, {
      deviceNameFilter: '.*',
      dataType: 'precent_U8',
      groupAddress: '1/0/9',
      key: 'brightness'
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...updateForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '属性更新配置更新成功' : '属性更新配置添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style> 