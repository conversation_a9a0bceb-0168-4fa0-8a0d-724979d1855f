<template>
  <div class="gateway-configuration">
    <!-- 配置内容 -->
    <div class="config-content">
      <!-- 基础配置模式 -->
      <div v-if="configMode === 'basic'" class="basic-config">
        <el-tabs v-model="activeTab" type="border-card" class="config-tabs">
          <!-- 通用配置 -->
          <el-tab-pane label="通用配置" name="general">
            <GatewayGeneralConfig 
              v-model="gatewayConfig.thingsboard"
              @change="handleConfigChange"
            />
          </el-tab-pane>

          <!-- 日志配置 -->
          <el-tab-pane label="日志配置" name="logs">
            <GatewayLogsConfig 
              v-model="gatewayConfig.logs"
              @change="handleConfigChange"
            />
          </el-tab-pane>

          <!-- 存储配置 -->
          <el-tab-pane label="存储配置" name="storage">
            <GatewayStorageConfig 
              v-model="gatewayConfig.storage"
              @change="handleConfigChange"
            />
          </el-tab-pane>

          <!-- GRPC配置 -->
          <el-tab-pane label="GRPC配置" name="grpc">
            <GatewayGrpcConfig 
              v-model="gatewayConfig.grpc"
              @change="handleConfigChange"
            />
          </el-tab-pane>

          <!-- 数据统计 -->
          <el-tab-pane label="数据统计" name="statistics">
            <GatewayStatisticsConfig 
              v-model="gatewayConfig.thingsboard.statistics"
              :remote-configuration="gatewayConfig.thingsboard.remoteConfiguration"
              @change="handleConfigChange"
            />
          </el-tab-pane>

          <!-- 设备过滤配置 -->
          <el-tab-pane label="设备过滤" name="device-filtering">
            <GatewayDeviceFilteringConfig 
              v-model="gatewayConfig.thingsboard.deviceFiltering"
              @change="handleConfigChange"
            />
          </el-tab-pane>

          <!-- 其他配置 -->
          <el-tab-pane label="其他配置" name="other">
            <GatewayOtherConfig 
              v-model="gatewayConfig.thingsboard"
              @change="handleConfigChange"
            />
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 高级配置模式 -->
      <div v-else class="advanced-config">
        <GatewayAdvancedConfig 
          v-model="gatewayConfig"
          @change="handleConfigChange"
        />
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="config-footer">
      <div class="footer-content">
        <div class="mode-toggle-section">
          <span class="mode-label">配置模式：</span>
          <el-button-group class="mode-toggle">
            <el-button 
              :type="configMode === 'basic' ? 'primary' : ''"
              @click="configMode = 'basic'"
              size="small"
            >
              基础配置模式
            </el-button>
            <el-button 
              :type="configMode === 'advanced' ? 'primary' : ''"
              @click="configMode = 'advanced'"
              size="small"
            >
              高级配置模式
            </el-button>
          </el-button-group>
        </div>
        <div class="footer-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleSave"
            :loading="saving"
            :disabled="!hasChanges"
          >
            保存配置
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { onBeforeRouteLeave } from "vue-router"
import { getFileFromName, updataFile } from '@/api/file.js'
import { cleanEmptyValues } from '@/utils/configUtils.js'

// 导入子组件
import GatewayGeneralConfig from './components/GatewayGeneralConfig.vue'
import GatewayLogsConfig from './components/GatewayLogsConfig.vue'
import GatewayStorageConfig from './components/GatewayStorageConfig.vue'
import GatewayGrpcConfig from './components/GatewayGrpcConfig.vue'
import GatewayStatisticsConfig from './components/GatewayStatisticsConfig.vue'
import GatewayOtherConfig from './components/GatewayOtherConfig.vue'
import GatewayAdvancedConfig from './components/GatewayAdvancedConfig.vue'
import GatewayDeviceFilteringConfig from './components/GatewayDeviceFilteringConfig.vue'

// 响应式数据
const configMode = ref('basic')
const activeTab = ref('general')
const saving = ref(false)
const hasChanges = ref(false)

// 网关配置数据
const gatewayConfig = reactive({
  thingsboard: {
    host: '',
    port: 1883,
    remoteShell: true,
    remoteConfiguration: true,
    security: {
      type: 'accessToken',
      accessToken: ''
    },
    reportStrategy: {
      type: 'ON_RECEIVED',
      reportPeriod: 60000
    },
    statistics: {
      enable: true,
      statsSendPeriodInSeconds: 60,
      customStatsSendPeriodInSeconds: 3600,
      configuration: 'statistics.json'
    },
    checkingDeviceActivity: {
      checkDeviceInactivity: true,
      inactivityTimeoutSeconds: 300,
      inactivityCheckPeriodSeconds: 10
    },
    maxPayloadSizeBytes: 8196,
    minPackSendDelayMS: 50,
    minPackSizeToSend: 500,
    checkConnectorsConfigurationInSeconds: 60,
    handleDeviceRenaming: true,
    qos: 1
  },
  storage: {
    type: 'memory',
    read_records_count: 100,
    max_records_count: 100000,
    data_folder_path: './data/',
    max_file_count: 10,
    max_read_records_count: 10,
    max_records_per_file: 10000,
    data_file_path: './data/data.db',
    messages_ttl_check_in_hours: 1,
    messages_ttl_in_days: 7
  },
  grpc: {
    enabled: false,
    serverPort: 9595,
    keepAliveTimeMs: 10001,
    keepAliveTimeoutMs: 5000,
    keepAlivePermitWithoutCalls: true,
    maxPingsWithoutData: 0,
    minTimeBetweenPingsMs: 10000,
    minPingIntervalWithoutDataMs: 5000
  },
  logs: {
    dateFormat: '%Y-%m-%d %H:%M:%S',
    logFormat: '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    logLevel: 'INFO',
    local: {
      service: {
        logLevel: 'INFO',
        filePath: './logs/service.log',
        backupCount: 7,
        savingTime: 1,
        savingPeriod: 'D'
      },
      connector: {
        logLevel: 'INFO',
        filePath: './logs/connector.log',
        backupCount: 7,
        savingTime: 1,
        savingPeriod: 'D'
      },
      converter: {
        logLevel: 'INFO',
        filePath: './logs/converter.log',
        backupCount: 7,
        savingTime: 1,
        savingPeriod: 'D'
      },
      tb_connection: {
        logLevel: 'INFO',
        filePath: './logs/tb_connection.log',
        backupCount: 7,
        savingTime: 1,
        savingPeriod: 'D'
      },
      storage: {
        logLevel: 'INFO',
        filePath: './logs/storage.log',
        backupCount: 7,
        savingTime: 1,
        savingPeriod: 'D'
      },
      extension: {
        logLevel: 'INFO',
        filePath: './logs/extension.log',
        backupCount: 7,
        savingTime: 1,
        savingPeriod: 'D'
      }
    }
  }
})

// 原始配置备份（用于检测变化）
let originalConfig = null

// 初始化
onMounted(() => {
  loadConfiguration()
})

// 监听配置模式变化（移除保存mode到配置的逻辑）
watch(configMode, (newMode) => {
  // 只更新界面显示，不保存到配置文件
  console.log('配置模式切换到:', newMode)
})

// 监听配置变化
watch(
  () => gatewayConfig,
  () => {
    if (originalConfig) {
      hasChanges.value = JSON.stringify(gatewayConfig) !== JSON.stringify(originalConfig)
    }
  },
  { deep: true }
)

// 加载配置
const loadConfiguration = async () => {
  try {
    const response = await getFileFromName('tb_gateway.json')
    if (response.data) {
      Object.assign(gatewayConfig, response.data)
      originalConfig = JSON.parse(JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败')
  }
}

// 处理配置变化
const handleConfigChange = () => {
  // 配置变化处理逻辑
  console.log('配置已更新:', gatewayConfig)
}

// 保存配置
const handleSave = async () => {
  try {
    await ElMessageBox.confirm(
      '是否确认保存更改内容，此操作将更新网关配置数据？',
      '提示',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    saving.value = true
    
    // 清理配置中的空值，确保不包含mode字段
    const cleanedConfig = cleanEmptyValues(gatewayConfig)
    // 确保删除mode字段（如果存在）
    delete cleanedConfig.mode
    
    const response = await updataFile({
      file_name: 'tb_gateway.json',
      file_text: JSON.stringify(cleanedConfig, null, 2)
    })

    if (response.msg === 'success') {
      ElMessage.success('配置保存成功')
      originalConfig = JSON.parse(JSON.stringify(gatewayConfig))
      hasChanges.value = false
    } else {
      ElMessage.error('保存失败，请联系管理员')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存配置失败:', error)
      ElMessage.error('保存配置失败')
    }
  } finally {
    saving.value = false
  }
}

// 取消配置
const handleCancel = async () => {
  if (hasChanges.value) {
    try {
      await ElMessageBox.confirm(
        '有未保存的更改，是否确认取消？',
        '提示',
    {
      confirmButtonText: '确认',
          cancelButtonText: '继续编辑',
      type: 'warning',
        }
      )
      
      // 重新加载原始配置
      if (originalConfig) {
        Object.assign(gatewayConfig, JSON.parse(JSON.stringify(originalConfig)))
        hasChanges.value = false
      }
    } catch {
      // 用户选择继续编辑
    }
  }
}

// 路由离开守卫
onBeforeRouteLeave((to, from, next) => {
  if (hasChanges.value) {
    ElMessageBox.confirm(
      "修改尚未保存，是否确认离开？",
      "提示",
      {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
      }
    ).then(() => {
      next()
    }).catch(() => {
      next(false)
    })
  } else {
    next()
  }
})
</script>

<style lang="scss" scoped>
.gateway-configuration {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f7fa;
}

.config-content {
  flex: 1;
  overflow: hidden;
  
  .basic-config,
  .advanced-config {
    height: 100%;
    padding: 24px;
    
    .config-tabs {
      height: 100%;
      border: none;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      
      :deep(.el-tabs__header) {
  margin: 0;
        border-bottom: 1px solid #e4e7ed;
        background: #fff;
      }
      
      :deep(.el-tabs__content) {
        height: calc(100% - 56px);
        overflow-y: auto;
        padding: 0;
      }
      
      :deep(.el-tab-pane) {
        height: 100%;
        padding: 24px;
      }
    }
  }
}

.config-footer {
  background: #fff;
  border-top: 1px solid #e4e7ed;
  padding: 16px 24px;
  
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .mode-toggle-section {
    display: flex;
    align-items: center;
    
    .mode-label {
      margin-right: 12px;
    }
  }
  
  .footer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .config-content {
    .basic-config,
    .advanced-config {
      padding: 16px;
    }
  }
  
  .config-footer {
    padding: 12px 16px;
    
    .footer-content {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }
    
    .mode-toggle-section {
      justify-content: center;
      
      .mode-label {
        margin-right: 8px;
        font-size: 14px;
      }
    }
    
    .footer-actions {
      justify-content: center;
    }
  }
}
</style>