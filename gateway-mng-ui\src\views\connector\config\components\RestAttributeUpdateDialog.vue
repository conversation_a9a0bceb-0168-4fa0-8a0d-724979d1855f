<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="70%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基础配置 -->
      <div class="section">
        <div class="section-title">端点配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="端点路径" prop="endpoint" required>
              <el-input 
                v-model="form.endpoint" 
                placeholder="/attributes/update"
              />
              <div class="field-hint">属性更新的端点路径</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="HTTP方法" prop="HTTPMethods" required>
              <el-select 
                v-model="form.HTTPMethods" 
                multiple 
                placeholder="选择HTTP方法"
                style="width: 100%"
              >
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 筛选条件 -->
      <div class="section">
        <div class="section-title">筛选条件</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备筛选" prop="deviceNameFilter" required>
              <el-input 
                v-model="form.deviceNameFilter" 
                placeholder=".*"
              />
              <div class="field-hint">设备名称的正则表达式筛选</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="属性筛选" prop="attributeFilter" required>
              <el-input 
                v-model="form.attributeFilter" 
                placeholder=".*"
              />
              <div class="field-hint">属性名称的正则表达式筛选</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表达式配置 -->
      <div class="section">
        <div class="section-title">表达式配置</div>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请求体表达式" prop="requestBodyExpression">
              <el-input 
                v-model="form.requestBodyExpression" 
                type="textarea"
                :rows="4"
                placeholder='{"deviceName": "${deviceName}", "attributeName": "${attributeName}", "value": ${attributeValue}}'
              />
              <div class="field-hint">生成请求体的表达式，支持JSON格式</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="响应值表达式" prop="valueExpression">
              <el-input 
                v-model="form.valueExpression" 
                placeholder="${response.result}"
              />
              <div class="field-hint">从响应中提取结果值的表达式</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        
        <div class="headers-config">
          <div class="headers-header">
            <span>请求头设置</span>
            <el-button type="primary" size="small" @click="addHeader">
              <el-icon><Plus /></el-icon>
              添加头部
            </el-button>
          </div>
          
          <div v-if="!form.httpHeaders || Object.keys(form.httpHeaders).length === 0" class="empty-headers">
            <el-empty description="暂无HTTP头配置" :image-size="60">
              <el-button type="primary" size="small" @click="addHeader">添加第一个头部</el-button>
            </el-empty>
          </div>
          
          <div v-else class="headers-list">
            <div 
              v-for="(value, key) in form.httpHeaders" 
              :key="key" 
              class="header-item"
            >
              <el-input 
                :model-value="key" 
                placeholder="头部名称"
                @input="updateHeaderKey(key, $event)"
                style="width: 200px"
              />
              <el-input 
                v-model="form.httpHeaders[key]" 
                placeholder="头部值"
                style="flex: 1; margin: 0 8px"
              />
              <el-button 
                type="danger" 
                size="small" 
                @click="removeHeader(key)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全配置 -->
      <div class="section">
        <div class="section-title">安全配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认证类型" prop="security.type">
              <el-select v-model="form.security.type" style="width: 100%">
                <el-option label="匿名访问" value="anonymous" />
                <el-option label="基本认证" value="basic" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.security.type === 'basic'">
            <el-form-item label="用户名" prop="security.username">
              <el-input v-model="form.security.username" placeholder="username" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.security.type === 'basic'">
            <el-form-item label="密码" prop="security.password">
              <el-input 
                v-model="form.security.password" 
                type="password" 
                placeholder="password"
                show-password 
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 请求参数配置 -->
      <div class="section">
        <div class="section-title">请求参数</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="响应超时(秒)" prop="responseTimeout">
              <el-input-number 
                v-model="form.responseTimeout" 
                :min="0.1" 
                :precision="1"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="请求超时(秒)" prop="timeout">
              <el-input-number 
                v-model="form.timeout" 
                :min="1" 
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重试次数" prop="tries">
              <el-input-number 
                v-model="form.tries" 
                :min="1" 
                :max="10"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="允许重定向">
              <el-switch v-model="form.allowRedirects" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 帮助说明 -->
      <div class="section">
        <div class="section-title">表达式变量说明</div>
        <div class="help-content">
          <el-alert
            title="可用的表达式变量"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul class="help-list">
                <li><code>${deviceName}</code> - 设备名称</li>
                <li><code>${attributeName}</code> - 属性名称</li>
                <li><code>${attributeValue}</code> - 属性值</li>
                <li><code>${ts}</code> - 时间戳</li>
                <li><code>${response.result}</code> - 响应结果</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  attributeUpdate: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const formRef = ref()
const saving = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑属性更新' : '添加属性更新'
})

// 默认表单结构
const defaultForm = {
  endpoint: '/attributes/update',
  HTTPMethods: ['POST'],
  deviceNameFilter: '.*',
  attributeFilter: '.*',
  requestBodyExpression: '{"deviceName": "${deviceName}", "attributeName": "${attributeName}", "value": ${attributeValue}}',
  valueExpression: '${response.result}',
  httpHeaders: {
    'Content-Type': 'application/json'
  },
  security: {
    type: 'anonymous'
  },
  responseTimeout: 1,
  timeout: 10,
  tries: 3,
  allowRedirects: true
}

const form = reactive({ ...defaultForm })

// 表单验证规则
const rules = reactive({
  endpoint: [
    { required: true, message: '请输入端点路径', trigger: 'blur' }
  ],
  HTTPMethods: [
    { required: true, message: '请选择HTTP方法', trigger: 'change' }
  ],
  deviceNameFilter: [
    { required: true, message: '请输入设备筛选条件', trigger: 'blur' }
  ],
  attributeFilter: [
    { required: true, message: '请输入属性筛选条件', trigger: 'blur' }
  ]
})

// 监听属性更新数据变化
watch(() => props.attributeUpdate, (newUpdate) => {
  if (newUpdate && props.modelValue) {
    Object.assign(form, JSON.parse(JSON.stringify(newUpdate)))
  }
}, { deep: true, immediate: true })

// 监听对话框状态
watch(() => props.modelValue, (newVal) => {
  if (newVal && !props.attributeUpdate) {
    // 新增时重置表单
    Object.assign(form, JSON.parse(JSON.stringify(defaultForm)))
  }
})

// 添加HTTP头
const addHeader = () => {
  if (!form.httpHeaders) {
    form.httpHeaders = {}
  }
  const newKey = `header-${Date.now()}`
  form.httpHeaders[newKey] = ''
}

// 删除HTTP头
const removeHeader = (key) => {
  delete form.httpHeaders[key]
}

// 更新HTTP头键名
const updateHeaderKey = (oldKey, newKey) => {
  if (oldKey !== newKey && newKey) {
    const value = form.httpHeaders[oldKey]
    delete form.httpHeaders[oldKey]
    form.httpHeaders[newKey] = value
  }
}

// 保存配置
const handleSave = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    saving.value = true

    // 深拷贝表单数据
    const updateData = JSON.parse(JSON.stringify(form))
    
    emit('save', updateData)
    
    ElMessage.success(props.isEdit ? '属性更新配置更新成功' : '属性更新配置添加成功')
    handleClose()
  } catch (error) {
    console.error('保存属性更新配置失败:', error)
    ElMessage.error('保存属性更新配置失败')
  } finally {
    saving.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.headers-config {
  .headers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 600;
    color: #606266;
  }

  .empty-headers {
    text-align: center;
    padding: 20px;
  }

  .headers-list {
    .header-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.help-content {
  .help-list {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 4px;
      
      code {
        background-color: #f5f5f5;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: monospace;
        color: #e53e3e;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-textarea) {
  width: 100%;
}
</style>
