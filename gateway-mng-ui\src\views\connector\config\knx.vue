<template>
  <div class="knx-config">
    <el-tabs v-model="activeTab" class="knx-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- KNX客户端配置标签页 -->
      <el-tab-pane label="KNX客户端配置" name="client">
        <KnxClientConfig 
          v-model="config.client" 
        />
      </el-tab-pane>

      <!-- 设备配置标签页 -->
      <el-tab-pane label="设备配置" name="devices">
        <KnxDevicesConfig 
          v-model="config.devices" 
        />
      </el-tab-pane>

      <!-- 属性更新配置标签页 -->
      <el-tab-pane label="属性更新配置" name="attributeUpdates">
        <KnxAttributeUpdatesConfig 
          v-model="config.attributeUpdates" 
        />
      </el-tab-pane>

      <!-- RPC配置标签页 -->
      <el-tab-pane label="RPC配置" name="serverSideRpc">
        <KnxRpcConfig 
          v-model="config.serverSideRpc" 
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import KnxClientConfig from './components/KnxClientConfig.vue'
import KnxDevicesConfig from './components/KnxDevicesConfig.vue'
import KnxAttributeUpdatesConfig from './components/KnxAttributeUpdatesConfig.vue'
import KnxRpcConfig from './components/KnxRpcConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  client: {
    type: 'AUTOMATIC',
    addressFormat: 'LONG',
    localIp: '0.0.0.0',
    localPort: 3671,
    gatewayIP: '127.0.0.1',
    gatewayPort: 3671,
    individualAddress: '1.0.10',
    rateLimit: 0,
    autoReconnect: true,
    autoReconnectWait: 3,
    gatewaysScanner: {
      enabled: true,
      scanPeriod: 3,
      stopOnFound: false
    }
  },
  devices: [],
  attributeUpdates: [],
  serverSideRpc: []
}

// KNX 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'knx', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
        
      console.log('KNX 连接器配置初始化成功')
    } else {
      ElMessage.warning('KNX 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('KNX 连接器初始化失败:', error)
    ElMessage.error('KNX 连接器初始化失败')
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  knx: config
})
</script>

<style lang="scss" scoped>
.knx-config {
  .knx-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
}
</style> 