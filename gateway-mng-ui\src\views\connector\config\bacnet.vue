<template>
  <div class="bacnet-config">
    <el-tabs v-model="activeTab" class="bacnet-tabs" v-if="configInitialized">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- 应用配置标签页 -->
      <el-tab-pane label="应用配置" name="application">
        <BacnetApplicationConfig 
          v-model="config.application" 
          v-if="config.application"
        />
      </el-tab-pane>

      <!-- 设备配置标签页 -->
      <el-tab-pane label="设备配置" name="devices">
        <BacnetDevicesConfig 
          v-model="config.devices" 
          v-if="config.devices"
        />
      </el-tab-pane>
    </el-tabs>
    <div v-else class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import BacnetApplicationConfig from './components/BacnetApplicationConfig.vue'
import BacnetDevicesConfig from './components/BacnetDevicesConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')
const configInitialized = ref(false)

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  application: {
    objectName: 'TB_gateway',
    host: '0.0.0.0',
    port: 47808,
    mask: '',
    objectIdentifier: 599,
    maxApduLengthAccepted: 1476,
    segmentationSupported: 'segmentedBoth',
    networkNumber: 3,
    vendorIdentifier: 15,
    deviceDiscoveryTimeoutInSec: 5
  },
  devices: []
}

// 配置数据结构 - 使用ref而不是reactive
const config = ref({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config.value, 'bacnet', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config.value)
      
      // 标记配置已初始化
      configInitialized.value = true
        
      console.log('BACnet 连接器配置初始化成功')
    } else {
      ElMessage.warning('BACnet 连接器配置初始化部分失败，请检查配置')
      configInitialized.value = true // 即使失败也要显示界面
    }
  } catch (error) {
    console.error('BACnet 连接器初始化失败:', error)
    ElMessage.error('BACnet 连接器初始化失败')
    configInitialized.value = true // 即使失败也要显示界面
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  bacnet: config
})
</script>

<style lang="scss" scoped>
.bacnet-config {
  .bacnet-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
  
  .loading-state {
    padding: 20px;
  }
}
</style>