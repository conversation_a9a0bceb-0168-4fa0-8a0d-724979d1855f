<template>
  <div class="odbc-pyodbc-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>PYODBC 驱动配置</span>
          <el-tooltip content="配置Python ODBC驱动的特定参数和性能选项" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="pyodbcConfig" :rules="rules" label-width="140px" ref="formRef">
        <!-- 基础配置 -->
        <div class="config-section">
          <div class="section-title">基础设置</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="启用连接池">
                <el-switch v-model="pyodbcConfig.pooling"  />
                <div class="field-hint">是否启用数据库连接池优化</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="线程安全级别" prop="threadsafety">
                <el-select v-model="pyodbcConfig.threadsafety" placeholder="请选择线程安全级别" >
                  <el-option label="0 - 不支持" :value="0" />
                  <el-option label="1 - 最小支持" :value="1" />
                  <el-option label="2 - 模块级支持" :value="2" />
                  <el-option label="3 - 完全支持" :value="3" />
                </el-select>
                <div class="field-hint">线程安全支持级别</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 版本配置 -->
        <div class="config-section">
          <div class="section-title">版本信息</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="PYODBC版本" prop="version">
                <el-input 
                  v-model="pyodbcConfig.version" 
                  placeholder="4.0.35"
                  
                />
                <div class="field-hint">PYODBC模块版本号</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="API版本" prop="apilevel">
                <el-select v-model="pyodbcConfig.apilevel" placeholder="请选择API版本" >
                  <el-option label="1.0" value="1.0" />
                  <el-option label="2.0" value="2.0" />
                </el-select>
                <div class="field-hint">数据库API规范版本</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="ODBC版本" prop="odbcversion">
                <el-select v-model="pyodbcConfig.odbcversion" placeholder="请选择ODBC版本" >
                  <el-option label="3.0" value="3.0" />
                  <el-option label="3.5" value="3.5" />
                  <el-option label="3.8" value="3.8" />
                </el-select>
                <div class="field-hint">使用的ODBC标准版本</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 数据处理配置 -->
        <div class="config-section">
          <div class="section-title">数据处理选项</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="列名小写">
                <el-switch v-model="pyodbcConfig.lowercase"  />
                <div class="field-hint">是否将结果集列名转换为小写</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="原生UUID支持">
                <el-switch v-model="pyodbcConfig.native_uuid"  />
                <div class="field-hint">是否返回原生SQL_GUID类型</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 高级配置 -->
        <div class="config-section">
          <div class="section-title">高级选项</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="参数风格" prop="paramstyle">
                <el-select v-model="pyodbcConfig.paramstyle" placeholder="请选择参数风格" >
                  <el-option label="format" value="format" />
                  <el-option label="pyformat" value="pyformat" />
                  <el-option label="numeric" value="numeric" />
                  <el-option label="named" value="named" />
                  <el-option label="qmark" value="qmark" />
                </el-select>
                <div class="field-hint">SQL参数占位符风格</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最大字符串长度" prop="maxwrite">
                <el-input-number 
                  v-model="pyodbcConfig.maxwrite" 
                  :min="0" 
                  :precision="0"
                  controls-position="right"
                  placeholder="最大字符串长度"
                  
                />
                <div class="field-hint">单次写入的最大字符串长度</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="Unicode 错误处理" prop="unicode_errors">
                <el-select v-model="pyodbcConfig.unicode_errors" placeholder="请选择错误处理方式" >
                  <el-option label="strict" value="strict" />
                  <el-option label="ignore" value="ignore" />
                  <el-option label="replace" value="replace" />
                  <el-option label="xmlcharrefreplace" value="xmlcharrefreplace" />
                </el-select>
                <div class="field-hint">Unicode编码错误的处理方式</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- PYODBC信息展示 -->
        <div class="info-section">
          <el-alert
            title="PYODBC 配置说明"
            type="info"
            :closable="false"
            show-icon
          >
            <div class="help-content">
              <p><strong>连接池:</strong> 启用后可提高频繁连接的性能</p>
              <p><strong>线程安全:</strong> 根据应用的并发需求选择合适的级别</p>
              <p><strong>列名小写:</strong> 统一列名格式，便于代码处理</p>
              <p><strong>原生UUID:</strong> 启用后可正确处理GUID类型数据</p>
              <p><strong>参数风格:</strong> 推荐使用 'qmark' 或 'format' 风格</p>
            </div>
          </el-alert>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      pooling: false
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()

// PYODBC配置数据
const pyodbcConfig = reactive({
  pooling: false,
  version: '',
  apilevel: '2.0',
  threadsafety: 1,
  lowercase: false,
  native_uuid: false,
  odbcversion: '3.8',
  paramstyle: 'qmark',
  maxwrite: 0,
  unicode_errors: 'strict'
})

// 表单验证规则
const rules = reactive({
  version: [
    { pattern: /^\d+\.\d+(\.\d+)?$/, message: '请输入正确的版本格式 (如: 4.0.35)', trigger: 'blur' }
  ],
  maxwrite: [
    { type: 'number', min: 0, message: '最大写入长度不能为负数', trigger: 'blur' }
  ]
})

// 处理配置变化
const handleChange = () => {
  emit('update:modelValue', { ...pyodbcConfig })
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(pyodbcConfig, {
      pooling: false,
      version: '',
      apilevel: '2.0',
      threadsafety: 1,
      lowercase: false,
      native_uuid: false,
      odbcversion: '3.8',
      paramstyle: 'qmark',
      maxwrite: 0,
      unicode_errors: 'strict',
      ...newValue
    })
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(pyodbcConfig, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })
</script>

<style lang="scss" scoped>
.odbc-pyodbc-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .config-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f8f9fa;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .info-section {
    margin-top: 24px;
    
    .help-content {
      line-height: 1.6;
      
      p {
        margin: 8px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-select) {
    width: 100%;
  }
}
</style> 