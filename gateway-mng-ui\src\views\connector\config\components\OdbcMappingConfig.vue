<template>
  <div class="odbc-mapping-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>数据映射配置</span>
          <el-tooltip content="配置SQL查询结果到IoTCloud设备数据的映射规则" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="mappingConfig" :rules="rules" label-width="140px" ref="formRef">
        <!-- 设备信息映射 -->
        <div class="config-section">
          <div class="section-title">设备信息映射</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备类型" prop="device.type" required>
                <el-input 
                  v-model="mappingConfig.device.type" 
                  placeholder="postgres"
                  
                />
                <div class="field-hint">设备在IoTCloud中的类型标识</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备名称表达式" prop="device.name" required>
                <el-input 
                  v-model="mappingConfig.device.name" 
                  placeholder="'ODBC ' + entity_id"
                  
                />
                <div class="field-hint">生成设备名称的Python表达式</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 数据处理选项 -->
        <div class="config-section">
          <div class="section-title">数据处理选项</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="仅变化时发送">
                <el-switch v-model="mappingConfig.sendDataOnlyOnChange"  />
                <div class="field-hint">是否仅在数据变化时发送到IoTCloud</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 属性映射 -->
        <div class="config-section">
          <div class="section-title">属性映射</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="属性映射规则" prop="attributes">
                <el-input 
                  v-model="mappingConfig.attributes" 
                  placeholder="*"
                  
                />
                <div class="field-hint">属性映射规则，使用 * 表示映射所有列，或指定具体列名</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- 属性映射说明 -->
          <div class="mapping-help">
            <el-alert
              title="属性映射规则说明"
              type="info"
              :closable="false"
              show-icon
            >
              <div class="help-content">
                <p><strong>通配符 *:</strong> 映射查询结果的所有列为设备属性</p>
                <p><strong>列名列表:</strong> 使用逗号分隔指定具体的列名，如: "column1,column2,column3"</p>
                <p><strong>表达式:</strong> 支持Python表达式计算，如: "{'status': status, 'location': location}"</p>
              </div>
            </el-alert>
          </div>
        </div>

        <!-- 遥测数据映射 -->
        <div class="config-section">
          <div class="section-title">
            遥测数据映射
            <el-button type="primary" size="small" @click="addTimeseries">
              <el-icon><Plus /></el-icon>
              添加遥测映射
            </el-button>
          </div>
          
          <div v-if="mappingConfig.timeseries.length === 0" class="empty-state">
            <el-empty description="暂无遥测数据映射" :image-size="60">
              <el-button type="primary" @click="addTimeseries">添加第一个遥测映射</el-button>
            </el-empty>
          </div>
          
          <div v-else class="timeseries-list">
            <el-card 
              v-for="(timeseries, index) in mappingConfig.timeseries" 
              :key="index" 
              class="timeseries-card"
              shadow="hover"
            >
              <template #header>
                <div class="timeseries-header">
                  <div class="timeseries-info">
                    <span class="timeseries-name">{{ timeseries.name || '未命名遥测' }}</span>
                    <el-tag size="small" type="warning">遥测数据</el-tag>
                  </div>
                  <div class="timeseries-actions">
                    <el-button type="primary" size="small" link @click="editTimeseries(timeseries, index)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button type="danger" size="small" link @click="deleteTimeseries(index)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </template>
              
              <div class="timeseries-summary">
                <el-row :gutter="16">
                  <el-col :span="12">
                    <div class="summary-item">
                      <span class="label">键名:</span>
                      <span class="value">{{ timeseries.name || '-' }}</span>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="summary-item">
                      <span class="label">值表达式:</span>
                      <span class="value">{{ timeseries.value || '-' }}</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </div>
        </div>
      </el-form>
    </el-card>

    <!-- 遥测数据映射对话框 -->
    <OdbcTimeseriesDialog
      v-model="dialogVisible"
      :timeseries="currentTimeseries"
      :is-edit="isEdit"
      @save="handleSaveTimeseries"
      @cancel="handleCancelTimeseries"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { QuestionFilled, Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import OdbcTimeseriesDialog from './OdbcTimeseriesDialog.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      device: {
        type: 'postgres',
        name: "'ODBC ' + entity_id"
      },
      sendDataOnlyOnChange: false,
      attributes: '*',
      timeseries: []
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const dialogVisible = ref(false)
const currentTimeseries = ref(null)
const isEdit = ref(false)
const currentIndex = ref(-1)

// 映射配置数据
const mappingConfig = reactive({
  device: {
    type: 'postgres',
    name: "'ODBC ' + entity_id"
  },
  sendDataOnlyOnChange: false,
  attributes: '*',
  timeseries: []
})

// 表单验证规则
const rules = reactive({
  'device.type': [
    { required: true, message: '请输入设备类型', trigger: 'blur' }
  ],
  'device.name': [
    { required: true, message: '请输入设备名称表达式', trigger: 'blur' }
  ],
  attributes: [
    { required: true, message: '请输入属性映射规则', trigger: 'blur' }
  ]
})

// 添加遥测映射
const addTimeseries = () => {
  currentTimeseries.value = null
  isEdit.value = false
  currentIndex.value = -1
  dialogVisible.value = true
}

// 编辑遥测映射
const editTimeseries = (timeseries, index) => {
  currentTimeseries.value = { ...timeseries }
  isEdit.value = true
  currentIndex.value = index
  dialogVisible.value = true
}

// 删除遥测映射
const deleteTimeseries = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个遥测映射吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    mappingConfig.timeseries.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存遥测映射
const handleSaveTimeseries = (timeseriesData) => {
  if (isEdit.value && currentIndex.value >= 0) {
    // 编辑模式
    mappingConfig.timeseries.splice(currentIndex.value, 1, timeseriesData)
    ElMessage.success('遥测映射更新成功')
  } else {
    // 添加模式
    mappingConfig.timeseries.push(timeseriesData)
    ElMessage.success('遥测映射添加成功')
  }
  
  dialogVisible.value = false
  handleChange()
}

// 取消操作
const handleCancelTimeseries = () => {
  dialogVisible.value = false
}

// 处理配置变化
const handleChange = () => {
  emit('update:modelValue', { ...mappingConfig })
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(mappingConfig, {
      device: {
        type: 'postgres',
        name: "'ODBC ' + entity_id"
      },
      sendDataOnlyOnChange: false,
      attributes: '*',
      timeseries: [],
      ...newValue
    })
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(mappingConfig, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })
</script>

<style lang="scss" scoped>
.odbc-mapping-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .config-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f8f9fa;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .mapping-help {
    margin-top: 16px;
    
    .help-content {
      line-height: 1.6;
      
      p {
        margin: 8px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .timeseries-list {
    .timeseries-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .timeseries-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .timeseries-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .timeseries-name {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .timeseries-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .timeseries-summary {
        margin-top: 16px;
        
        .summary-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .label {
            font-size: 12px;
            color: #909399;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
            word-break: break-all;
          }
        }
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }
}
</style> 