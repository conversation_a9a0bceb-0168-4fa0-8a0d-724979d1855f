<template>
  <div class="knx-client-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>KNX客户端配置</span>
          <el-tooltip content="配置KNX网关连接参数、地址格式和网关扫描设置" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="clientConfig" :rules="rules" label-width="140px" ref="formRef">
        <!-- 连接配置 -->
        <div class="connection-section">
          <div class="section-title">连接配置</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="连接类型" prop="type" required>
                <el-select v-model="clientConfig.type" placeholder="请选择连接类型" >
                  <el-option label="自动连接" value="AUTOMATIC" />
                  <el-option label="手动连接" value="MANUAL" />
                  <el-option label="隧道连接" value="TUNNEL" />
                </el-select>
                <div class="field-hint">KNX网关的连接模式</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地址格式" prop="addressFormat" required>
                <el-select v-model="clientConfig.addressFormat" placeholder="请选择地址格式" >
                  <el-option label="长格式 (x/y/z)" value="LONG" />
                  <el-option label="短格式 (xyz)" value="SHORT" />
                </el-select>
                <div class="field-hint">KNX群组地址的格式</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="个体地址" prop="individualAddress">
                <el-input 
                  v-model="clientConfig.individualAddress" 
                  placeholder="1.0.10"
                  
                />
                <div class="field-hint">KNX设备的个体地址</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 本地配置 -->
        <div class="local-section">
          <div class="section-title">本地接口配置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="本地IP地址" prop="localIp">
                <el-input 
                  v-model="clientConfig.localIp" 
                  placeholder="0.0.0.0"
                  
                />
                <div class="field-hint">本地网络接口IP地址，0.0.0.0表示自动选择</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="本地端口" prop="localPort">
                <el-input-number 
                  v-model="clientConfig.localPort" 
                  :min="1" 
                  :max="65535"
                  :precision="0"
                  controls-position="right"
                  
                />
                <div class="field-hint">本地监听端口，默认3671</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 网关配置 -->
        <div class="gateway-section">
          <div class="section-title">KNX网关配置</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="网关IP地址" prop="gatewayIP" required>
                <el-input 
                  v-model="clientConfig.gatewayIP" 
                  placeholder="127.0.0.1"
                  
                />
                <div class="field-hint">KNX网关的IP地址</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="网关端口" prop="gatewayPort" required>
                <el-input-number 
                  v-model="clientConfig.gatewayPort" 
                  :min="1" 
                  :max="65535"
                  :precision="0"
                  controls-position="right"
                  
                />
                <div class="field-hint">KNX网关端口，默认3671</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="速率限制" prop="rateLimit">
                <el-input-number 
                  v-model="clientConfig.rateLimit" 
                  :min="0" 
                  :step="10"
                  controls-position="right"
                  
                />
                <span class="unit-suffix">ms</span>
                <div class="field-hint">请求间隔限制，0表示无限制</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 重连配置 -->
        <div class="reconnect-section">
          <div class="section-title">重连配置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="自动重连">
                <el-switch 
                  v-model="clientConfig.autoReconnect" 
                  
                />
                <div class="field-hint">连接断开时是否自动重连</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重连等待时间" v-if="clientConfig.autoReconnect">
                <el-input-number 
                  v-model="clientConfig.autoReconnectWait" 
                  :min="1" 
                  :max="60"
                  :precision="0"
                  controls-position="right"
                  
                />
                <span class="unit-suffix">秒</span>
                <div class="field-hint">自动重连的等待时间</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 网关扫描配置 -->
        <div class="scanner-section">
          <div class="section-title">网关扫描器配置</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="启用扫描器">
                <el-switch 
                  v-model="clientConfig.gatewaysScanner.enabled" 
                  
                />
                <div class="field-hint">启用自动扫描KNX网关</div>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="clientConfig.gatewaysScanner.enabled">
              <el-form-item label="扫描周期">
                <el-input-number 
                  v-model="clientConfig.gatewaysScanner.scanPeriod" 
                  :min="1" 
                  :max="60"
                  :precision="0"
                  controls-position="right"
                  
                />
                <span class="unit-suffix">秒</span>
                <div class="field-hint">网关扫描的时间间隔</div>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="clientConfig.gatewaysScanner.enabled">
              <el-form-item label="找到后停止">
                <el-switch 
                  v-model="clientConfig.gatewaysScanner.stopOnFound" 
                  
                />
                <div class="field-hint">找到第一个网关后停止扫描</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 连接测试 -->
        <div class="test-section">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-button 
                type="primary" 
                :loading="testing" 
                @click="testConnection"
                :disabled="!isConfigValid"
              >
                <el-icon><Connection /></el-icon>
                测试连接
              </el-button>
              <span class="test-hint">点击测试KNX网关连接是否正常</span>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { QuestionFilled, Connection } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      type: 'AUTOMATIC',
      addressFormat: 'LONG',
      localIp: '0.0.0.0',
      localPort: 3671,
      gatewayIP: '127.0.0.1',
      gatewayPort: 3671,
      individualAddress: '1.0.10',
      rateLimit: 0,
      autoReconnect: true,
      autoReconnectWait: 3,
      gatewaysScanner: {
        enabled: true,
        scanPeriod: 3,
        stopOnFound: false
      }
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const testing = ref(false)

// 客户端配置数据
const clientConfig = reactive({
  type: 'AUTOMATIC',
  addressFormat: 'LONG',
  localIp: '0.0.0.0',
  localPort: 3671,
  gatewayIP: '127.0.0.1',
  gatewayPort: 3671,
  individualAddress: '1.0.10',
  rateLimit: 0,
  autoReconnect: true,
  autoReconnectWait: 3,
  gatewaysScanner: {
    enabled: true,
    scanPeriod: 3,
    stopOnFound: false
  }
})

// 表单验证规则
const rules = reactive({
  type: [
    { required: true, message: '请选择连接类型', trigger: 'change' }
  ],
  addressFormat: [
    { required: true, message: '请选择地址格式', trigger: 'change' }
  ],
  gatewayIP: [
    { required: true, message: '请输入网关IP地址', trigger: 'blur' },
    { 
      pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      message: '请输入有效的IP地址',
      trigger: 'blur'
    }
  ],
  gatewayPort: [
    { required: true, message: '请输入网关端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号范围：1-65535', trigger: 'blur' }
  ],
  individualAddress: [
    { 
      pattern: /^\d+\.\d+\.\d+$/,
      message: '个体地址格式：x.y.z',
      trigger: 'blur'
    }
  ]
})

// 检查配置是否有效
const isConfigValid = computed(() => {
  return !!(clientConfig.gatewayIP && clientConfig.gatewayPort)
})

// 测试连接
const testConnection = async () => {
  try {
    await formRef.value?.validate()
    testing.value = true
    
    // 模拟连接测试
    setTimeout(() => {
      testing.value = false
      ElMessage.success('KNX网关连接测试成功！')
    }, 2000)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查配置参数')
  }
}

// 处理配置变化
const handleChange = () => {
  emit('update:modelValue', { ...clientConfig })
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(clientConfig, {
      type: 'AUTOMATIC',
      addressFormat: 'LONG',
      localIp: '0.0.0.0',
      localPort: 3671,
      gatewayIP: '127.0.0.1',
      gatewayPort: 3671,
      individualAddress: '1.0.10',
      rateLimit: 0,
      autoReconnect: true,
      autoReconnectWait: 3,
      gatewaysScanner: {
        enabled: true,
        scanPeriod: 3,
        stopOnFound: false
      },
      ...newValue
    })
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(clientConfig, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })
</script>

<style lang="scss" scoped>
.knx-client-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .unit-suffix {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
  }

  .connection-section,
  .local-section,
  .gateway-section,
  .reconnect-section,
  .scanner-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f8f9fa;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
    }
  }

  .test-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f0f9ff;
    
    .test-hint {
      margin-left: 12px;
      font-size: 12px;
      color: #909399;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-select) {
    width: 100%;
  }
}
</style> 