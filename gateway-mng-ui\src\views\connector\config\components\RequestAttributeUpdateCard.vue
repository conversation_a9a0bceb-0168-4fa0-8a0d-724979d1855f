<template>
  <el-card class="attribute-update-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="card-title">
          <span>属性更新配置 {{ index + 1 }}</span>
          <el-tag :type="getMethodTagType(updateData.httpMethod)" size="small">
            {{ updateData.httpMethod || 'POST' }}
          </el-tag>
        </div>
        <el-button type="danger" size="small" @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>
    
    <el-form :model="updateData" label-width="120px">
      <!-- 基础请求配置 -->
      <div class="section">
        <div class="section-title">请求配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="HTTP方法" required>
              <el-select v-model="updateData.httpMethod" @change="updateValue">
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="PATCH" value="PATCH" />
                <el-option label="GET" value="GET" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="超时(秒)">
              <el-input-number 
                v-model="updateData.timeout" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重试次数">
              <el-input-number 
                v-model="updateData.tries" 
                :min="0" 
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="允许重定向">
              <el-switch 
                v-model="updateData.allowRedirects" 
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 过滤器配置 -->
      <div class="section">
        <div class="section-title">过滤器配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名筛选" required>
              <el-input 
                v-model="updateData.deviceNameFilter" 
                placeholder="SD.*"
                @input="updateValue"
              />
              <div class="field-hint">正则表达式，用于匹配设备名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="属性筛选" required>
              <el-input 
                v-model="updateData.attributeFilter" 
                placeholder="send_data"
                @input="updateValue"
              />
              <div class="field-hint">正则表达式，用于匹配属性名称</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- URL和值表达式配置 -->
      <div class="section">
        <div class="section-title">表达式配置</div>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请求URL表达式" required>
              <el-input 
                v-model="updateData.requestUrlExpression" 
                placeholder="sensor/${deviceName}/${attributeKey}"
                @input="updateValue"
              />
              <div class="field-hint">
                支持变量: ${deviceName}, ${attributeKey}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请求值表达式" required>
              <el-input 
                v-model="updateData.requestValueExpression" 
                type="textarea"
                :rows="3"
                placeholder='{"${attributeKey}":"${attributeValue}"}'
                @input="updateValue"
              />
              <div class="field-hint">
                支持变量: ${attributeKey}, ${attributeValue}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        <RequestHttpHeaders v-model="updateData.httpHeaders" @update:modelValue="updateValue" />
      </div>

      <!-- 表达式帮助 -->
      <div class="section">
        <el-collapse size="small">
          <el-collapse-item name="help" title="表达式变量说明">
            <div class="help-content">
              <div class="help-section">
                <h4>URL表达式变量</h4>
                <ul>
                  <li><code>${deviceName}</code> - 设备名称</li>
                  <li><code>${attributeKey}</code> - 属性键名</li>
                </ul>
              </div>
              <div class="help-section">
                <h4>值表达式变量</h4>
                <ul>
                  <li><code>${attributeKey}</code> - 属性键名</li>
                  <li><code>${attributeValue}</code> - 属性值</li>
                </ul>
              </div>
              <div class="help-section">
                <h4>示例配置</h4>
                <ul>
                  <li>URL: <code>api/device/${deviceName}/attribute/${attributeKey}</code></li>
                  <li>值: <code>{"key":"${attributeKey}","value":"${attributeValue}","timestamp":${ts}}</code></li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 配置预览 -->
      <div class="section" v-if="updateData.deviceNameFilter && updateData.attributeFilter">
        <div class="section-title">配置预览</div>
        <div class="preview-content">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="HTTP方法">
              <el-tag :type="getMethodTagType(updateData.httpMethod)" size="small">
                {{ updateData.httpMethod }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="超时设置">
              {{ updateData.timeout }}秒 (重试{{ updateData.tries }}次)
            </el-descriptions-item>
            <el-descriptions-item label="设备筛选">
              <code class="filter-code">{{ updateData.deviceNameFilter }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="属性筛选">
              <code class="filter-code">{{ updateData.attributeFilter }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="URL模板" span="2">
              <code class="expression-code">{{ updateData.requestUrlExpression }}</code>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import RequestHttpHeaders from './RequestHttpHeaders.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

// 属性更新数据
const updateData = computed({
  get: () => ({
    httpMethod: 'POST',
    httpHeaders: {
      'CONTENT-TYPE': 'application/json'
    },
    timeout: 0.5,
    tries: 3,
    allowRedirects: true,
    deviceNameFilter: '',
    attributeFilter: '',
    requestUrlExpression: '',
    requestValueExpression: '',
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 获取HTTP方法标签类型
const getMethodTagType = (method) => {
  const methodTagMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'PATCH': 'info',
    'DELETE': 'danger'
  }
  return methodTagMap[method] || 'primary'
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...updateData.value })
}

// 删除配置
const handleDelete = () => {
  emit('delete')
}
</script>

<style lang="scss" scoped>
.attribute-update-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .help-content {
    .help-section {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #606266;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 4px;
          font-size: 13px;
          color: #606266;
          
          code {
            background: #f5f7fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
            color: #e6a23c;
          }
        }
      }
    }
  }
  
  .preview-content {
    .filter-code,
    .expression-code {
      background: #f5f7fa;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
      color: #e6a23c;
      font-size: 12px;
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .el-collapse-item__content {
    padding: 12px 0 0 0;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style> 