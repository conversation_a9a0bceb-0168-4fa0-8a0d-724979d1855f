<template>
  <div class="serial-data-points">
    <el-tabs v-model="activeDataType" type="border-card">
      <el-tab-pane 
        v-for="(dataType, key) in dataTypes" 
        :key="key"
        :label="dataType.label" 
        :name="key">
        <div class="data-type-config">
          <div class="data-header">
            <div class="data-title">{{ dataType.label }}配置</div>
            <el-button type="primary" size="small" :icon="CirclePlus" @click="addDataItem(key)">
              添加{{ dataType.label }}
            </el-button>
          </div>

          <div v-if="dataValues[key].length === 0" class="empty-data">
            <el-empty :description="`暂无${dataType.label}配置`" :image-size="60">
              <el-button type="primary" size="small" @click="addDataItem(key)">
                添加{{ dataType.label }}
              </el-button>
            </el-empty>
          </div>

          <div v-else class="data-items">
            <div 
              v-for="(item, index) in dataValues[key]" 
              :key="index" 
              class="data-item">
              <div class="item-header">
                <span class="item-title">{{ dataType.label }} {{ index + 1 }}: {{ getItemDisplayName(item, key) }}</span>
                <el-button type="danger" size="small" :icon="Delete" @click="removeDataItem(key, index)">
                  删除
                </el-button>
              </div>
              
              <div class="item-content">
                <el-form :model="item" label-width="120px" size="small">
                  <!-- 遥测数据配置 -->
                  <div v-if="key === 'telemetry'">
                    <el-row :gutter="16">
                      <el-col :span="8">
                        <el-form-item label="数据键" required>
                          <el-input v-model="item.key" placeholder="数据键名" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="数据类型" required>
                          <el-select v-model="item.type" placeholder="选择数据类型">
                            <el-option label="字符串" value="string" />
                            <el-option label="整数" value="int" />
                            <el-option label="浮点数" value="float" />
                            <el-option label="布尔值" value="bool" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="结束分隔符">
                          <el-input v-model="item.untilDelimiter" placeholder="\\r\\n" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 属性数据配置 -->
                  <div v-if="key === 'attributes'">
                    <el-row :gutter="16">
                      <el-col :span="8">
                        <el-form-item label="属性键" required>
                          <el-input v-model="item.key" placeholder="属性键名" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="数据类型" required>
                          <el-select v-model="item.type" placeholder="选择数据类型">
                            <el-option label="字符串" value="string" />
                            <el-option label="整数" value="int" />
                            <el-option label="浮点数" value="float" />
                            <el-option label="布尔值" value="bool" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="16">
                      <el-col :span="8">
                        <el-form-item label="起始字节">
                          <el-input-number v-model="item.fromByte" :min="0" controls-position="right" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="结束字节">
                          <el-input-number v-model="item.toByte" :min="-1" controls-position="right" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 属性更新配置 -->
                  <div v-if="key === 'attributeUpdates'">
                    <el-row :gutter="16">
                      <el-col :span="12">
                        <el-form-item label="平台属性名" required>
                          <el-input v-model="item.attributeOnPlatform" placeholder="平台上的属性名" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="发送到设备的字符串" required>
                          <el-input v-model="item.stringToDevice" placeholder="value = ${attr1}\\n" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- RPC配置 -->
                  <div v-if="key === 'serverSideRpc'">
                    <el-row :gutter="16">
                      <el-col :span="8">
                        <el-form-item label="方法名" required>
                          <el-input v-model="item.method" placeholder="RPC方法名" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="参数类型">
                          <el-select v-model="item.type" placeholder="选择参数类型">
                            <el-option label="字符串" value="string" />
                            <el-option label="整数" value="int" />
                            <el-option label="浮点数" value="float" />
                            <el-option label="布尔值" value="bool" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="是否有响应">
                          <el-switch v-model="item.withResponse" />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    
                    <!-- 响应配置 -->
                    <div v-if="item.withResponse" class="response-config">
                      <el-row :gutter="16">
                        <el-col :span="8">
                          <el-form-item label="响应类型">
                            <el-select v-model="item.responseType" placeholder="响应数据类型">
                              <el-option label="字符串" value="string" />
                              <el-option label="整数" value="int" />
                              <el-option label="浮点数" value="float" />
                              <el-option label="布尔值" value="bool" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="响应分隔符">
                            <el-input v-model="item.responseUntilDelimiter" placeholder="\\r" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-form-item label="响应超时(秒)">
                            <el-input-number v-model="item.responseTimeoutSec" :min="1" :max="60" controls-position="right" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      telemetry: [],
      attributes: [],
      attributeUpdates: [],
      serverSideRpc: []
    })
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const activeDataType = ref('telemetry')

const dataValues = reactive({
  telemetry: [],
  attributes: [],
  attributeUpdates: [],
  serverSideRpc: [],
  ...props.modelValue
})

// 数据类型配置
const dataTypes = {
  telemetry: {
    label: '遥测数据',
    description: '从设备读取的时序数据'
  },
  attributes: {
    label: '属性数据',
    description: '设备静态属性信息'
  },
  attributeUpdates: {
    label: '属性更新',
    description: '向设备写入属性值'
  },
  serverSideRpc: {
    label: 'RPC方法',
    description: '远程过程调用方法'
  }
}

// 方法
const addDataItem = (dataType) => {
  const newItem = createDefaultDataItem(dataType)
  dataValues[dataType].push(newItem)
  ElMessage.success(`已添加${dataTypes[dataType].label}`)
}

const removeDataItem = async (dataType, index) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这个${dataTypes[dataType].label}吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 删除数据项
    dataValues[dataType].splice(index, 1)
    
    // 调试信息
    console.log(`已删除串口${dataTypes[dataType].label}，当前数量:`, dataValues[dataType].length)
    
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const createDefaultDataItem = (dataType) => {
  const timestamp = Date.now()
  
  switch (dataType) {
    case 'telemetry':
      return {
        key: `telemetry_${timestamp}`,
        type: 'float',
        untilDelimiter: '\\r'
      }
    case 'attributes':
      return {
        key: `attribute_${timestamp}`,
        type: 'string',
        fromByte: 0,
        toByte: -1
      }
    case 'attributeUpdates':
      return {
        attributeOnPlatform: `attr_${timestamp}`,
        stringToDevice: 'value = ${attr}\\n'
      }
    case 'serverSideRpc':
      return {
        method: `rpcMethod_${timestamp}`,
        type: 'string',
        withResponse: false,
        responseType: 'string',
        responseUntilDelimiter: '\\r',
        responseTimeoutSec: 5
      }
    default:
      return {}
  }
}

const getItemDisplayName = (item, dataType) => {
  switch (dataType) {
    case 'telemetry':
    case 'attributes':
      return item.key || '未命名'
    case 'attributeUpdates':
      return item.attributeOnPlatform || '未命名'
    case 'serverSideRpc':
      return item.method || '未命名'
    default:
      return '未命名'
  }
}

// 监听配置变化
watch(dataValues, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(dataValues, newValue)
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
.serial-data-points {
  .data-type-config {
    .data-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px;
      background: #f0f2f5;
      border-radius: 6px;

      .data-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }
    }

    .empty-data {
      text-align: center;
      padding: 30px 20px;
    }

    .data-items {
      .data-item {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        margin-bottom: 12px;
        overflow: hidden;

        .item-header {
          background: #fafafa;
          padding: 8px 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #e4e7ed;

          .item-title {
            font-size: 13px;
            font-weight: 500;
            color: #303133;
          }
        }

        .item-content {
          padding: 12px;

          .response-config {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #e4e7ed;
          }
        }
      }
    }
  }

  :deep(.el-tabs__header) {
    margin-bottom: 16px;
  }

  :deep(.el-tabs__item) {
    font-size: 13px;
    padding: 8px 16px;
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-form-item__label) {
    font-size: 12px;
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input), :deep(.el-select) {
    width: 100%;
  }
}
</style> 