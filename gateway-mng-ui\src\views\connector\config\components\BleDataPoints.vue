<template>
  <div class="ble-data-points">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <span class="title">{{ title }}</span>
            <span class="description">{{ description }}</span>
          </div>
          <el-button type="primary" size="small" @click="handleAddDataPoint">
            <el-icon><Plus /></el-icon>
            添加{{ getDataTypeName() }}
          </el-button>
        </div>
      </template>
      
      <!-- 数据点列表 -->
      <div v-if="dataPoints.length === 0" class="empty-state">
        <el-empty :description="`暂无${getDataTypeName()}配置`">
          <el-button type="primary" @click="handleAddDataPoint">添加第一个{{ getDataTypeName() }}</el-button>
        </el-empty>
      </div>
      
      <div v-else class="data-points-list">
        <div 
          v-for="(dataPoint, index) in dataPoints" 
          :key="index" 
          class="data-point-item"
        >
          <el-card shadow="hover">
            <template #header>
              <div class="data-point-header">
                <div class="data-point-info">
                  <h4 class="data-point-name">{{ getDataPointDisplayName(dataPoint) }}</h4>
                  <span class="data-point-method">{{ getMethodDisplay(dataPoint.method || dataPoint.methodProcessing) }}</span>
                </div>
                <div class="data-point-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    link 
                    @click="handleEditDataPoint(dataPoint, index)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    link 
                    @click="handleDeleteDataPoint(index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="data-point-details">
              <div v-if="dataType !== 'attributeUpdates'" class="detail-item">
                <span class="label">特征UUID:</span>
                <span class="value uuid">{{ dataPoint.characteristicUUID || '未设置' }}</span>
              </div>
              <div v-if="dataType === 'telemetry' || dataType === 'attributes'" class="detail-item">
                <span class="label">值表达式:</span>
                <span class="value">{{ dataPoint.valueExpression || '未设置' }}</span>
              </div>
              <div v-if="dataType === 'attributeUpdates'" class="detail-item">
                <span class="label">平台属性:</span>
                <span class="value">{{ dataPoint.attributeOnThingsBoard || '未设置' }}</span>
              </div>
              <div v-if="dataType === 'serverSideRpc'" class="detail-item">
                <span class="label">是否响应:</span>
                <span class="value">{{ dataPoint.withResponse ? '是' : '否' }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 数据点配置对话框 -->
    <BleDataPointDialog
      v-model="dialogVisible"
      :data-point="currentDataPoint"
      :data-type="dataType"
      :is-edit="isEdit"
      @save="handleSaveDataPoint"
      @cancel="handleCancelDataPoint"
    />
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BleDataPointDialog from './BleDataPointDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['telemetry', 'attributes', 'attributeUpdates', 'serverSideRpc'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const dataPoints = ref([])
const dialogVisible = ref(false)
const currentDataPoint = ref(null)
const currentIndex = ref(-1)
const isEdit = ref(false)

// BLE处理方法映射
const methodMap = {
  notify: '通知',
  read: '读取',
  write: '写入',
  scan: '扫描'
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  dataPoints.value = [...(newValue || [])]
}, { deep: true, immediate: true })

// 监听数据点列表变化
watch(dataPoints, (newDataPoints) => {
  emit('update:modelValue', [...newDataPoints])
}, { deep: true })

// 获取数据类型名称
const getDataTypeName = () => {
  const typeNames = {
    telemetry: '遥测数据',
    attributes: '属性数据',
    attributeUpdates: '属性更新',
    serverSideRpc: 'RPC方法'
  }
  return typeNames[props.dataType] || '数据点'
}

// 获取数据点显示名称
const getDataPointDisplayName = (dataPoint) => {
  if (props.dataType === 'serverSideRpc') {
    return dataPoint.methodRPC || 'Unknown Method'
  } else if (props.dataType === 'attributeUpdates') {
    return dataPoint.attributeOnThingsBoard || 'Unknown Attribute'
  }
  return dataPoint.key || 'Unknown Key'
}

// 获取方法显示名称
const getMethodDisplay = (method) => {
  return methodMap[method] || method || 'Unknown'
}

// 添加数据点
const handleAddDataPoint = () => {
  currentDataPoint.value = createDefaultDataPoint()
  currentIndex.value = -1
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑数据点
const handleEditDataPoint = (dataPoint, index) => {
  currentDataPoint.value = { ...dataPoint }
  currentIndex.value = index
  isEdit.value = true
  dialogVisible.value = true
}

// 删除数据点
const handleDeleteDataPoint = async (index) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这个${getDataTypeName()}吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    dataPoints.value.splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存数据点
const handleSaveDataPoint = (dataPointData) => {
  if (isEdit.value) {
    // 编辑模式
    dataPoints.value[currentIndex.value] = { ...dataPointData }
    ElMessage.success('数据点更新成功')
  } else {
    // 添加模式
    dataPoints.value.push({ ...dataPointData })
    ElMessage.success('数据点添加成功')
  }
  
  dialogVisible.value = false
  currentDataPoint.value = null
  currentIndex.value = -1
}

// 取消数据点配置
const handleCancelDataPoint = () => {
  dialogVisible.value = false
  currentDataPoint.value = null
  currentIndex.value = -1
}

// 创建默认数据点配置
const createDefaultDataPoint = () => {
  const timestamp = Date.now()
  
  switch (props.dataType) {
    case 'telemetry':
      return {
        key: `telemetry_${timestamp}`,
        method: 'notify',
        characteristicUUID: '',
        valueExpression: '[0]'
      }
    case 'attributes':
      return {
        key: `attribute_${timestamp}`,
        method: 'read',
        characteristicUUID: '',
        valueExpression: '[:]'
      }
    case 'attributeUpdates':
      return {
        attributeOnThingsBoard: `attr_${timestamp}`,
        characteristicUUID: ''
      }
    case 'serverSideRpc':
      return {
        methodRPC: `rpcMethod_${timestamp}`,
        withResponse: true,
        characteristicUUID: '',
        methodProcessing: 'read'
      }
    default:
      return {}
  }
}
</script>

<style lang="scss" scoped>
.ble-data-points {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-info {
        flex: 1;
        
        .title {
          font-weight: 600;
          color: #303133;
          font-size: 16px;
        }
        
        .description {
          display: block;
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
  
  .data-points-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 16px;
    
    .data-point-item {
      .data-point-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .data-point-info {
          flex: 1;
          
          .data-point-name {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            word-break: break-word;
          }
          
          .data-point-method {
            font-size: 11px;
            color: #409eff;
            background: #ecf5ff;
            padding: 2px 6px;
            border-radius: 10px;
          }
        }
        
        .data-point-actions {
          display: flex;
          gap: 6px;
          margin-left: 8px;
        }
      }
      
      .data-point-details {
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 6px 0;
          border-bottom: 1px solid #f5f7fa;
          
          &:last-child {
            border-bottom: none;
          }
          
          .label {
            font-size: 12px;
            color: #606266;
            font-weight: 500;
            min-width: 80px;
          }
          
          .value {
            font-size: 12px;
            color: #303133;
            font-weight: 500;
            flex: 1;
            text-align: right;
            word-break: break-word;
            
            &.uuid {
              font-family: monospace;
              font-size: 10px;
            }
          }
        }
      }
    }
  }
  
  :deep(.el-card) {
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
  
  :deep(.el-card__header) {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f2f5;
  }
  
  :deep(.el-card__body) {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .data-points-list {
    grid-template-columns: 1fr;
  }
}
</style> 