import request from '@/utils/request'

// 获取网络接口列表
export function getNetworkInterfaces() {
  return request({
    url: '/gateway/v1/network/interfaces',
    method: 'get'
  })
}

// 获取网络配置
export function getNetworkConfig(interfaceName) {
  return request({
    url: '/gateway/v1/network/config',
    method: 'get',
    params: { interface: interfaceName }
  })
}

// 设置网络配置
export function setNetworkConfig(data) {
  return request({
    url: '/gateway/v1/network/config',
    method: 'post',
    data
  })
}

// 获取网络配置状态
export function getNetworkStatus(taskId) {
  return request({
    url: '/gateway/v1/network/status',
    method: 'get',
    params: { task_id: taskId }
  })
}

// 扫描WiFi网络
export function scanWifiNetworks(interfaceName) {
  return request({
    url: '/gateway/v1/network/wifi/scan',
    method: 'post',
    data: { interface: interfaceName }
  })
}

// 连接WiFi网络
export function connectWifiNetwork(data) {
  return request({
    url: '/gateway/v1/network/wifi/connect',
    method: 'post',
    data
  })
}

// 获取WiFi连接状态
export function getWifiStatus(taskId) {
  return request({
    url: '/gateway/v1/network/wifi/status',
    method: 'get',
    params: { task_id: taskId }
  })
}

// 断开网络连接
export function disconnectNetworkConnection(ifaceName) {
  return request({
    url: '/gateway/v1/network/disconnect',
    method: 'post',
    params: { interface: ifaceName }
  })
}