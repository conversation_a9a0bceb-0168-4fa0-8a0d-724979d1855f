<template>
    <el-dialog
      v-model="dialogVisible"
      :title="getDialogTitle()"
      width="60%"
      :before-close="handleClose"
      destroy-on-close
    >
      <el-form :model="dataPointForm" :rules="rules" ref="formRef" label-width="120px">
        <!-- 属性数据配置 -->
        <template v-if="dataType === 'attributes'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="属性键名" prop="key" required>
                <el-input v-model="dataPointForm.key" placeholder="temp" />
                <div class="field-hint">在IoTCloud中显示的属性名称</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="值表达式" prop="value" required>
                <el-input v-model="dataPointForm.value" placeholder="[1:]" />
                <div class="field-hint">数据提取表达式，如[1:]表示从索引1开始的所有数据</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
  
        <!-- 遥测数据配置 -->
        <template v-if="dataType === 'timeseries'">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="数据类型" prop="type" required>
                <el-select v-model="dataPointForm.type" placeholder="请选择数据类型">
                  <el-option label="整数" value="int" />
                  <el-option label="浮点数" value="float" />
                  <el-option label="字符串" value="string" />
                  <el-option label="布尔值" value="bool" />
                  <el-option label="双精度" value="double" />
                  <el-option label="长整数" value="long" />
                </el-select>
                <div class="field-hint">遥测数据的数据类型</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="键名" prop="key" required>
                <el-input v-model="dataPointForm.key" placeholder="[0:1]" />
                <div class="field-hint">键名提取表达式或固定键名</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="值表达式" prop="value" required>
                <el-input v-model="dataPointForm.value" placeholder="[0:1]" />
                <div class="field-hint">数据值提取表达式</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
  
        <!-- 表达式语法帮助 -->
        <div class="expression-help">
          <el-alert
            title="表达式语法说明"
            type="info"
            :closable="false"
            show-icon
          >
            <div class="help-content">
              <p><strong>切片表达式:</strong></p>
              <ul>
                <li><code>[0:1]</code> - 获取索引0到1的数据（不包含1）</li>
                <li><code>[1:]</code> - 获取从索引1开始的所有数据</li>
                <li><code>[:2]</code> - 获取从开头到索引2的数据（不包含2）</li>
                <li><code>[0]</code> - 获取索引0的单个数据</li>
              </ul>
              <p><strong>数据类型:</strong> int(整数), float(浮点数), string(字符串), bool(布尔值), double(双精度), long(长整数)</p>
            </div>
          </el-alert>
        </div>
      </el-form>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saving">
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup>
  import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
  import { ElMessage } from 'element-plus'
  
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    dataPoint: {
      type: Object,
      default: null
    },
    dataType: {
      type: String,
      required: true,
      validator: (value) => ['attributes', 'timeseries'].includes(value)
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  })
  
  const emit = defineEmits(['update:modelValue', 'save', 'cancel'])
  
  const dialogVisible = ref(false)
  const formRef = ref()
  const saving = ref(false)
  
  // 数据点表单数据
  const dataPointForm = reactive({
    key: '',
    value: '',
    type: 'int'
  })
  
  // 表单验证规则
  const rules = computed(() => {
    const baseRules = {}
    
    if (props.dataType === 'attributes') {
      baseRules.key = [
        { required: true, message: '请输入属性键名', trigger: 'blur' }
      ]
      baseRules.value = [
        { required: true, message: '请输入值表达式', trigger: 'blur' }
      ]
    } else if (props.dataType === 'timeseries') {
      baseRules.type = [
        { required: true, message: '请选择数据类型', trigger: 'change' }
      ]
      baseRules.key = [
        { required: true, message: '请输入键名', trigger: 'blur' }
      ]
      baseRules.value = [
        { required: true, message: '请输入值表达式', trigger: 'blur' }
      ]
    }
    
    return baseRules
  })
  
  // 获取对话框标题
  const getDialogTitle = () => {
    const typeNames = {
      attributes: '属性数据',
      timeseries: '遥测数据'
    }
    const action = props.isEdit ? '编辑' : '添加'
    return `${action}${typeNames[props.dataType] || '数据点'}`
  }
  
  // 监听对话框显示状态
  watch(() => props.modelValue, (newValue) => {
    dialogVisible.value = newValue
    if (newValue && props.dataPoint) {
      Object.assign(dataPointForm, getDefaultFormData(), props.dataPoint)
    } else if (newValue) {
      Object.assign(dataPointForm, getDefaultFormData())
    }
  }, { immediate: true })
  
  // 监听对话框关闭
  watch(dialogVisible, (newValue) => {
    emit('update:modelValue', newValue)
  })
  
  // 获取默认表单数据
  const getDefaultFormData = () => {
    switch (props.dataType) {
      case 'attributes':
        return {
          key: 'temp',
          value: '[1:]'
        }
      case 'timeseries':
        return {
          type: 'int',
          key: '[0:1]',
          value: '[0:1]'
        }
      default:
        return {}
    }
  }
  
  // 处理保存
  const handleSave = async () => {
    try {
      await formRef.value?.validate()
      saving.value = true
      
      let saveData = {}
      if (props.dataType === 'attributes') {
        saveData = {
          key: dataPointForm.key,
          value: dataPointForm.value
        }
      } else if (props.dataType === 'timeseries') {
        saveData = {
          type: dataPointForm.type,
          key: dataPointForm.key,
          value: dataPointForm.value
        }
      }
      
      emit('save', saveData)
      ElMessage.success(props.isEdit ? '数据点更新成功' : '数据点添加成功')
    } catch (error) {
      console.error('表单验证失败:', error)
      ElMessage.error('请检查表单输入')
    } finally {
      saving.value = false
    }
  }
  
  // 处理关闭
  const handleClose = () => {
    emit('cancel')
  }
  </script>
  
  <style lang="scss" scoped>
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .expression-help {
    margin-top: 20px;
    
    .help-content {
      line-height: 1.6;
      
      p {
        margin: 8px 0;
      }
      
      ul {
        margin: 8px 0;
        padding-left: 20px;
        
        li {
          margin: 4px 0;
          
          code {
            background: #f1f2f3;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            color: #e6a23c;
          }
        }
      }
    }
  }
  
  .dialog-footer {
    text-align: right;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
  
  :deep(.el-select) {
    width: 100%;
  }
  </style>