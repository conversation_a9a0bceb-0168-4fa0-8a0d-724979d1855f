<template>
  <div class="rest-extension-config">
    <div class="config-section">
      <div class="section-title">自定义转换器配置</div>
      
      <el-form :model="extensionData" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="数据键名" required>
              <el-input 
                v-model="extensionData.key" 
                placeholder="Totaliser"
                @input="updateValue"
              />
              <div class="field-hint">数据在IoTCloud中的键名</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据类型" required>
              <el-select v-model="extensionData.datatype" @change="updateValue">
                <el-option label="浮点数 (float)" value="float" />
                <el-option label="整数 (int)" value="int" />
                <el-option label="双精度 (double)" value="double" />
                <el-option label="长整数 (long)" value="long" />
                <el-option label="字符串 (string)" value="string" />
                <el-option label="布尔值 (bool)" value="bool" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 字节解析配置 -->
        <div class="byte-config-section">
          <div class="subsection-title">字节解析配置</div>
          
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="起始字节">
                <el-input-number 
                  v-model="extensionData.fromByte" 
                  :min="0" 
                  :step="1"
                  controls-position="right"
                  style="width: 100%"
                  @change="updateValue"
                />
                <div class="field-hint">数据在字节流中的起始位置</div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结束字节">
                <el-input-number 
                  v-model="extensionData.toByte" 
                  :min="0" 
                  :step="1"
                  controls-position="right"
                  style="width: 100%"
                  @change="updateValue"
                />
                <div class="field-hint">数据在字节流中的结束位置</div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="字节序">
                <el-select v-model="extensionData.byteorder" @change="updateValue">
                  <el-option label="大端序 (big)" value="big" />
                  <el-option label="小端序 (little)" value="little" />
                </el-select>
                <div class="field-hint">字节数据的排列顺序</div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="有符号">
                <el-switch 
                  v-model="extensionData.signed" 
                  @change="updateValue"
                />
                <div class="field-hint">数据是否包含符号位</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 数据处理配置 -->
        <div class="processing-config-section">
          <div class="subsection-title">数据处理配置</div>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="乘数">
                <el-input-number 
                  v-model="extensionData.multiplier" 
                  :min="0.001"
                  :step="0.1"
                  :precision="3"
                  controls-position="right"
                  style="width: 100%"
                  @change="updateValue"
                />
                <div class="field-hint">对解析出的数值进行乘法运算</div>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="extensionData.datatype === 'string'">
              <el-form-item label="字符编码">
                <el-select v-model="extensionData.encoding" @change="updateValue">
                  <el-option label="UTF-8" value="utf-8" />
                  <el-option label="ASCII" value="ascii" />
                  <el-option label="Latin-1" value="latin-1" />
                  <el-option label="UTF-16" value="utf-16" />
                </el-select>
                <div class="field-hint">字符串数据的编码格式</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 位操作配置 -->
        <div class="bit-config-section">
          <div class="subsection-title">
            位操作配置
            <el-switch 
              v-model="bitOperationEnabled" 
              @change="toggleBitOperation"
              style="margin-left: 16px;"
            />
          </div>
          
          <div v-if="bitOperationEnabled">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="位偏移">
                  <el-input-number 
                    v-model="extensionData.bitOffset" 
                    :min="0" 
                    :max="7"
                    :step="1"
                    controls-position="right"
                    style="width: 100%"
                    @change="updateValue"
                  />
                  <div class="field-hint">位在字节中的偏移量</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="位长度">
                  <el-input-number 
                    v-model="extensionData.bitLength" 
                    :min="1" 
                    :max="8"
                    :step="1"
                    controls-position="right"
                    style="width: 100%"
                    @change="updateValue"
                  />
                  <div class="field-hint">要提取的位数长度</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="位掩码">
                  <el-input 
                    v-model="extensionData.bitMask" 
                    placeholder="0xFF"
                    @input="updateValue"
                  />
                  <div class="field-hint">位操作掩码(十六进制)</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 配置预览 -->
        <div class="config-preview">
          <div class="preview-title">配置预览</div>
          <div class="preview-content">
            <pre><code>{{ JSON.stringify(previewConfig, null, 2) }}</code></pre>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      key: 'value',
      datatype: 'float',
      fromByte: 0,
      toByte: 4,
      byteorder: 'big',
      signed: true,
      multiplier: 1
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const bitOperationEnabled = ref(false)

// 扩展配置数据
const extensionData = computed({
  get: () => ({
    key: 'value',
    datatype: 'float',
    fromByte: 0,
    toByte: 4,
    byteorder: 'big',
    signed: true,
    multiplier: 1,
    encoding: 'utf-8',
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 配置预览
const previewConfig = computed(() => {
  const config = { ...extensionData.value }
  
  // 移除未使用的字段
  if (config.datatype !== 'string') {
    delete config.encoding
  }
  
  if (!bitOperationEnabled.value) {
    delete config.bitOffset
    delete config.bitLength
    delete config.bitMask
  }
  
  return config
})

// 切换位操作
const toggleBitOperation = (enabled) => {
  const newData = { ...extensionData.value }
  
  if (enabled) {
    newData.bitOffset = 0
    newData.bitLength = 1
    newData.bitMask = '0xFF'
  } else {
    delete newData.bitOffset
    delete newData.bitLength
    delete newData.bitMask
  }
  
  emit('update:modelValue', newData)
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...extensionData.value })
}
</script>

<style lang="scss" scoped>
.rest-extension-config {
  .config-section {
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 20px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  .byte-config-section,
  .processing-config-section,
  .bit-config-section {
    margin: 24px 0;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    
    .subsection-title {
      font-size: 13px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .config-preview {
    margin-top: 24px;
    padding: 16px;
    background: #fafbfc;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    
    .preview-title {
      font-size: 13px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 12px;
    }
    
    .preview-content {
      pre {
        margin: 0;
        padding: 12px;
        background: #f6f8fa;
        border: 1px solid #e1e4e8;
        border-radius: 4px;
        overflow-x: auto;
        
        code {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
          font-size: 12px;
          line-height: 1.4;
          color: #24292e;
        }
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}
</style> 