<template>
  <div class="gateway-other-config">
    <el-form :model="config" :rules="rules" ref="formRef" label-width="140px">
      <!-- 设备活跃检查 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>设备活跃检查</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用活跃检查">
              <el-switch 
                v-model="config.checkingDeviceActivity.checkDeviceInactivity"
                @change="handleChange"
              />
              <div class="field-hint">检查设备是否处于活跃状态</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="不活跃超时(秒)" prop="checkingDeviceActivity.inactivityTimeoutSeconds">
              <el-input-number 
                v-model="config.checkingDeviceActivity.inactivityTimeoutSeconds" 
                :min="60"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">设备被认为不活跃的超时时间</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查周期(秒)" prop="checkingDeviceActivity.inactivityCheckPeriodSeconds">
              <el-input-number 
                v-model="config.checkingDeviceActivity.inactivityCheckPeriodSeconds" 
                :min="10"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">检查设备活跃状态的时间间隔</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 性能参数 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>性能参数</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最大负载(字节)" prop="maxPayloadSizeBytes">
              <el-input-number 
                v-model="config.maxPayloadSizeBytes" 
                :min="1024"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">单个数据包的最大负载大小</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发送间隔(毫秒)" prop="minPackSendDelayMS">
              <el-input-number 
                v-model="config.minPackSendDelayMS" 
                :min="0"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">数据包发送的最小间隔时间</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最小包大小(字节)" prop="minPackSizeToSend">
              <el-input-number 
                v-model="config.minPackSizeToSend" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">触发发送的最小数据包大小</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 其他设置 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>其他设置</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="处理设备重命名">
              <el-switch 
                v-model="config.handleDeviceRenaming"
                @change="handleChange"
              />
              <div class="field-hint">是否处理设备重命名事件</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="消息QoS等级" prop="qos">
              <el-select 
                v-model="config.qos" 
                placeholder="请选择QoS等级"
                @change="handleChange"
              >
                <el-option label="0 - 最多一次" :value="0" />
                <el-option label="1 - 至少一次" :value="1" />
                <el-option label="2 - 恰好一次" :value="2" />
              </el-select>
              <div class="field-hint">MQTT消息的服务质量等级</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="连接器检查周期(秒)" prop="checkConnectorsConfigurationInSeconds">
              <el-input-number 
                v-model="config.checkConnectorsConfigurationInSeconds" 
                :min="10"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">检查连接器配置变化的时间间隔</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const config = ref({
  checkingDeviceActivity: {
    checkDeviceInactivity: true,
    inactivityTimeoutSeconds: 300,
    inactivityCheckPeriodSeconds: 10
  },
  maxPayloadSizeBytes: 8196,
  minPackSendDelayMS: 50,
  minPackSizeToSend: 500,
  checkConnectorsConfigurationInSeconds: 60,
  handleDeviceRenaming: true,
  qos: 1
})

const rules = {
  'checkingDeviceActivity.inactivityTimeoutSeconds': [
    { type: 'number', min: 60, message: '不活跃超时时间不能小于60秒', trigger: 'blur' }
  ],
  'checkingDeviceActivity.inactivityCheckPeriodSeconds': [
    { type: 'number', min: 10, message: '检查周期不能小于10秒', trigger: 'blur' }
  ],
  maxPayloadSizeBytes: [
    { type: 'number', min: 1024, message: '最大负载不能小于1024字节', trigger: 'blur' }
  ],
  minPackSendDelayMS: [
    { type: 'number', min: 0, message: '发送间隔不能小于0毫秒', trigger: 'blur' }
  ],
  minPackSizeToSend: [
    { type: 'number', min: 1, message: '最小包大小不能小于1字节', trigger: 'blur' }
  ],
  qos: [
    { required: true, message: '请选择QoS等级', trigger: 'change' }
  ],
  checkConnectorsConfigurationInSeconds: [
    { type: 'number', min: 10, message: '检查周期不能小于10秒', trigger: 'blur' }
  ]
}

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(config.value, newValue)
  }
}, { deep: true, immediate: true })

watch(config, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
  emit('change', { ...newConfig })
}, { deep: true })

const handleChange = () => {
  formRef.value?.validateField(Object.keys(rules))
}

defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.gateway-other-config {
  .config-card {
    margin-bottom: 24px;
    border: 1px solid #e4e7ed;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-header {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
  
  :deep(.el-card__body) {
    padding: 24px;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
}
</style> 