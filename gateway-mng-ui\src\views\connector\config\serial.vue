<template>
  <el-form :model="serialConfig" :rules="serialRules" ref="serialFormRef">
    <el-tabs v-model="activeTab" class="serial-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="serialConfig" 
        />
        
        <!-- Serial特有配置 -->
        <el-card class="config-card" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>串口连接器专用配置</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="上行队列大小" prop="uplinkQueueSize">
                <el-input-number 
                  v-model="serialConfig.uplinkQueueSize" 
                  :min="1000" 
                  :max="1000000"
                  :step="1000"
                  controls-position="right" 
                  style="width: 100%" />
                <div class="field-hint">设置上行数据队列的大小，影响数据缓冲能力</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>

      <!-- 设备配置标签页 -->
      <el-tab-pane label="设备配置" name="devices">
        <div class="config-section">
          <div class="section-header">
            <div class="section-title">串口设备管理</div>
            <el-button type="primary" :icon="CirclePlus" @click="addDevice">
              添加设备
            </el-button>
          </div>

          <div v-if="serialConfig.devices.length === 0" class="empty-devices">
            <el-empty description="暂无设备配置" :image-size="100">
              <el-button type="primary" @click="addDevice">添加第一个设备</el-button>
            </el-empty>
          </div>

          <div v-else class="devices-grid">
            <div 
              v-for="(device, index) in serialConfig.devices" 
              :key="index" 
              class="device-card">
              <div class="device-header">
                <div class="device-info">
                  <h4 class="device-name">{{ device.name || '未命名设备' }}</h4>
                  <span class="device-port">{{ device.port || '未设置端口' }}</span>
                  <span class="device-baudrate">{{ device.baudrate || 9600 }} bps</span>
                </div>
                <div class="device-actions">
                  <el-button type="primary" size="small" @click="editDevice(index)">
                    编辑
                  </el-button>
                  <el-button type="danger" size="small" @click="deleteDevice(index)">
                    删除
                  </el-button>
                </div>
              </div>
              <div class="device-stats">
                <div class="stat-item">
                  <span class="stat-label">遥测数据:</span>
                  <span class="stat-value">{{ device.telemetry?.length || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">属性数据:</span>
                  <span class="stat-value">{{ device.attributes?.length || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">属性更新:</span>
                  <span class="stat-value">{{ device.attributeUpdates?.length || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">RPC方法:</span>
                  <span class="stat-value">{{ device.serverSideRpc?.length || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 设备配置对话框 -->
    <el-dialog 
      v-model="deviceDialogVisible" 
      :title="deviceDialogTitle" 
      width="80%" 
      :close-on-click-modal="false">
      <SerialDeviceDialog 
        v-if="deviceDialogVisible"
        :device-config="currentDeviceConfig" 
        :is-edit="isEditMode"
        @save="handleDeviceSave" 
        @cancel="handleDeviceCancel" />
    </el-dialog>
  </el-form>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CirclePlus } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import SerialDeviceDialog from './components/SerialDeviceDialog.vue'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

// 路由
const route = useRoute()

// 响应式数据
const activeTab = ref('general')
const deviceDialogVisible = ref(false)
const isEditMode = ref(false)
const currentDeviceIndex = ref(-1)
const currentDeviceConfig = ref(null)
const serialFormRef = ref(null)

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  uplinkQueueSize: 100000,
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  devices: []
}

// Serial配置数据结构
const serialConfig = reactive({ ...defaultConfigStructure })

// 表单验证规则
const serialRules = reactive({
  logLevel: [
    { required: true, message: '请选择日志级别', trigger: 'change' }
  ],
  uplinkQueueSize: [
    { required: true, message: '请输入上行队列大小', trigger: 'blur' },
    { type: 'number', min: 1000, message: '队列大小不能小于1000', trigger: 'blur' }
  ]
})

// 计算属性
const deviceDialogTitle = computed(() => {
  return isEditMode.value ? '编辑串口设备' : '添加串口设备'
})

// 方法
const addDevice = () => {
  currentDeviceConfig.value = createDefaultDeviceConfig()
  isEditMode.value = false
  currentDeviceIndex.value = -1
  deviceDialogVisible.value = true
}

const editDevice = (index) => {
  currentDeviceConfig.value = { ...serialConfig.devices[index] }
  isEditMode.value = true
  currentDeviceIndex.value = index
  deviceDialogVisible.value = true
}

const deleteDevice = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个设备配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    serialConfig.devices.splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleDeviceSave = (deviceConfig) => {
  if (isEditMode.value) {
    serialConfig.devices[currentDeviceIndex.value] = deviceConfig
    ElMessage.success('设备配置已更新')
  } else {
    serialConfig.devices.push(deviceConfig)
    ElMessage.success('设备配置已添加')
  }
  deviceDialogVisible.value = false
}

const handleDeviceCancel = () => {
  deviceDialogVisible.value = false
}

const createDefaultDeviceConfig = () => {
  return {
    name: `SerialDevice_${Date.now()}`,
    type: 'default',
    port: '/dev/ttyUSB0',
    baudrate: 9600,
    converter: 'SerialUplinkConverter',
    downlink_converter: 'SerialDownlinkConverter',
    telemetry: [],
    attributes: [],
    attributeUpdates: [],
    serverSideRpc: []
  }
}

// 生命周期
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(serialConfig, 'serial', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(serialConfig)
      
      console.log('Serial 连接器配置初始化成功')
    } else {
      ElMessage.warning('Serial 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('Serial 连接器初始化失败:', error)
    ElMessage.error('Serial 连接器初始化失败')
  }
})

// 暴露给父组件
defineExpose({ 
  serial: serialConfig,
  validate: () => serialFormRef.value?.validate()
})
</script>

<style lang="scss" scoped>
.serial-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
}

.config-section {
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.empty-devices {
  text-align: center;
  padding: 40px 20px;
}

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.device-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #409eff;
  }

  .device-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    .device-info {
      flex: 1;

      .device-name {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .device-port {
        display: inline-block;
        background: #f0f2f5;
        color: #606266;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        margin-right: 8px;
      }

      .device-baudrate {
        display: inline-block;
        background: #e7f3ff;
        color: #409eff;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }

    .device-actions {
      display: flex;
      gap: 8px;
    }
  }

  .device-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;

    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      font-size: 13px;

      .stat-label {
        color: #606266;
      }

      .stat-value {
        color: #409eff;
        font-weight: 500;
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}
</style> 