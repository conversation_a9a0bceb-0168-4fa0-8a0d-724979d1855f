<template>
  <div class="snmp-device-config">
    <el-form :model="deviceConfig" :rules="configRules" ref="configFormRef" label-width="140px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称" prop="deviceName" required>
                <el-input v-model="deviceConfig.deviceName" placeholder="SNMP Device" />
                <div class="field-hint">设备在IoTCloud中显示的名称</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备类型" prop="deviceType" required>
                <el-input v-model="deviceConfig.deviceType" placeholder="snmp" disabled />
                <div class="field-hint">SNMP设备类型标识</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="IP地址" prop="ip" required>
                <el-input v-model="deviceConfig.ip" placeholder="127.0.0.1" />
                <div class="field-hint">SNMP设备的IP地址</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="端口" prop="port" required>
                <el-input-number v-model="deviceConfig.port" :min="1" :max="65535" controls-position="right" style="width: 100%" />
                <div class="field-hint">SNMP服务端口，默认161</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="轮询周期(ms)" prop="pollPeriod" required>
                <el-input-number v-model="deviceConfig.pollPeriod" :min="1000" :step="1000" controls-position="right" style="width: 100%" />
                <div class="field-hint">数据轮询间隔时间</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="Community" prop="community" required>
                <el-input v-model="deviceConfig.community" placeholder="public" />
                <div class="field-hint">SNMP Community字符串</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自定义转换器" prop="converter">
                <el-input v-model="deviceConfig.converter" placeholder="CustomSNMPConverter" />
                <div class="field-hint">可选，自定义数据转换器</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <div class="config-section">
            <div class="section-header">
              <h4>属性数据配置</h4>
              <p class="section-description">配置从SNMP设备读取的属性数据</p>
              <el-button type="primary" @click="handleAddAttribute">
                <el-icon><Plus /></el-icon>添加属性
              </el-button>
            </div>
            
            <div v-if="deviceConfig.attributes.length === 0" class="empty-state">
              <el-empty description="暂无属性配置">
                <el-button type="primary" @click="handleAddAttribute">添加第一个属性</el-button>
              </el-empty>
            </div>
            
            <div v-else class="attribute-list">
              <el-card 
                v-for="(attr, index) in deviceConfig.attributes" 
                :key="index" 
                class="attribute-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span class="card-title">属性 {{ index + 1 }}: {{ attr.key || '未命名' }}</span>
                    <el-button type="danger" text @click="handleRemoveAttribute(index)">
                      <el-icon><Delete /></el-icon>删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item :label="'属性名称'" :prop="'attributes.' + index + '.key'" required>
                      <el-input v-model="attr.key" placeholder="属性名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="'请求方法'" :prop="'attributes.' + index + '.method'" required>
                      <el-select v-model="attr.method" @change="handleMethodChange(attr)">
                        <el-option label="GET" value="get" />
                        <el-option label="GET NEXT" value="getnext" />
                        <el-option label="MULTI GET" value="multiget" />
                        <el-option label="WALK" value="walk" />
                        <el-option label="BULK WALK" value="bulkwalk" />
                        <el-option label="BULK GET" value="bulkget" />
                        <el-option label="TABLE" value="table" />
                        <el-option label="MULTI WALK" value="multiwalk" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="'超时(秒)'" :prop="'attributes.' + index + '.timeout'">
                      <el-input-number v-model="attr.timeout" :min="1" :max="60" controls-position="right" style="width: 100%" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- OID配置 -->
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item :label="'OID配置'" :prop="'attributes.' + index + '.oid'" required>
                      <template v-if="['get', 'getnext', 'walk', 'table'].includes(attr.method)">
                        <el-input v-model="attr.oid" placeholder="*******.*******.0" />
                      </template>
                      <template v-else-if="['multiget', 'multiwalk', 'bulkwalk'].includes(attr.method)">
                        <div class="oid-list">
                          <div v-for="(oid, oidIndex) in attr.oid" :key="oidIndex" class="oid-item">
                            <el-input v-model="attr.oid[oidIndex]" placeholder="*******.*******.0" />
                            <el-button type="danger" circle @click="removeOid(attr.oid, oidIndex)">
                              <el-icon><Minus /></el-icon>
                            </el-button>
                          </div>
                          <el-button type="primary" text @click="addOid(attr.oid)">
                            <el-icon><Plus /></el-icon>添加OID
                          </el-button>
                        </div>
                      </template>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- BULK GET 特殊配置 -->
                <template v-if="attr.method === 'bulkget'">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="Scalar OIDs" :prop="'attributes.' + index + '.scalarOid'">
                        <div class="oid-list">
                          <div v-for="(oid, oidIndex) in attr.scalarOid" :key="oidIndex" class="oid-item">
                            <el-input v-model="attr.scalarOid[oidIndex]" placeholder="*******.*******.0" />
                            <el-button type="danger" circle @click="removeOid(attr.scalarOid, oidIndex)">
                              <el-icon><Minus /></el-icon>
                            </el-button>
                          </div>
                          <el-button type="primary" text @click="addOid(attr.scalarOid)">
                            <el-icon><Plus /></el-icon>添加Scalar OID
                          </el-button>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="Repeating OIDs" :prop="'attributes.' + index + '.repeatingOid'">
                        <div class="oid-list">
                          <div v-for="(oid, oidIndex) in attr.repeatingOid" :key="oidIndex" class="oid-item">
                            <el-input v-model="attr.repeatingOid[oidIndex]" placeholder="*******.*******.0" />
                            <el-button type="danger" circle @click="removeOid(attr.repeatingOid, oidIndex)">
                              <el-icon><Minus /></el-icon>
                            </el-button>
                          </div>
                          <el-button type="primary" text @click="addOid(attr.repeatingOid)">
                            <el-icon><Plus /></el-icon>添加Repeating OID
                          </el-button>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="最大列表大小" :prop="'attributes.' + index + '.maxListSize'">
                        <el-input-number v-model="attr.maxListSize" :min="1" controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>

                <!-- Community 覆盖 (for walk method) -->
                <el-row :gutter="20" v-if="attr.method === 'walk'">
                  <el-col :span="12">
                    <el-form-item label="Community覆盖" :prop="'attributes.' + index + '.community'">
                      <el-input v-model="attr.community" placeholder="private" />
                      <div class="field-hint">可选，覆盖设备默认Community</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="telemetry">
          <div class="config-section">
            <div class="section-header">
              <h4>遥测数据配置</h4>
              <p class="section-description">配置从SNMP设备读取的遥测数据</p>
              <el-button type="primary" @click="handleAddTelemetry">
                <el-icon><Plus /></el-icon>添加遥测
              </el-button>
            </div>
            
            <div v-if="deviceConfig.telemetry.length === 0" class="empty-state">
              <el-empty description="暂无遥测配置">
                <el-button type="primary" @click="handleAddTelemetry">添加第一个遥测</el-button>
              </el-empty>
            </div>
            
            <div v-else class="telemetry-list">
              <el-card 
                v-for="(tele, index) in deviceConfig.telemetry" 
                :key="index" 
                class="telemetry-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span class="card-title">遥测 {{ index + 1 }}: {{ tele.key || '未命名' }}</span>
                    <el-button type="danger" text @click="handleRemoveTelemetry(index)">
                      <el-icon><Delete /></el-icon>删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item :label="'遥测名称'" :prop="'telemetry.' + index + '.key'" required>
                      <el-input v-model="tele.key" placeholder="遥测名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="'请求方法'" :prop="'telemetry.' + index + '.method'" required>
                      <el-select v-model="tele.method">
                        <el-option label="WALK" value="walk" />
                        <el-option label="TABLE" value="table" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="'OID'" :prop="'telemetry.' + index + '.oid'" required>
                      <el-input v-model="tele.oid" placeholder="*******.*******.0" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- Community 覆盖 (for walk method) -->
                <el-row :gutter="20" v-if="tele.method === 'walk'">
                  <el-col :span="12">
                    <el-form-item label="Community覆盖" :prop="'telemetry.' + index + '.community'">
                      <el-input v-model="tele.community" placeholder="private" />
                      <div class="field-hint">可选，覆盖设备默认Community</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 属性更新配置 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <div class="config-section">
            <div class="section-header">
              <h4>属性更新配置</h4>
              <p class="section-description">配置向SNMP设备写入属性的操作</p>
              <el-button type="primary" @click="handleAddAttributeUpdate">
                <el-icon><Plus /></el-icon>添加属性更新
              </el-button>
            </div>
            
            <div v-if="deviceConfig.attributeUpdateRequests.length === 0" class="empty-state">
              <el-empty description="暂无属性更新配置">
                <el-button type="primary" @click="handleAddAttributeUpdate">添加第一个属性更新</el-button>
              </el-empty>
            </div>
            
            <div v-else class="attribute-update-list">
              <el-card 
                v-for="(update, index) in deviceConfig.attributeUpdateRequests" 
                :key="index" 
                class="attribute-update-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span class="card-title">属性更新 {{ index + 1 }}: {{ update.attributeFilter || '未命名' }}</span>
                    <el-button type="danger" text @click="handleRemoveAttributeUpdate(index)">
                      <el-icon><Delete /></el-icon>删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item :label="'属性过滤器'" :prop="'attributeUpdateRequests.' + index + '.attributeFilter'" required>
                      <el-input v-model="update.attributeFilter" placeholder="dataToSet" />
                      <div class="field-hint">IoTCloud平台属性名称</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="'请求方法'" :prop="'attributeUpdateRequests.' + index + '.method'" required>
                      <el-select v-model="update.method" @change="handleUpdateMethodChange(update)">
                        <el-option label="SET" value="set" />
                        <el-option label="MULTI SET" value="multiset" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="update.method === 'set'">
                    <el-form-item :label="'OID'" :prop="'attributeUpdateRequests.' + index + '.oid'" required>
                      <el-input v-model="update.oid" placeholder="*******.*******.0" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- MULTI SET 映射配置 -->
                <template v-if="update.method === 'multiset'">
                  <el-form-item label="OID映射配置">
                    <div class="mappings-config">
                      <div v-for="(value, oid) in update.mappings" :key="oid" class="mapping-item">
                        <el-input v-model="update.mappings[oid]" :placeholder="'OID: ' + oid">
                          <template #prepend>{{ oid }}</template>
                        </el-input>
                        <el-button type="danger" circle @click="removeMappingItem(update.mappings, oid)">
                          <el-icon><Minus /></el-icon>
                        </el-button>
                      </div>
                      <el-button type="primary" text @click="addMappingItem(update)">
                        <el-icon><Plus /></el-icon>添加映射
                      </el-button>
                    </div>
                  </el-form-item>
                </template>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- RPC配置 -->
        <el-tab-pane label="RPC配置" name="rpc">
          <div class="config-section">
            <div class="section-header">
              <h4>RPC方法配置</h4>
              <p class="section-description">配置设备的远程过程调用方法</p>
              <el-button type="primary" @click="handleAddRpc">
                <el-icon><Plus /></el-icon>添加RPC
              </el-button>
            </div>
            
            <div v-if="deviceConfig.serverSideRpcRequests.length === 0" class="empty-state">
              <el-empty description="暂无RPC配置">
                <el-button type="primary" @click="handleAddRpc">添加第一个RPC</el-button>
              </el-empty>
            </div>
            
            <div v-else class="rpc-list">
              <el-card 
                v-for="(rpc, index) in deviceConfig.serverSideRpcRequests" 
                :key="index" 
                class="rpc-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span class="card-title">RPC {{ index + 1 }}: {{ rpc.requestFilter || '未命名' }}</span>
                    <el-button type="danger" text @click="handleRemoveRpc(index)">
                      <el-icon><Delete /></el-icon>删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item :label="'请求过滤器'" :prop="'serverSideRpcRequests.' + index + '.requestFilter'" required>
                      <el-input v-model="rpc.requestFilter" placeholder="setData" />
                      <div class="field-hint">RPC方法名称过滤器</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="'请求方法'" :prop="'serverSideRpcRequests.' + index + '.method'" required>
                      <el-select v-model="rpc.method" @change="handleRpcMethodChange(rpc)">
                        <el-option label="SET" value="set" />
                        <el-option label="MULTI SET" value="multiset" />
                        <el-option label="GET" value="get" />
                        <el-option label="BULK WALK" value="bulkwalk" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="rpc.method !== 'multiset'">
                    <el-form-item :label="'OID配置'" :prop="'serverSideRpcRequests.' + index + '.oid'" required>
                      <template v-if="rpc.method === 'bulkwalk'">
                        <div class="oid-list">
                          <div v-for="(oid, oidIndex) in rpc.oid" :key="oidIndex" class="oid-item">
                            <el-input v-model="rpc.oid[oidIndex]" placeholder="*******.*******.0" />
                            <el-button type="danger" circle @click="removeOid(rpc.oid, oidIndex)">
                              <el-icon><Minus /></el-icon>
                            </el-button>
                          </div>
                          <el-button type="primary" text @click="addOid(rpc.oid)">
                            <el-icon><Plus /></el-icon>添加OID
                          </el-button>
                        </div>
                      </template>
                      <template v-else>
                        <el-input v-model="rpc.oid" placeholder="*******.*******.0" />
                      </template>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Plus, Delete, Minus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 表单引用和活动标签页
const configFormRef = ref(null)
const activeTab = ref('basic')

// 配置数据
const deviceConfig = reactive({
  deviceName: 'SNMP Device',
  deviceType: 'snmp',
  ip: '127.0.0.1',
  port: 161,
  pollPeriod: 5000,
  community: 'public',
  converter: '',
  attributes: [],
  telemetry: [],
  attributeUpdateRequests: [],
  serverSideRpcRequests: [],
  ...props.modelValue
})

// 表单验证规则
const configRules = {
  deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  ip: [{ required: true, message: '请输入IP地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
  community: [{ required: true, message: '请输入Community字符串', trigger: 'blur' }],
  pollPeriod: [{ required: true, message: '请输入轮询周期', trigger: 'blur' }]
}

// 监听配置变化
watch(deviceConfig, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 初始化数据时处理props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    Object.assign(deviceConfig, newVal)
  }
}, { deep: true, immediate: true })

// 属性操作
const handleAddAttribute = () => {
  deviceConfig.attributes.push({
    key: '',
    method: 'get',
    oid: '',
    timeout: 6
  })
}

const handleRemoveAttribute = (index) => {
  deviceConfig.attributes.splice(index, 1)
}

const handleMethodChange = (attr) => {
  // 根据方法类型初始化OID结构
  if (['multiget', 'multiwalk', 'bulkwalk'].includes(attr.method)) {
    if (!Array.isArray(attr.oid)) {
      attr.oid = [attr.oid || '']
    }
  } else if (['get', 'getnext', 'walk', 'table'].includes(attr.method)) {
    if (Array.isArray(attr.oid)) {
      attr.oid = attr.oid[0] || ''
    }
  }
  
  // BULK GET 特殊处理
  if (attr.method === 'bulkget') {
    if (!attr.scalarOid) attr.scalarOid = ['']
    if (!attr.repeatingOid) attr.repeatingOid = ['']
    if (!attr.maxListSize) attr.maxListSize = 10
  }
}

// 遥测操作
const handleAddTelemetry = () => {
  deviceConfig.telemetry.push({
    key: '',
    method: 'walk',
    oid: ''
  })
}

const handleRemoveTelemetry = (index) => {
  deviceConfig.telemetry.splice(index, 1)
}

// 属性更新操作
const handleAddAttributeUpdate = () => {
  deviceConfig.attributeUpdateRequests.push({
    attributeFilter: '',
    method: 'set',
    oid: '',
    mappings: {}
  })
}

const handleRemoveAttributeUpdate = (index) => {
  deviceConfig.attributeUpdateRequests.splice(index, 1)
}

const handleUpdateMethodChange = (update) => {
  if (update.method === 'multiset') {
    if (!update.mappings) update.mappings = {}
  }
}

// RPC操作
const handleAddRpc = () => {
  deviceConfig.serverSideRpcRequests.push({
    requestFilter: '',
    method: 'get',
    oid: ''
  })
}

const handleRemoveRpc = (index) => {
  deviceConfig.serverSideRpcRequests.splice(index, 1)
}

const handleRpcMethodChange = (rpc) => {
  if (rpc.method === 'bulkwalk') {
    if (!Array.isArray(rpc.oid)) {
      rpc.oid = [rpc.oid || '']
    }
  } else if (['get', 'set'].includes(rpc.method)) {
    if (Array.isArray(rpc.oid)) {
      rpc.oid = rpc.oid[0] || ''
    }
  }
}

// OID操作
const addOid = (oidArray) => {
  oidArray.push('')
}

const removeOid = (oidArray, index) => {
  oidArray.splice(index, 1)
}

// 映射操作
const addMappingItem = (update) => {
  const oid = prompt('请输入OID:')
  if (oid) {
    if (!update.mappings) update.mappings = {}
    update.mappings[oid] = '${attribute}'
  }
}

const removeMappingItem = (mappings, oid) => {
  delete mappings[oid]
}
</script>

<style scoped>
.snmp-device-config {
  padding: 0;
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-section {
  .section-header {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .section-description {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
}

.attribute-card,
.telemetry-card,
.attribute-update-card,
.rpc-card {
  margin-bottom: 16px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-weight: 600;
      color: #303133;
    }
  }
}

.oid-list {
  .oid-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    
    .el-input {
      flex: 1;
    }
  }
}

.mappings-config {
  .mapping-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    
    .el-input {
      flex: 1;
    }
  }
}

:deep(.el-tabs__content) {
  padding: 20px 0;
}

:deep(.el-card__header) {
  padding: 12px 20px;
  background: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 