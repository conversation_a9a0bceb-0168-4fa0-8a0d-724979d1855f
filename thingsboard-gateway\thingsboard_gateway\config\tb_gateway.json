{"thingsboard": {"host": "s3.iotcloud.top", "port": 1883, "remoteShell": true, "remoteConfiguration": false, "latencyDebugMode": false, "statistics": {"enable": true, "statsSendPeriodInSeconds": 60, "customStatsSendPeriodInSeconds": 3600, "configuration": "statistics.json"}, "deviceFiltering": {"enable": false, "filterFile": "list.json"}, "maxPayloadSizeBytes": 8196, "minPackSendDelayMS": 50, "minPackSizeToSend": 500, "checkConnectorsConfigurationInSeconds": 60, "handleDeviceRenaming": true, "security": {"type": "accessToken", "accessToken": "LQLNzjMaDammzvj4yaDY"}, "qos": 1, "reportStrategy": {"type": "ON_RECEIVED"}, "checkingDeviceActivity": {"checkDeviceInactivity": true, "inactivityTimeoutSeconds": 300, "inactivityCheckPeriodSeconds": 10}, "rateLimits": "DEFAULT_TELEMETRY_RATE_LIMIT", "dpRateLimits": "DEFAULT_TELEMETRY_DP_RATE_LIMIT", "messagesRateLimits": "DEFAULT_MESSAGES_RATE_LIMIT", "deviceMessagesRateLimits": "DEFAULT_MESSAGES_RATE_LIMIT", "deviceRateLimits": "DEFAULT_TELEMETRY_RATE_LIMIT", "deviceDpRateLimits": "DEFAULT_TELEMETRY_DP_RATE_LIMIT"}, "storage": {"type": "memory", "read_records_count": 100, "max_records_count": 100000, "data_folder_path": "./data/", "max_file_count": 10, "max_read_records_count": 10, "max_records_per_file": 10000, "data_file_path": "./data/data.db", "messages_ttl_check_in_hours": 1, "messages_ttl_in_days": 7}, "grpc": {"enabled": false, "serverPort": 9595, "keepAliveTimeMs": 10001, "keepAliveTimeoutMs": 5000, "keepAlivePermitWithoutCalls": true, "maxPingsWithoutData": 0, "minTimeBetweenPingsMs": 10000, "minPingIntervalWithoutDataMs": 5000}, "connectors": []}