<template>
  <div class="ftp-data-points">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <div>
            <h4>{{ title }}</h4>
            <p class="description">{{ description }}</p>
          </div>
          <el-button type="primary" size="small" @click="addDataPoint">
            <el-icon><Plus /></el-icon>
            添加数据点
          </el-button>
        </div>
      </template>
      
      <div v-if="dataPoints.length === 0" class="empty-state">
        <el-empty description="暂无数据点配置">
          <el-button type="primary" @click="addDataPoint">添加第一个数据点</el-button>
        </el-empty>
      </div>
      
      <div v-else class="data-points-list">
        <el-card 
          v-for="(dataPoint, index) in dataPoints" 
          :key="index" 
          class="data-point-card"
          shadow="hover"
        >
          <template #header>
            <div class="data-point-header">
              <div class="data-point-info">
                <span class="data-point-key">{{ getDataPointKey(dataPoint) }}</span>
                <el-tag size="small" :type="getDataPointTagType()">{{ getDataPointTypeName() }}</el-tag>
              </div>
              <div class="data-point-actions">
                <el-button type="primary" size="small" link @click="editDataPoint(dataPoint, index)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" link @click="deleteDataPoint(index)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="data-point-summary">
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="summary-item">
                  <span class="label">键名:</span>
                  <span class="value">{{ dataPoint.key || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="summary-item">
                  <span class="label">值表达式:</span>
                  <span class="value">{{ dataPoint.value || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="12" v-if="dataType === 'timeseries'">
                <div class="summary-item">
                  <span class="label">数据类型:</span>
                  <span class="value">{{ dataPoint.type || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 数据点配置对话框 -->
    <FtpDataPointDialog
      v-model="dialogVisible"
      :data-point="currentDataPoint"
      :data-type="dataType"
      :is-edit="isEdit"
      @save="handleSaveDataPoint"
      @cancel="handleCancelDataPoint"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps, nextTick } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import FtpDataPointDialog from './FtpDataPointDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries'].includes(value)
  },
  title: {
    type: String,
    default: '数据点配置'
  },
  description: {
    type: String,
    default: '配置数据点的提取规则'
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const currentDataPoint = ref(null)
const isEdit = ref(false)
const currentIndex = ref(-1)

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 数据点配置数据
const dataPoints = reactive([])

// 获取数据点键名
const getDataPointKey = (dataPoint) => {
  return dataPoint.key || '未命名数据点'
}

// 获取数据点类型标签类型
const getDataPointTagType = () => {
  return props.dataType === 'attributes' ? 'success' : 'warning'
}

// 获取数据点类型名称
const getDataPointTypeName = () => {
  return props.dataType === 'attributes' ? '属性' : '遥测'
}

// 添加数据点
const addDataPoint = () => {
  currentDataPoint.value = null
  isEdit.value = false
  currentIndex.value = -1
  dialogVisible.value = true
}

// 编辑数据点
const editDataPoint = (dataPoint, index) => {
  currentDataPoint.value = { ...dataPoint }
  isEdit.value = true
  currentIndex.value = index
  dialogVisible.value = true
}

// 删除数据点
const deleteDataPoint = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个数据点配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    dataPoints.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存数据点
const handleSaveDataPoint = (dataPointData) => {
  if (isEdit.value && currentIndex.value >= 0) {
    // 编辑模式
    dataPoints.splice(currentIndex.value, 1, dataPointData)
    ElMessage.success('数据点更新成功')
  } else {
    // 添加模式
    dataPoints.push(dataPointData)
    ElMessage.success('数据点添加成功')
  }
  
  dialogVisible.value = false
  handleChange()
}

// 取消操作
const handleCancelDataPoint = () => {
  dialogVisible.value = false
}

// 处理配置变化
const handleChange = () => {
  if (!isInternalUpdate.value) {
    const points = dataPoints.map(point => ({ ...point }))
    emit('update:modelValue', points)
  }
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value && Array.isArray(newValue)) {
    isInternalUpdate.value = true
    dataPoints.splice(0, dataPoints.length, ...newValue)
    nextTick(() => {
      isInternalUpdate.value = false
    })
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(dataPoints, () => {
  handleChange()
}, { deep: true })
</script>

<style lang="scss" scoped>
.ftp-data-points {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .description {
        margin: 0;
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .data-points-list {
    .data-point-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .data-point-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .data-point-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .data-point-key {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .data-point-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .data-point-summary {
        margin-top: 16px;
        
        .summary-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .label {
            font-size: 12px;
            color: #909399;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
            word-break: break-all;
          }
        }
      }
    }
  }
}
</style> 