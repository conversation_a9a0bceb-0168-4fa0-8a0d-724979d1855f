<template>
  <div class="request-connector">
    <el-form :model="requestConfig" :rules="rules" ref="formRef" label-width="120px">
      <el-tabs v-model="activeTab" class="request-tabs">
        <!-- 通用配置标签页 -->
        <el-tab-pane label="通用配置" name="general">
          <CommonGeneralConfig 
            v-model="requestConfig" 
          />
        </el-tab-pane>

        <!-- 服务器配置标签页 -->
        <el-tab-pane label="服务器配置" name="server">
          <div class="config-section">
            <div class="section-title">连接配置</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="服务器地址" prop="host" required>
                  <el-input 
                    v-model="requestConfig.host" 
                    placeholder="http://127.0.0.1:5000"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="SSL验证">
                  <el-switch v-model="requestConfig.SSLVerify" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <div class="section-title">安全配置</div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="认证类型">
                  <el-select v-model="requestConfig.security.type">
                    <el-option label="基本认证" value="basic" />
                    <el-option label="匿名" value="anonymous" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="requestConfig.security.type === 'basic'">
                <el-form-item label="用户名" prop="security.username">
                  <el-input v-model="requestConfig.security.username" placeholder="username" />
                </el-form-item>
              </el-col>
              <el-col :span="8" v-if="requestConfig.security.type === 'basic'">
                <el-form-item label="密码" prop="security.password">
                  <el-input 
                    v-model="requestConfig.security.password" 
                    type="password" 
                    placeholder="password"
                    show-password 
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- 映射配置标签页 -->
        <el-tab-pane label="映射配置" name="mapping">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">数据映射配置</div>
              <el-button type="primary" :icon="Plus" @click="showMappingDialog()">
                添加映射配置
              </el-button>
            </div>
            
            <div v-if="requestConfig.mapping.length === 0" class="empty-state">
              <el-empty description="暂无映射配置">
                <el-button type="primary" @click="showMappingDialog()">添加第一个映射配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="mapping-cards">
              <el-card 
                v-for="(mapping, index) in requestConfig.mapping" 
                :key="index" 
                class="mapping-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <div class="card-title">
                      <span class="mapping-name">映射配置 {{ index + 1 }}</span>
                      <div class="mapping-tags">
                        <el-tag 
                          :type="mapping.httpMethod === 'GET' ? 'success' : 'info'" 
                          size="small"
                        >
                          {{ mapping.httpMethod || 'GET' }}
                        </el-tag>
                        <el-tag 
                          :type="mapping.converter?.type === 'json' ? '' : 'warning'" 
                          size="small"
                        >
                          {{ mapping.converter?.type === 'json' ? 'JSON转换器' : '自定义转换器' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="card-actions">
                      <el-button size="small" @click="editMapping(index, mapping)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button size="small" type="danger" @click="deleteMapping(index)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div class="mapping-summary">
                  <div class="summary-item">
                    <span class="label">URL路径:</span>
                    <span class="value">{{ mapping.url || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">设备名表达式:</span>
                    <span class="value">{{ mapping.converter?.deviceNameJsonExpression || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">扫描周期:</span>
                    <span class="value">{{ mapping.scanPeriod || 5 }}秒</span>
                  </div>
                  <div class="summary-item" v-if="mapping.converter?.type === 'json'">
                    <span class="label">数据点:</span>
                    <span class="value">
                      属性{{ mapping.converter?.attributes?.length || 0 }}个，
                      遥测{{ mapping.converter?.telemetry?.length || 0 }}个
                    </span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 属性更新配置标签页 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">属性更新配置</div>
              <el-button type="primary" :icon="Plus" @click="showAttributeUpdateDialog()">
                添加属性更新
              </el-button>
            </div>
            
            <div v-if="requestConfig.attributeUpdates.length === 0" class="empty-state">
              <el-empty description="暂无属性更新配置">
                <el-button type="primary" @click="showAttributeUpdateDialog()">添加第一个属性更新配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="attribute-update-cards">
              <el-card 
                v-for="(update, index) in requestConfig.attributeUpdates" 
                :key="index" 
                class="attribute-update-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <div class="card-title">
                      <span class="update-name">属性更新配置 {{ index + 1 }}</span>
                      <div class="update-tags">
                        <el-tag 
                          :type="update.httpMethod === 'POST' ? 'info' : 'warning'" 
                          size="small"
                        >
                          {{ update.httpMethod || 'POST' }}
                        </el-tag>
                        <el-tag size="small" type="info">
                          {{ update.tries || 3 }}次重试
                        </el-tag>
                      </div>
                    </div>
                    <div class="card-actions">
                      <el-button size="small" @click="editAttributeUpdate(index, update)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button size="small" type="danger" @click="deleteAttributeUpdate(index)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div class="update-summary">
                  <div class="summary-item">
                    <span class="label">设备筛选:</span>
                    <span class="value">{{ update.deviceNameFilter || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">属性筛选:</span>
                    <span class="value">{{ update.attributeFilter || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">URL表达式:</span>
                    <span class="value">{{ update.requestUrlExpression || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">超时设置:</span>
                    <span class="value">{{ update.timeout || 0.5 }}秒</span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- RPC配置标签页 -->
        <el-tab-pane label="RPC配置" name="rpc">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">服务端RPC配置</div>
              <el-button type="primary" :icon="Plus" @click="showServerRpcDialog()">
                添加RPC配置
              </el-button>
            </div>
            
            <div v-if="requestConfig.serverSideRpc.length === 0" class="empty-state">
              <el-empty description="暂无RPC配置">
                <el-button type="primary" @click="showServerRpcDialog()">添加第一个RPC配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="server-rpc-cards">
              <el-card 
                v-for="(rpc, index) in requestConfig.serverSideRpc" 
                :key="index" 
                class="server-rpc-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <div class="card-title">
                      <span class="rpc-name">RPC配置 {{ index + 1 }}</span>
                      <div class="rpc-tags">
                        <el-tag 
                          :type="rpc.httpMethod === 'GET' ? 'success' : 'info'" 
                          size="small"
                        >
                          {{ rpc.httpMethod || 'GET' }}
                        </el-tag>
                        <el-tag size="small" type="info">
                          {{ rpc.tries || 3 }}次重试
                        </el-tag>
                      </div>
                    </div>
                    <div class="card-actions">
                      <el-button size="small" @click="editServerRpc(index, rpc)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button size="small" type="danger" @click="deleteServerSideRpc(index)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div class="rpc-summary">
                  <div class="summary-item">
                    <span class="label">设备筛选:</span>
                    <span class="value">{{ rpc.deviceNameFilter || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">方法筛选:</span>
                    <span class="value">{{ rpc.methodFilter || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">URL表达式:</span>
                    <span class="value">{{ rpc.requestUrlExpression || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">响应超时:</span>
                    <span class="value">{{ rpc.responseTimeout || 1 }}秒</span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <!-- 映射配置对话框 -->
    <RequestMappingDialog
      v-model="mappingDialogVisible"
      :mapping="currentMapping"
      :is-edit="currentMappingIndex >= 0"
      @save="saveMappingConfig"
    />

    <!-- 属性更新配置对话框 -->
    <RequestAttributeUpdateDialog
      v-model="attributeUpdateDialogVisible"
      :attribute-update="currentAttributeUpdate"
      :is-edit="currentAttributeUpdateIndex >= 0"
      @save="saveAttributeUpdateConfig"
    />

    <!-- RPC配置对话框 -->
    <RequestServerRpcDialog
      v-model="serverRpcDialogVisible"
      :server-rpc="currentServerRpc"
      :is-edit="currentServerRpcIndex >= 0"
      @save="saveServerRpcConfig"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import { useRoute } from 'vue-router'
import RequestMappingDialog from './components/RequestMappingDialog.vue'
import RequestAttributeUpdateDialog from './components/RequestAttributeUpdateDialog.vue'
import RequestServerRpcDialog from './components/RequestServerRpcDialog.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const formRef = ref()
const activeTab = ref('general')

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  host: 'http://127.0.0.1:5000',
  SSLVerify: true,
  security: {
    type: 'basic',
    username: '',
    password: ''
  },
  mapping: [],
  attributeUpdates: [],
  serverSideRpc: []
}

// 请求连接器配置
const requestConfig = reactive({ ...defaultConfigStructure })

// 表单验证规则
const rules = reactive({
  host: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  'security.username': [
    { 
      required: true, 
      message: '请输入用户名', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (requestConfig.security.type === 'basic' && !value) {
          callback(new Error('请输入用户名'))
        } else {
          callback()
        }
      }
    }
  ],
  'security.password': [
    { 
      required: true, 
      message: '请输入密码', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (requestConfig.security.type === 'basic' && !value) {
          callback(new Error('请输入密码'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 对话框状态管理
const mappingDialogVisible = ref(false)
const attributeUpdateDialogVisible = ref(false)
const serverRpcDialogVisible = ref(false)

// 编辑状态管理
const currentMappingIndex = ref(-1)
const currentAttributeUpdateIndex = ref(-1)
const currentServerRpcIndex = ref(-1)
const currentMapping = ref(null)
const currentAttributeUpdate = ref(null)
const currentServerRpc = ref(null)

// 显示映射对话框
const showMappingDialog = (index = -1, mapping = null) => {
  currentMappingIndex.value = index
  currentMapping.value = mapping
  mappingDialogVisible.value = true
}

// 编辑映射配置
const editMapping = (index, mapping) => {
  showMappingDialog(index, mapping)
}

// 保存映射配置
const saveMappingConfig = (mappingData) => {
  try {
    if (currentMappingIndex.value >= 0) {
      // 编辑模式
      requestConfig.mapping[currentMappingIndex.value] = mappingData
      console.log('映射配置已更新:', mappingData)
    } else {
      // 新增模式
      requestConfig.mapping.push(mappingData)
      console.log('映射配置已添加:', mappingData)
    }
    mappingDialogVisible.value = false
  } catch (error) {
    console.error('保存映射配置失败:', error)
    ElMessage.error('保存映射配置失败')
  }
}

// 删除映射配置
const deleteMapping = (index) => {
  requestConfig.mapping.splice(index, 1)
  ElMessage.success('映射配置删除成功')
}

// 显示属性更新对话框
const showAttributeUpdateDialog = (index = -1, update = null) => {
  currentAttributeUpdateIndex.value = index
  currentAttributeUpdate.value = update
  attributeUpdateDialogVisible.value = true
}

// 编辑属性更新配置
const editAttributeUpdate = (index, update) => {
  showAttributeUpdateDialog(index, update)
}

// 保存属性更新配置
const saveAttributeUpdateConfig = (updateData) => {
  try {
    if (currentAttributeUpdateIndex.value >= 0) {
      // 编辑模式
      requestConfig.attributeUpdates[currentAttributeUpdateIndex.value] = updateData
      console.log('属性更新配置已更新:', updateData)
    } else {
      // 新增模式
      requestConfig.attributeUpdates.push(updateData)
      console.log('属性更新配置已添加:', updateData)
    }
    attributeUpdateDialogVisible.value = false
  } catch (error) {
    console.error('保存属性更新配置失败:', error)
    ElMessage.error('保存属性更新配置失败')
  }
}

// 删除属性更新配置
const deleteAttributeUpdate = (index) => {
  requestConfig.attributeUpdates.splice(index, 1)
  ElMessage.success('属性更新配置删除成功')
}

// 显示服务端RPC对话框
const showServerRpcDialog = (index = -1, rpc = null) => {
  currentServerRpcIndex.value = index
  currentServerRpc.value = rpc
  serverRpcDialogVisible.value = true
}

// 编辑服务端RPC配置
const editServerRpc = (index, rpc) => {
  showServerRpcDialog(index, rpc)
}

// 保存服务端RPC配置
const saveServerRpcConfig = (rpcData) => {
  try {
    if (currentServerRpcIndex.value >= 0) {
      // 编辑模式
      requestConfig.serverSideRpc[currentServerRpcIndex.value] = rpcData
      console.log('RPC配置已更新:', rpcData)
    } else {
      // 新增模式
      requestConfig.serverSideRpc.push(rpcData)
      console.log('RPC配置已添加:', rpcData)
    }
    serverRpcDialogVisible.value = false
  } catch (error) {
    console.error('保存RPC配置失败:', error)
    ElMessage.error('保存RPC配置失败')
  }
}

// 删除服务端RPC配置
const deleteServerSideRpc = (index) => {
  requestConfig.serverSideRpc.splice(index, 1)
  ElMessage.success('RPC配置删除成功')
}

// 组件挂载时加载配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(requestConfig, 'request', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(requestConfig)
        
      console.log('Request 连接器配置初始化成功')
    } else {
      ElMessage.warning('Request 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('Request 连接器初始化失败:', error)
    ElMessage.error('Request 连接器初始化失败')
  }
})

// 暴露给父组件
defineExpose({ 
  request: requestConfig,
  validate: () => formRef.value?.validate()
})
</script>

<style lang="scss" scoped>
.request-connector {
  .request-tabs {
    :deep(.el-tabs__content) {
      padding: 20px 0;
    }
  }

  .config-section {
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .mapping-cards,
  .attribute-update-cards,
  .server-rpc-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 16px;
    
    .mapping-card,
    .attribute-update-card,
    .server-rpc-card {
      border: 1px solid #e4e7ed;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .card-title {
          flex: 1;
          
          .mapping-name,
          .update-name,
          .rpc-name {
            font-weight: 600;
            color: #303133;
            display: block;
            margin-bottom: 8px;
          }
          
          .mapping-tags,
          .update-tags,
          .rpc-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
          }
        }
        
        .card-actions {
          display: flex;
          gap: 8px;
          flex-shrink: 0;
        }
      }
      
      .mapping-summary,
      .update-summary,
      .rpc-summary {
        .summary-item {
          display: flex;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .label {
            font-weight: 500;
            color: #606266;
            min-width: 100px;
            flex-shrink: 0;
          }
          
          .value {
            color: #303133;
            word-break: break-all;
            flex: 1;
          }
        }
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>