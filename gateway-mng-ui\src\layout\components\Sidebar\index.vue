<template>
  <div
    :style="{ backgroundColor: sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
    <logo :collapse="false" />
    <el-menu :default-active="activeMenu" :unique-opened="true" mode="vertical">
      <sidebar-item v-for="(route, index) in sidebarRouters" :key="route.path + index" :item="route"
        :base-path="route.path" />
    </el-menu>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import variables from '@/assets/styles/variables.module.scss'
import useSettingsStore from '@/store/modules/settings'
import SidebarItem from "./SidebarItem";
import Logo from "./Logo";

const route = useRoute()
const router = useRouter()
const settingsStore = useSettingsStore()

const sideTheme = computed(() => settingsStore.sideTheme);

const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
})
const sidebarRouters = ref([])
onMounted(() => {
  router.options.routes.forEach(item => {
    if (!item.hidden) {
      sidebarRouters.value.push(item)
    }
  })
  // console.log(sidebarRouters.value);
})
</script>

<style lang="scss" scoped>
:deep(.el-menu-item.is-active) {
  background: #0078FF !important;
  color: #fff;
}

:deep(.el-menu-item.is-active:hover) {
  background: #0078FF !important;
  color: #fff !important;
}

:deep(.el-menu-item:hover) {
  color: #0078FF !important;
  background: #fff !important;
}

:deep(.el-sub-menu__title:hover) {
  color: #0078FF !important;
  background: #fff !important;
}
</style>
