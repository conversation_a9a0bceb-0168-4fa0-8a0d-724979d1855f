<template>
  <el-form :model="modbusConfig" :rules="modbusRules" ref="modbusFormRef">
    <el-tabs v-model="activeTab" class="modbus-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="modbusConfig" 
        />
      </el-tab-pane>

      <!-- 主机连接配置标签页 -->
      <el-tab-pane label="主机连接" name="master">
        <div class="config-section">
          <div class="section-header">
            <div class="section-title">从机设备列表</div>
            <el-button type="primary" :icon="CirclePlus" @click="addSlave">
              添加从机设备
            </el-button>
          </div>
          
          <div v-if="modbusConfig.master.slaves.length === 0" class="empty-state">
            <el-empty description="暂无从机设备配置">
              <el-button type="primary" @click="addSlave">添加第一个从机设备</el-button>
            </el-empty>
          </div>

          <div v-for="(slave, index) in modbusConfig.master.slaves" :key="index" class="slave-config-card">
            <div class="card-header">
              <div class="card-title">
                <el-icon><Monitor /></el-icon>
                从机设备 {{ index + 1 }}: {{ slave.deviceName || '未命名设备' }}
              </div>
              <div class="card-actions">
                <el-button type="primary" size="small" @click="editSlave(index)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="deleteSlave(index)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
            
            <div class="card-content">
              <el-row :gutter="16">
            <el-col :span="8">
                  <div class="info-item">
                    <span class="label">连接类型:</span>
                    <span class="value">{{ getProtocolLabel(slave.type) }}</span>
                  </div>
            </el-col>
            <el-col :span="8">
                  <div class="info-item">
                    <span class="label">地址:</span>
                    <span class="value">{{ slave.type === 'serial' ? slave.serialPort : `${slave.host}:${slave.port}` }}</span>
                  </div>
            </el-col>
            <el-col :span="8">
                  <div class="info-item">
                    <span class="label">设备类型:</span>
                    <span class="value">{{ slave.deviceType || '默认' }}</span>
                  </div>
            </el-col>
          </el-row>
              <el-row :gutter="16">
                <el-col :span="8">
                  <div class="info-item">
                    <span class="label">单元ID:</span>
                    <span class="value">{{ slave.unitId }}</span>
        </div>
            </el-col>
            <el-col :span="8">
                  <div class="info-item">
                    <span class="label">轮询周期:</span>
                    <span class="value">{{ slave.pollPeriod }}ms</span>
                  </div>
            </el-col>
            <el-col :span="8">
                  <div class="info-item">
                    <span class="label">数据点数量:</span>
                    <span class="value">{{ getTotalDataPoints(slave) }}</span>
                  </div>
            </el-col>
          </el-row>
        </div>
          </div>
            </div>
      </el-tab-pane>

      <!-- 从机服务器配置标签页 -->
      <el-tab-pane label="从机服务器" name="slave">
        <div class="config-section">
          <div class="section-header">
            <div class="section-title">从机服务器配置</div>
            <el-switch v-model="slaveEnabled" @change="toggleSlaveConfig">
              <template #active-text>启用</template>
              <template #inactive-text>禁用</template>
            </el-switch>
              </div>

          <div v-if="!slaveEnabled" class="disabled-state">
            <el-alert
              title="从机服务器已禁用"
              description="启用从机服务器以允许其他设备连接到此网关"
              type="info"
              show-icon
              :closable="false">
            </el-alert>
                </div>

          <div v-else class="slave-server-config">
            <ModbusSlaveConfig v-model="modbusConfig.slave" />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 从机设备配置对话框 -->
    <el-dialog
      v-model="slaveDialogVisible"
      :title="slaveDialogTitle"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close>
      <ModbusSlaveDialog
        v-if="slaveDialogVisible"
        :slave-config="currentSlaveConfig"
        :is-edit="isEditMode"
        @save="handleSlaveSave"
        @cancel="handleSlaveCancel"
      />
    </el-dialog>
  </el-form>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { CirclePlus, Monitor, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import { useRoute } from 'vue-router'
import ModbusSlaveConfig from './components/ModbusSlaveConfig.vue'
import ModbusSlaveDialog from './components/ModbusSlaveDialog.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()

// 响应式数据
const activeTab = ref('general')
const slaveEnabled = ref(false)
const slaveDialogVisible = ref(false)
const isEditMode = ref(false)
const currentSlaveIndex = ref(-1)
const currentSlaveConfig = ref(null)
const modbusFormRef = ref(null)

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  master: {
    slaves: []
  },
  slave: {
    type: 'tcp',
    host: '0.0.0.0',
    port: 502,
    method: 'socket',
    baudrate: 19200,
    timeout: 3000,
    bytesize: 8,
    parity: 'N',
    stopbits: 1,
    strict: true,
    identity: {
      vendorName: 'IoTCloud',
      productCode: 'IoTCloud',
      productName: 'IoTCloud Gateway',
      modelName: 'IoTCloud Gateway',
      majorMinorRevision: '1.0'
    },
    values: {}
  }
}

// Modbus 配置数据
const modbusConfig = reactive({ ...defaultConfigStructure })

// 表单验证规则
const modbusRules = reactive({})

// 计算属性
const slaveDialogTitle = computed(() => {
  return isEditMode.value ? '编辑从机设备' : '添加从机设备'
})

// 协议类型标签映射
const protocolLabels = {
  tcp: 'TCP',
  udp: 'UDP',
  serial: '串口'
}

// 方法
const getProtocolLabel = (type) => {
  return protocolLabels[type] || type
}

const getTotalDataPoints = (slave) => {
  const attributes = slave.attributes?.length || 0
  const timeseries = slave.timeseries?.length || 0
  const attributeUpdates = slave.attributeUpdates?.length || 0
  const rpc = slave.rpc?.length || 0
  return attributes + timeseries + attributeUpdates + rpc
}

const addSlave = () => {
  currentSlaveConfig.value = createDefaultSlaveConfig()
  isEditMode.value = false
  currentSlaveIndex.value = -1
  slaveDialogVisible.value = true
}

const editSlave = (index) => {
  currentSlaveConfig.value = { ...modbusConfig.master.slaves[index] }
  isEditMode.value = true
  currentSlaveIndex.value = index
  slaveDialogVisible.value = true
}

const deleteSlave = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个从机设备配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
          }
    )
    modbusConfig.master.slaves.splice(index, 1)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleSlaveSave = (slaveConfig) => {
  console.log('接收到的从机设备配置:', {
    deviceName: slaveConfig.deviceName,
    attributes: slaveConfig.attributes?.length || 0,
    timeseries: slaveConfig.timeseries?.length || 0,
    attributeUpdates: slaveConfig.attributeUpdates?.length || 0,
    rpc: slaveConfig.rpc?.length || 0
  })
  
  if (isEditMode.value) {
    // 深拷贝配置以确保数据不会被意外修改
    modbusConfig.master.slaves[currentSlaveIndex.value] = { ...slaveConfig }
    console.log('已更新从机设备:', currentSlaveIndex.value)
    ElMessage.success('从机设备配置已更新')
  } else {
    modbusConfig.master.slaves.push({ ...slaveConfig })
    ElMessage.success('从机设备配置已添加')
  }
  slaveDialogVisible.value = false
}

const handleSlaveCancel = () => {
  slaveDialogVisible.value = false
}

const toggleSlaveConfig = (enabled) => {
  if (!enabled) {
    // 清空从机配置的某些字段
    modbusConfig.slave.values = {}
  }
}

const createDefaultSlaveConfig = () => {
  return {
    deviceName: `NewSlaveDevice_${modbusConfig.master.slaves.length + 1}`,
    deviceType: 'default',
    type: 'tcp',
    host: '127.0.0.1',
    port: 502,
    unitId: 1,
    pollPeriod: 1000,
    attributes: [],
    timeseries: [],
    attributeUpdates: [],
    rpc: []
  }
}

onMounted(async () => {
  try {
    const success = await initializeConnectorConfig(modbusConfig, 'modbus', route, defaultConfigStructure)
    if (success) {
      ensureConfigId(modbusConfig)
      slaveEnabled.value = !!modbusConfig.slave
      console.log('Modbus 连接器配置初始化成功')
    } else {
      ElMessage.warning('Modbus 连接器配置初始化失败')
    }
  } catch (error) {
    console.error('Modbus 连接器初始化失败:', error)
    ElMessage.error('Modbus 连接器初始化失败')
  }
})

// 暴露给父组件
defineExpose({
  modbus: modbusConfig,
})
</script>

<style lang="scss" scoped>
.modbus-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
}

.config-section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .section-title {
      margin-bottom: 0;
      border-bottom: none;
      padding-bottom: 0;
    }
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.slave-config-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .card-header {
    background: #f8f9fa;
    padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
    border-bottom: 1px solid #e4e7ed;

    .card-title {
      display: flex;
      align-items: center;
      font-weight: 600;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .card-actions {
      display: flex;
      gap: 8px;
    }
  }

  .card-content {
    padding: 20px;

    .info-item {
      margin-bottom: 12px;

      .label {
        color: #909399;
        font-size: 14px;
        margin-right: 8px;
      }

      .value {
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.disabled-state {
  padding: 20px;
}

.slave-server-config {
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}
</style>