<template>
  <div class="ocpp-charge-points-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <div>
            <span>充电桩配置</span>
            <p class="description">配置OCPP充电桩的识别规则和数据映射</p>
          </div>
          <el-button type="primary" @click="addChargePoint">
            <el-icon><Plus /></el-icon>
            添加充电桩
          </el-button>
        </div>
      </template>
      
      <div v-if="chargePoints.length === 0" class="empty-state">
        <el-empty description="暂无充电桩配置">
          <el-button type="primary" @click="addChargePoint">添加第一个充电桩</el-button>
        </el-empty>
      </div>
      
      <div v-else class="charge-points-list">
        <el-card 
          v-for="(chargePoint, index) in chargePoints" 
          :key="index" 
          class="charge-point-card"
          shadow="hover"
        >
          <template #header>
            <div class="charge-point-header">
              <div class="charge-point-info">
                <span class="charge-point-name">{{ chargePoint.idRegexpPattern || '未命名充电桩' }}</span>
                <el-tag size="small" type="info">充电桩</el-tag>
              </div>
              <div class="charge-point-actions">
                <el-button type="primary" size="small" link @click="editChargePoint(chargePoint, index)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" link @click="deleteChargePoint(index)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="charge-point-summary">
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">ID正则:</span>
                  <span class="value">{{ chargePoint.idRegexpPattern || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">设备名表达式:</span>
                  <span class="value">{{ chargePoint.deviceNameExpression || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">设备类型:</span>
                  <span class="value">{{ chargePoint.deviceTypeExpression || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">属性数:</span>
                  <span class="value">{{ (chargePoint.attributes || []).length }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">遥测数:</span>
                  <span class="value">{{ (chargePoint.timeseries || []).length }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">属性更新:</span>
                  <span class="value">{{ (chargePoint.attributeUpdates || []).length }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">RPC:</span>
                  <span class="value">{{ (chargePoint.serverSideRpc || []).length }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 充电桩配置对话框 -->
    <OcppChargePointDialog
      v-model="dialogVisible"
      :charge-point="currentChargePoint"
      :is-edit="isEdit"
      @save="handleSaveChargePoint"
      @cancel="handleCancelChargePoint"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import OcppChargePointDialog from './OcppChargePointDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const currentChargePoint = ref(null)
const isEdit = ref(false)
const currentIndex = ref(-1)

// 充电桩配置数据
const chargePoints = reactive([])

// 添加充电桩
const addChargePoint = () => {
  currentChargePoint.value = null
  isEdit.value = false
  currentIndex.value = -1
  dialogVisible.value = true
}

// 编辑充电桩
const editChargePoint = (chargePoint, index) => {
  currentChargePoint.value = { ...chargePoint }
  isEdit.value = true
  currentIndex.value = index
  dialogVisible.value = true
}

// 删除充电桩
const deleteChargePoint = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个充电桩配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    chargePoints.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存充电桩
const handleSaveChargePoint = (chargePointData) => {
  if (isEdit.value && currentIndex.value >= 0) {
    // 编辑模式
    chargePoints.splice(currentIndex.value, 1, chargePointData)
    ElMessage.success('充电桩更新成功')
  } else {
    // 添加模式
    chargePoints.push(chargePointData)
    ElMessage.success('充电桩添加成功')
  }
  
  dialogVisible.value = false
  handleChange()
}

// 取消操作
const handleCancelChargePoint = () => {
  dialogVisible.value = false
}

// 处理配置变化
const handleChange = () => {
  const points = chargePoints.map(point => ({ ...point }))
  emit('update:modelValue', points)
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (Array.isArray(newValue)) {
    chargePoints.splice(0, chargePoints.length, ...newValue)
  }
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
.ocpp-charge-points-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .description {
        margin: 4px 0 0 0;
        font-size: 12px;
        color: #909399;
        font-weight: normal;
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .charge-points-list {
    .charge-point-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .charge-point-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .charge-point-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .charge-point-name {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .charge-point-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .charge-point-summary {
        margin-top: 16px;
        
        .summary-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          margin-bottom: 12px;
          
          .label {
            font-size: 12px;
            color: #909399;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
            word-break: break-all;
          }
        }
      }
    }
  }
}
</style> 