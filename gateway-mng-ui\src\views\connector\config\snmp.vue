<template>
  <div class="snmp-config">
    <el-tabs v-model="activeTab" class="snmp-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- SNMP设备配置标签页 -->
      <el-tab-pane label="设备配置" name="devices">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>SNMP设备配置管理</span>
              <el-button type="primary" size="small" @click="handleAddDevice">
                <el-icon><Plus /></el-icon>添加设备
              </el-button>
  </div>
          </template>
          
          <!-- 设备列表 -->
          <div v-if="config.devices.length === 0" class="empty-state">
            <el-empty description="暂无设备配置">
              <el-button type="primary" @click="handleAddDevice">添加第一个设备</el-button>
            </el-empty>
  </div>

          <div v-else class="devices-grid">
            <div 
              v-for="(device, index) in config.devices" 
              :key="index" 
              class="device-card"
            >
              <el-card shadow="hover">
                <template #header>
                  <div class="device-header">
                    <div class="device-info">
                      <h4 class="device-name">{{ device.deviceName || 'Unknown Device' }}</h4>
                      <span class="device-type">{{ device.deviceType || 'snmp' }}</span>
                    </div>
                    <div class="device-actions">
                      <el-button 
                        type="primary" 
                        size="small" 
                        link 
                        @click="handleEditDevice(device, index)"
                      >
                        编辑
                      </el-button>
                      <el-button 
                        type="danger" 
                        size="small" 
                        link 
                        @click="handleDeleteDevice(index)"
                      >
                        删除
                      </el-button>
      </div>
        </div>
              </template>
                
                <div class="device-details">
                  <div class="detail-item">
                    <span class="label">IP地址:</span>
                    <span class="value">{{ device.ip || 'N/A' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">端口:</span>
                    <span class="value">{{ device.port || 161 }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Community:</span>
                    <span class="value">{{ device.community || 'public' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">轮询周期:</span>
                    <span class="value">{{ device.pollPeriod || 5000 }}ms</span>
      </div>
                  <div class="detail-item">
                    <span class="label">属性数量:</span>
                    <span class="value">{{ (device.attributes || []).length }}</span>
      </div>
                  <div class="detail-item">
                    <span class="label">遥测数量:</span>
                    <span class="value">{{ (device.telemetry || []).length }}</span>
        </div>
                  <div class="detail-item">
                    <span class="label">RPC数量:</span>
                    <span class="value">{{ (device.serverSideRpcRequests || []).length }}</span>
      </div>
                  <div class="detail-item">
                    <span class="label">属性更新数量:</span>
                    <span class="value">{{ (device.attributeUpdateRequests || []).length }}</span>
      </div>
        </div>
              </el-card>
      </div>
      </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- SNMP设备配置对话框 -->
    <el-dialog 
      v-model="deviceDialogVisible" 
      :title="editingDeviceIndex === -1 ? '添加SNMP设备' : '编辑SNMP设备'"
      width="85%"
      :before-close="handleCloseDialog"
      destroy-on-close
    >
      <SnmpDeviceConfig v-model="editingDevice" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelDevice">取消</el-button>
          <el-button type="primary" @click="handleSaveDevice" :loading="saving">
            {{ editingDeviceIndex === -1 ? '添加' : '更新' }}
          </el-button>
        </div>
    </template>
  </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import SnmpDeviceConfig from './components/SnmpDeviceConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')
const deviceDialogVisible = ref(false)
const editingDeviceIndex = ref(-1)
const editingDevice = ref({})
const saving = ref(false)

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  devices: []
}

// 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 表单验证规则
const configRules = reactive({
  logLevel: [
    { required: true, message: '请选择日志级别', trigger: 'change' }
  ]
})

// 切换报告策略
const toggleReportStrategy = (enabled) => {
  if (!enabled) {
    config.reportStrategy = {
      type: 'ON_RECEIVED',
      reportPeriod: 60000
    }
  }
}

// 设备操作
const handleAddDevice = () => {
  editingDeviceIndex.value = -1
  editingDevice.value = createDefaultDevice()
  deviceDialogVisible.value = true
}

const handleEditDevice = (device, index) => {
  editingDeviceIndex.value = index
  editingDevice.value = JSON.parse(JSON.stringify(device))
  deviceDialogVisible.value = true
}

const handleDeleteDevice = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个SNMP设备配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    config.devices.splice(index, 1)
    ElMessage.success('设备删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleSaveDevice = async () => {
  try {
    saving.value = true
    
    // 验证必填字段
    if (!editingDevice.value.deviceName) {
      ElMessage.error('请输入设备名称')
      return
    }
    if (!editingDevice.value.ip) {
      ElMessage.error('请输入IP地址')
      return
    }
    
    if (editingDeviceIndex.value === -1) {
      // 添加模式
      config.devices.push(JSON.parse(JSON.stringify(editingDevice.value)))
      ElMessage.success('设备添加成功')
    } else {
      // 编辑模式
      config.devices[editingDeviceIndex.value] = JSON.parse(JSON.stringify(editingDevice.value))
      ElMessage.success('设备更新成功')
    }
    
    deviceDialogVisible.value = false
    editingDevice.value = {}
    editingDeviceIndex.value = -1
  } catch (error) {
    console.error('保存设备失败:', error)
    ElMessage.error('保存设备失败')
  } finally {
    saving.value = false
  }
}

const handleCancelDevice = () => {
  deviceDialogVisible.value = false
  editingDevice.value = {}
  editingDeviceIndex.value = -1
}

const handleCloseDialog = () => {
  handleCancelDevice()
}

const createDefaultDevice = () => {
  return {
    deviceName: `SNMP_Device_${Date.now()}`,
    deviceType: 'snmp',
    ip: '',
    port: 161,
    community: 'public',
    pollPeriod: 5000,
    attributes: [],
    telemetry: [],
    serverSideRpcRequests: [],
    attributeUpdateRequests: []
  }
}

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'snmp', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
        
      console.log('SNMP 连接器配置初始化成功')
    } else {
      ElMessage.warning('SNMP 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('SNMP 连接器初始化失败:', error)
    ElMessage.error('SNMP 连接器初始化失败')
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  snmp: config
})
</script>

<style lang="scss" scoped>
.snmp-config {
  .snmp-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }

  .config-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
    color: #909399;
  }

  .devices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }

  .device-card {
    transition: box-shadow 0.3s;

    .device-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .device-info {
      .device-name {
        margin: 0;
        font-size: 16px;
        color: #303133;
      }

      .device-type {
        font-size: 12px;
        color: #909399;
        background-color: #f4f4f5;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }

    .device-actions {
      display: flex;
      gap: 10px;
    }

    .device-details {
      padding-top: 15px;

      .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 6px 0;
        font-size: 14px;
        border-bottom: 1px solid #e9e9eb;

        &:last-child {
          border-bottom: none;
        }

        .label {
          color: #606266;
        }

        .value {
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>