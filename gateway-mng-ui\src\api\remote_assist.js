import request from '@/utils/request'

const baseUrl = '/gateway/v1/remote_assist'

// 获取远程协助配置
export function getConfig() {
  return request({
    url: baseUrl + '/config',
    method: 'get'
  })
}

// 设置远程协助配置
export function setConfig(vkey) {
  return request({
    url: baseUrl + '/config',
    method: 'post',
    data: { vkey }
  })
}

// 启动远程协助
export function startAssist() {
  return request({
    url: baseUrl + '/start',
    method: 'post'
  })
}

// 停止远程协助
export function stopAssist() {
  return request({
    url: baseUrl + '/stop',
    method: 'post'
  })
}

// 获取远程协助状态
export function getStatus() {
  return request({
    url: baseUrl + '/status',
    method: 'get'
  })
}