<template>
  <el-card class="rest-server-rpc-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="card-title">
          <span>服务端RPC {{ index + 1 }}</span>
          <el-tag :type="getMethodTagType(rpcData.HTTPMethod)" size="small">
            {{ rpcData.HTTPMethod }}
          </el-tag>
          <el-tag :type="rpcData.responseTimeout ? 'success' : 'info'" size="small">
            {{ rpcData.responseTimeout ? '有响应' : '无响应' }}
          </el-tag>
        </div>
        <el-button type="danger" size="small" @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>
    
    <el-form :model="rpcData" label-width="120px">
      <!-- 过滤器配置 -->
      <div class="section">
        <div class="section-title">过滤器配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名过滤器" required>
              <el-input 
                v-model="rpcData.deviceNameFilter" 
                placeholder=".*"
                @input="updateValue"
              />
              <div class="field-hint">设备名的正则表达式过滤器</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方法过滤器" required>
              <el-input 
                v-model="rpcData.methodFilter" 
                placeholder="echo"
                @input="updateValue"
              />
              <div class="field-hint">RPC方法名的正则表达式过滤器</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP配置 -->
      <div class="section">
        <div class="section-title">HTTP配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="HTTP方法" required>
              <el-select v-model="rpcData.HTTPMethod" @change="updateValue">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="PATCH" value="PATCH" />
                <el-option label="DELETE" value="DELETE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="超时时间(秒)">
              <el-input-number 
                v-model="rpcData.timeout" 
                :min="0.1" 
                :max="300"
                :step="0.1"
                :precision="1"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
              <div class="field-hint">请求超时时间</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重试次数">
              <el-input-number 
                v-model="rpcData.tries" 
                :min="1" 
                :max="10"
                :step="1"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
              <div class="field-hint">请求失败时的重试次数</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 响应配置 -->
      <div class="section">
        <div class="section-title">
          响应配置
          <el-switch 
            v-model="responseEnabled" 
            @change="toggleResponse"
            style="margin-left: 16px;"
          />
        </div>
        
        <div v-if="responseEnabled">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="响应超时(秒)">
                <el-input-number 
                  v-model="rpcData.responseTimeout" 
                  :min="1" 
                  :max="300"
                  :step="1"
                  controls-position="right"
                  style="width: 100%"
                  @change="updateValue"
                />
                <div class="field-hint">等待响应的超时时间</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="响应值表达式">
                <el-input 
                  v-model="rpcData.responseValueExpression" 
                  placeholder="${response}"
                  @input="updateValue"
                />
                <div class="field-hint">从响应中提取值的表达式</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 安全配置 -->
      <div class="section">
        <div class="section-title">安全配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认证类型">
              <el-select v-model="rpcData.security.type" @change="handleSecurityTypeChange">
                <el-option label="匿名访问" value="anonymous" />
                <el-option label="基本认证" value="basic" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="rpcData.security.type === 'basic'">
            <el-form-item label="用户名">
              <el-input 
                v-model="rpcData.security.username" 
                placeholder="username"
                @input="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="rpcData.security.type === 'basic'">
            <el-form-item label="密码">
              <el-input 
                v-model="rpcData.security.password" 
                type="password"
                placeholder="password"
                show-password
                @input="updateValue"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        <RestHttpHeaders 
          v-model="rpcData.httpHeaders" 
          @update:modelValue="updateValue"
        />
      </div>

      <!-- 表达式配置 -->
      <div class="section">
        <div class="section-title">表达式配置</div>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请求URL表达式" required>
              <el-input 
                v-model="rpcData.requestUrlExpression" 
                placeholder="http://127.0.0.1:5001/${deviceName}"
                @input="updateValue"
              />
              <div class="field-hint">构建请求URL的表达式，可使用 ${deviceName}、${methodName}、${requestId} 等变量</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="值表达式" required>
              <el-input 
                type="textarea"
                :rows="3"
                v-model="rpcData.valueExpression" 
                placeholder="${params}"
                @input="updateValue"
              />
              <div class="field-hint">构建请求体的表达式，可使用 ${params}、${deviceName} 等变量</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表达式帮助 -->
      <div class="section">
        <el-collapse size="small">
          <el-collapse-item name="expressions" title="表达式使用说明">
            <div class="help-content">
              <h4>可用变量</h4>
              <ul>
                <li><code>${deviceName}</code> - 设备名称</li>
                <li><code>${methodName}</code> - RPC方法名</li>
                <li><code>${requestId}</code> - 请求ID（仅无响应RPC）</li>
                <li><code>${params}</code> - RPC参数对象</li>
                <li><code>${params.param1}</code> - 特定参数值</li>
              </ul>
              
              <h4>URL表达式示例</h4>
              <ul>
                <li><code>http://127.0.0.1:5001/${deviceName}</code> - 简单设备RPC</li>
                <li><code>http://api.example.com/device/${deviceName}/rpc/${methodName}</code> - RESTful RPC</li>
                <li><code>sensor/${deviceName}/request/${methodName}/${requestId}</code> - 无响应RPC</li>
              </ul>
              
              <h4>值表达式示例</h4>
              <pre><code>// 简单参数传递
${params}

// JSON格式
{
  "sensorName": "${deviceName}",
  "sensorModel": "${params.sensorModel}",
  "certificateNumber": "${params.certificateNumber}",
  "temp": "${params.temp}",
  "hum": "${params.hum}"
}

// 特定参数
${params.hum}</code></pre>
              
              <h4>RPC类型说明</h4>
              <ul>
                <li><strong>有响应RPC</strong>：网关等待外部系统响应，并将响应返回给IoTCloud</li>
                <li><strong>无响应RPC</strong>：网关发送请求后不等待响应，适用于单向控制</li>
              </ul>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 配置预览 -->
      <div class="section" v-if="rpcData.requestUrlExpression">
        <div class="section-title">配置预览</div>
        <div class="preview-content">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="HTTP方法">
              <el-tag :type="getMethodTagType(rpcData.HTTPMethod)" size="small">
                {{ rpcData.HTTPMethod }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="RPC类型">
              <el-tag :type="rpcData.responseTimeout ? 'success' : 'info'" size="small">
                {{ rpcData.responseTimeout ? '有响应RPC' : '无响应RPC' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="设备过滤器">
              <code>{{ rpcData.deviceNameFilter }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="方法过滤器">
              <code>{{ rpcData.methodFilter }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="超时时间">
              {{ rpcData.timeout }}秒
            </el-descriptions-item>
            <el-descriptions-item label="重试次数">
              {{ rpcData.tries }}次
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import RestHttpHeaders from './RestHttpHeaders.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

const responseEnabled = ref(false)

// RPC数据
const rpcData = computed({
  get: () => {
    const data = {
      deviceNameFilter: '.*',
      methodFilter: '',
      requestUrlExpression: '',
      HTTPMethod: 'GET',
      valueExpression: '${params}',
      timeout: 10,
      tries: 3,
      httpHeaders: {
        'Content-Type': 'application/json'
      },
      security: {
        type: 'anonymous'
      },
      ...props.modelValue
    }
    
    // 设置响应开关状态
    responseEnabled.value = !!data.responseTimeout
    
    return data
  },
  set: (value) => emit('update:modelValue', value)
})

// 获取HTTP方法标签类型
const getMethodTagType = (method) => {
  const methodTagMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'PATCH': 'info',
    'DELETE': 'danger'
  }
  return methodTagMap[method] || 'info'
}

// 切换响应配置
const toggleResponse = (enabled) => {
  const newData = { ...rpcData.value }
  
  if (enabled) {
    newData.responseTimeout = 1
    newData.responseValueExpression = '${response}'
  } else {
    delete newData.responseTimeout
    delete newData.responseValueExpression
  }
  
  emit('update:modelValue', newData)
}

// 安全类型变化处理
const handleSecurityTypeChange = () => {
  const newData = { ...rpcData.value }
  if (newData.security.type === 'anonymous') {
    delete newData.security.username
    delete newData.security.password
  } else {
    newData.security.username = ''
    newData.security.password = ''
  }
  emit('update:modelValue', newData)
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...rpcData.value })
}

// 删除配置
const handleDelete = () => {
  emit('delete')
}
</script>

<style lang="scss" scoped>
.rest-server-rpc-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      align-items: center;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .help-content {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
    }
    
    ul {
      margin: 0 0 16px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
        font-size: 13px;
        color: #606266;
        line-height: 1.5;
        
        code {
          background: #f5f7fa;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
          color: #e6a23c;
        }
        
        strong {
          color: #409eff;
        }
      }
    }
    
    p {
      margin: 8px 0;
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
    }
    
    pre {
      margin: 8px 0;
      padding: 12px;
      background: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
        font-size: 12px;
        line-height: 1.4;
        color: #24292e;
      }
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .el-collapse-item__content {
    padding: 12px 0 0 0;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}
</style> 