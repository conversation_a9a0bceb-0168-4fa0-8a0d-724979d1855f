<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="keyForm" :rules="rules" ref="formRef" label-width="120px">
      <el-row :gutter="20">
        <!-- 键名称/方法名 -->
        <el-col :span="12">
          <el-form-item 
            :label="dataType === 'serverSideRpc' ? '方法名' : '键名称'" 
            :prop="dataType === 'serverSideRpc' ? 'method' : 'key'"
            required
          >
            <el-input 
              v-model="keyForm[dataType === 'serverSideRpc' ? 'method' : 'key']" 
              :placeholder="dataType === 'serverSideRpc' ? 'set_state' : 'temperature'"
            />
            <div class="field-hint">
              {{ dataType === 'serverSideRpc' ? 'RPC方法名称' : '数据键名称' }}
            </div>
          </el-form-item>
        </el-col>
        
        <!-- 对象类型 -->
        <el-col :span="12">
          <el-form-item label="对象类型" prop="objectType" required>
            <el-select 
              v-model="keyForm.objectType" 
              placeholder="请选择对象类型"
              @change="handleObjectTypeChange"
            >
              <el-option 
                v-for="(label, value) in objectTypes" 
                :key="value"
                :label="label" 
                :value="value"
              />
            </el-select>
            <div class="field-hint">BACnet对象类型</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <!-- 对象ID -->
        <el-col :span="12">
          <el-form-item label="对象ID" prop="objectId" required>
            <el-input 
              v-model="keyForm.objectId" 
              placeholder="1"
            />
            <div class="field-hint">BACnet对象实例号</div>
          </el-form-item>
        </el-col>
        
        <!-- 属性ID -->
        <el-col :span="12">
          <el-form-item label="属性ID" prop="propertyId" required>
            <el-select 
              v-model="keyForm.propertyId" 
              placeholder="请选择属性ID"
              filterable
              allow-create
            >
              <el-option 
                v-for="propertyId in availablePropertyIds" 
                :key="propertyId"
                :label="getPropertyIdDisplay(propertyId)" 
                :value="propertyId"
              />
            </el-select>
            <div class="field-hint">BACnet对象属性标识符</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- RPC特有配置 -->
      <template v-if="dataType === 'serverSideRpc'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求类型" prop="requestType" required>
              <el-select v-model="keyForm.requestType" placeholder="请选择请求类型">
                <el-option label="写属性" value="writeProperty" />
                <el-option label="读属性" value="readProperty" />
              </el-select>
              <div class="field-hint">BACnet请求操作类型</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求超时" prop="requestTimeout" required>
              <el-input-number 
                v-model.number="keyForm.requestTimeout" 
                :min="100" 
                :step="100"
                controls-position="right"
                placeholder="10000"
              />
              <span class="unit-suffix">毫秒</span>
              <div class="field-hint">请求超时时间</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      
      <!-- 属性更新特有配置 -->
      <template v-if="dataType === 'attributeUpdates'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求类型" prop="requestType" required>
              <el-select v-model="keyForm.requestType" placeholder="请选择请求类型">
                <el-option label="写属性" value="writeProperty" />
                <el-option label="读属性" value="readProperty" />
              </el-select>
              <div class="field-hint">属性更新操作类型</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dataKey: {
    type: Object,
    default: null
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries', 'attributeUpdates', 'serverSideRpc'].includes(value)
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// BACnet对象类型
const objectTypes = {
  analogInput: '模拟输入',
  analogOutput: '模拟输出',
  analogValue: '模拟值',
  binaryInput: '二进制输入',
  binaryOutput: '二进制输出',
  binaryValue: '二进制值',
  multiStateInput: '多状态输入',
  multiStateOutput: '多状态输出',
  multiStateValue: '多状态值'
}

// 属性ID映射
const propertyIdsByObjectType = {
  analogInput: ['presentValue', 'statusFlags', 'covIncrement', 'units'],
  analogOutput: ['presentValue', 'statusFlags', 'covIncrement', 'units', 'priorityArray', 'relinquishDefault'],
  analogValue: ['presentValue', 'statusFlags', 'covIncrement', 'units'],
  binaryInput: ['presentValue', 'statusFlags', 'eventState', 'outOfService', 'polarity'],
  binaryOutput: ['presentValue', 'statusFlags', 'eventState', 'outOfService', 'polarity', 'priorityArray', 'relinquishDefault'],
  binaryValue: ['presentValue', 'statusFlags', 'eventState', 'outOfService'],
  multiStateInput: ['presentValue', 'statusFlags', 'eventState', 'outOfService', 'numberOfStates'],
  multiStateOutput: ['presentValue', 'statusFlags', 'eventState', 'outOfService', 'numberOfStates', 'priorityArray', 'relinquishDefault'],
  multiStateValue: ['presentValue', 'statusFlags', 'eventState', 'outOfService', 'numberOfStates']
}

// 属性ID显示名称映射
const propertyIdDisplayMap = {
  presentValue: '当前值',
  statusFlags: '状态标志',
  covIncrement: 'COV增量',
  units: '单位',
  eventState: '事件状态',
  outOfService: '停用状态',
  polarity: '极性',
  priorityArray: '优先级数组',
  relinquishDefault: '释放默认值',
  numberOfStates: '状态数量'
}

// 数据键表单
const keyForm = reactive({
  key: '',
  method: '',
  objectType: 'analogOutput',
  objectId: '1',
  propertyId: 'presentValue',
  requestType: 'writeProperty',
  requestTimeout: 10000
})

// 可用的属性ID列表
const availablePropertyIds = ref([])

// 表单验证规则
const rules = reactive({
  key: [
    { required: true, message: '请输入键名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  method: [
    { required: true, message: '请输入方法名', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  objectType: [
    { required: true, message: '请选择对象类型', trigger: 'change' }
  ],
  objectId: [
    { required: true, message: '请输入对象ID', trigger: 'blur' }
  ],
  propertyId: [
    { required: true, message: '请选择属性ID', trigger: 'change' }
  ],
  requestType: [
    { required: true, message: '请选择请求类型', trigger: 'change' }
  ],
  requestTimeout: [
    { required: true, message: '请输入请求超时时间', trigger: 'blur' },
    { type: 'number', min: 100, message: '超时时间不能小于100毫秒', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.dataKey) {
    // 加载数据键数据
    Object.assign(keyForm, {
      key: '',
      method: '',
      objectType: 'analogOutput',
      objectId: '1',
      propertyId: 'presentValue',
      requestType: 'writeProperty',
      requestTimeout: 10000,
      ...props.dataKey
    })
    
    // 更新可用属性ID
    updateAvailablePropertyIds()
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 获取对话框标题
const getDialogTitle = () => {
  const typeNames = {
    attributes: '属性',
    timeseries: '遥测',
    attributeUpdates: '属性更新',
    serverSideRpc: 'RPC方法'
  }
  const typeName = typeNames[props.dataType] || '数据键'
  return `${props.isEdit ? '编辑' : '添加'}${typeName}`
}

// 获取属性ID显示名称
const getPropertyIdDisplay = (propertyId) => {
  return propertyIdDisplayMap[propertyId] || propertyId
}

// 处理对象类型变化
const handleObjectTypeChange = () => {
  updateAvailablePropertyIds()
  // 如果当前属性ID不在新的可用列表中，重置为第一个
  if (!availablePropertyIds.value.includes(keyForm.propertyId)) {
    keyForm.propertyId = availablePropertyIds.value[0] || 'presentValue'
  }
}

// 更新可用属性ID
const updateAvailablePropertyIds = () => {
  availablePropertyIds.value = propertyIdsByObjectType[keyForm.objectType] || ['presentValue']
}

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...keyForm }
    
    // 根据数据类型清理不需要的字段
    if (props.dataType === 'serverSideRpc') {
      delete saveData.key
    } else {
      delete saveData.method
      if (props.dataType !== 'attributeUpdates') {
        delete saveData.requestType
      }
      if (props.dataType !== 'serverSideRpc') {
        delete saveData.requestTimeout
      }
    }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '数据键更新成功' : '数据键添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}

// 初始化可用属性ID
updateAvailablePropertyIds()
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.unit-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  
  .el-input-number {
    width: 100%;
  }
}
</style> 