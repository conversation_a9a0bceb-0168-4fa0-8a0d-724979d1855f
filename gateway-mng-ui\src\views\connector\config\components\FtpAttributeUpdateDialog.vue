<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑属性更新' : '添加属性更新'"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="updateForm" :rules="rules" ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="文件路径" prop="path" required>
            <el-input 
              v-model="updateForm.path" 
              placeholder="fol/hello.json"
            />
            <div class="field-hint">写入属性值的目标文件路径</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备名筛选器" prop="deviceNameFilter" required>
            <el-input 
              v-model="updateForm.deviceNameFilter" 
              placeholder=".*"
            />
            <div class="field-hint">正则表达式，用于匹配设备名称</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="写入模式" prop="writingMode" required>
            <el-select v-model="updateForm.writingMode" placeholder="请选择写入模式">
              <el-option label="覆盖模式" value="OVERRIDE" />
              <el-option label="写入模式" value="WRITE" />
            </el-select>
            <div class="field-hint">文件写入的模式</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="值表达式" prop="valueExpression" required>
            <el-input 
              v-model="updateForm.valueExpression" 
              placeholder="{'${attributeKey}':'${attributeValue}'}"
            />
            <div class="field-hint">属性值的格式化表达式</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 表达式语法帮助 -->
      <div class="expression-help">
        <el-alert
          title="表达式语法说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>变量替换:</strong></p>
            <ul>
              <li><code>${attributeKey}</code> - 属性键名</li>
              <li><code>${attributeValue}</code> - 属性值</li>
            </ul>
            <p><strong>写入模式:</strong></p>
            <ul>
              <li><strong>OVERRIDE</strong> - 覆盖整个文件内容</li>
              <li><strong>WRITE</strong> - 追加写入文件</li>
            </ul>
            <p><strong>示例:</strong> <code>{'${attributeKey}':'${attributeValue}'}</code></p>
          </div>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  attributeUpdate: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 更新表单数据
const updateForm = reactive({
  path: 'fol/hello.json',
  deviceNameFilter: '.*',
  writingMode: 'WRITE',
  valueExpression: "{'${attributeKey}':'${attributeValue}'}"
})

// 表单验证规则
const rules = reactive({
  path: [
    { required: true, message: '请输入文件路径', trigger: 'blur' }
  ],
  deviceNameFilter: [
    { required: true, message: '请输入设备名筛选器', trigger: 'blur' }
  ],
  writingMode: [
    { required: true, message: '请选择写入模式', trigger: 'change' }
  ],
  valueExpression: [
    { required: true, message: '请输入值表达式', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.attributeUpdate) {
    // 加载属性更新数据
    Object.assign(updateForm, {
      path: 'fol/hello.json',
      deviceNameFilter: '.*',
      writingMode: 'WRITE',
      valueExpression: "{'${attributeKey}':'${attributeValue}'}",
      ...props.attributeUpdate
    })
  } else if (newValue) {
    // 重置为默认值
    Object.assign(updateForm, {
      path: 'fol/hello.json',
      deviceNameFilter: '.*',
      writingMode: 'WRITE',
      valueExpression: "{'${attributeKey}':'${attributeValue}'}"
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...updateForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '属性更新配置更新成功' : '属性更新配置添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.expression-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        code {
          background: #f1f2f3;
          padding: 2px 6px;
          border-radius: 3px;
          font-family: monospace;
          color: #e6a23c;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style> 