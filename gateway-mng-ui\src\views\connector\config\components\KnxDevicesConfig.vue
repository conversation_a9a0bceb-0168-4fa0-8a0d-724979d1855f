<template>
  <div class="knx-devices-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>KNX设备配置</span>
          <el-button type="primary" size="small" @click="addDevice">
            <el-icon><Plus /></el-icon>
            添加设备
          </el-button>
        </div>
      </template>
      
      <div v-if="devicesConfig.length === 0" class="empty-state">
        <el-empty description="暂无设备配置">
          <el-button type="primary" @click="addDevice">添加第一个设备</el-button>
        </el-empty>
      </div>
      
      <div v-else class="devices-list">
        <el-card 
          v-for="(device, index) in devicesConfig" 
          :key="index" 
          class="device-card"
          shadow="hover"
        >
          <template #header>
            <div class="device-header">
              <div class="device-info">
                <span class="device-name">{{ getDeviceName(device) }}</span>
                <el-tag size="small" type="success">KNX设备</el-tag>
              </div>
              <div class="device-actions">
                <el-button type="primary" size="small" link @click="editDevice(device, index)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" link @click="deleteDevice(index)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="device-summary">
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">设备名表达式:</span>
                  <span class="value">{{ device.deviceInfo?.deviceNameExpression || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">轮询周期:</span>
                  <span class="value">{{ device.pollPeriod }}ms</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">数据点:</span>
                  <span class="value">
                    属性{{ (device.attributes || []).length }}个，
                    遥测{{ (device.timeseries || []).length }}个
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 设备配置对话框 -->
    <KnxDeviceDialog
      v-model="dialogVisible"
      :device="currentDevice"
      :is-edit="isEdit"
      @save="handleSaveDevice"
      @cancel="handleCancelDevice"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import KnxDeviceDialog from './KnxDeviceDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const currentDevice = ref(null)
const isEdit = ref(false)
const currentIndex = ref(-1)

// 设备配置数据
const devicesConfig = reactive([])

// 获取设备名称
const getDeviceName = (device) => {
  if (device.deviceInfo?.deviceNameExpression) {
    return device.deviceInfo.deviceNameExpression
  }
  return '未命名设备'
}

// 添加设备
const addDevice = () => {
  currentDevice.value = null
  isEdit.value = false
  currentIndex.value = -1
  dialogVisible.value = true
}

// 编辑设备
const editDevice = (device, index) => {
  currentDevice.value = { ...device }
  isEdit.value = true
  currentIndex.value = index
  dialogVisible.value = true
}

// 删除设备
const deleteDevice = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个设备配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    devicesConfig.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存设备
const handleSaveDevice = (deviceData) => {
  if (isEdit.value && currentIndex.value >= 0) {
    // 编辑模式
    devicesConfig.splice(currentIndex.value, 1, deviceData)
    ElMessage.success('设备更新成功')
  } else {
    // 添加模式
    devicesConfig.push(deviceData)
    ElMessage.success('设备添加成功')
  }
  
  dialogVisible.value = false
  handleChange()
}

// 取消操作
const handleCancelDevice = () => {
  dialogVisible.value = false
}

// 处理配置变化
const handleChange = () => {
  const devices = devicesConfig.map(device => ({ ...device }))
  emit('update:modelValue', devices)
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (Array.isArray(newValue)) {
    devicesConfig.splice(0, devicesConfig.length, ...newValue)
  }
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
.knx-devices-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .devices-list {
    .device-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .device-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .device-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .device-name {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .device-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .device-summary {
        margin-top: 16px;
        
        .summary-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .label {
            font-size: 12px;
            color: #909399;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
            word-break: break-all;
          }
        }
      }
    }
  }
}
</style> 