<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>网络配置</span>
        </div>
      </template>

      <!-- 网络接口选择 -->
      <el-form label-width="120px">
        <el-form-item label="网络接口">
          <el-select v-model="selectedInterface" @change="loadNetworkConfig" style="width: 400px">
            <el-option
              v-for="item in interfaces"
              :key="item.name"
              :label="formatInterfaceLabel(item)"
              :value="item.name"
            >
              <span>{{ item.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                <el-tag 
                  size="small"
                  :type="item.status.state === 'up' ? 'success' : 'info'"
                >
                  {{ item.status.state === 'up' ? '已启用' : '已禁用' }}
                </el-tag>
                <el-tag 
                  size="small"
                  :type="item.status.carrier ? 'success' : 'info'"
                  style="margin-left: 5px"
                >
                  {{ item.status.carrier ? '已连接' : '未连接' }}
                </el-tag>
                <el-tag 
                  size="small"
                  type="info"
                  style="margin-left: 5px"
                >
                  {{ item.status.speed }}
                </el-tag>
                <el-tag 
                  size="small"
                  type="warning"
                  style="margin-left: 5px"
                >
                  {{ isWirelessInterface(item.name) ? '无线网卡' : '有线网卡' }}
                </el-tag>
              </span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <!-- WiFi网络搜索和连接 -->
      <template v-if="selectedInterface && isWirelessInterface(selectedInterface)">
        <el-divider content-position="left">WiFi网络</el-divider>
        <el-form label-width="120px">
          <el-form-item>
            <el-button type="primary" @click="handleScanWifiNetworks" :loading="scanning">
              扫描WiFi网络
            </el-button>
          </el-form-item>
        </el-form>

        <!-- WiFi网络列表 -->
        <el-table
          v-loading="scanning"
          :data="wifiNetworks"
          style="width: 100%; margin-bottom: 20px"
        >
          <el-table-column prop="ssid" label="网络名称" />
          <el-table-column prop="signal" label="信号强度">
            <template #default="scope">
              <el-progress
                :percentage="scope.row.signal"
                :status="getSignalStatus(scope.row.signal)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="security" label="安全类型" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click="showWifiConnectDialog(scope.row)"
              >
                连接
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>

      <!-- 网络配置表单 -->
      <el-form
        v-if="selectedInterface"
        ref="networkForm"
        :model="networkConfig"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="IP地址" prop="address">
          <el-input v-model="networkConfig.address" placeholder="请输入IP地址" />
        </el-form-item>

        <el-form-item label="子网掩码" prop="netmask">
          <el-input v-model="networkConfig.netmask" placeholder="请输入子网掩码" />
        </el-form-item>

        <el-form-item label="默认网关" prop="gateway">
          <el-input v-model="networkConfig.gateway" placeholder="请输入默认网关" />
        </el-form-item>

        <el-form-item label="首选DNS" prop="dns1">
          <el-input v-model="networkConfig.dns1" placeholder="请输入首选DNS服务器" />
        </el-form-item>

        <el-form-item label="备选DNS" prop="dns2">
          <el-input v-model="networkConfig.dns2" placeholder="请输入备选DNS服务器（可选）" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="configuring">保存配置</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button 
            type="danger" 
            @click="disconnectNetwork" 
            :loading="configuring"
            :disabled="!networkConfig.address"
          >
            断开连接
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- WiFi连接对话框 -->
    <el-dialog
      v-model="wifiDialogVisible"
      title="连接WiFi网络"
      width="400px"
    >
      <el-form
        ref="wifiForm"
        :model="wifiConfig"
        :rules="wifiRules"
        label-width="100px"
      >
        <el-form-item label="网络名称">
          <span>{{ selectedWifiNetwork.ssid }}</span>
        </el-form-item>
        <el-form-item label="安全类型">
          <span>{{ selectedWifiNetwork.security }}</span>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="selectedWifiNetwork.security !== '开放'">
          <el-input
            v-model="wifiConfig.password"
            type="password"
            placeholder="请输入WiFi密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="wifiDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="connectWifi" :loading="connecting">
            连接
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 配置进度对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="正在应用网络配置"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div style="text-align: center;">
        <el-progress type="dashboard" :percentage="configProgress" :status="configStatus">
          <template #default="{ percentage }">
            <span class="progress-label">{{ configStatusText }}</span>
            <span class="progress-percentage">{{ !isComplete ? '' : percentage === 100 ? '成功' : '失败' }}</span>
          </template>
        </el-progress>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'
import { getNetworkInterfaces, getNetworkConfig, setNetworkConfig, getNetworkStatus, scanWifiNetworks, connectWifiNetwork, getWifiStatus, disconnectNetworkConnection } from '@/api/network'

// 网络接口列表
const interfaces = ref([])
const selectedInterface = ref('')

// WiFi相关状态
const scanning = ref(false)
const wifiNetworks = ref([])
const wifiDialogVisible = ref(false)
const selectedWifiNetwork = ref({})
const connecting = ref(false)
const wifiConfig = ref({
  password: ''
})

// WiFi表单验证规则
const wifiRules = {
  password: [
    { required: true, message: '请输入WiFi密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能小于8个字符', trigger: 'blur' }
  ]
}

// 判断是否为无线网卡
const isWirelessInterface = (interfaceName) => {
  return interfaceName.startsWith('wlan') || interfaceName.startsWith('wifi') || interfaceName.startsWith('wlp')
}

// 获取信号强度状态
const getSignalStatus = (signal) => {
  if (signal >= 80) return 'success'
  if (signal >= 60) return 'warning'
  return 'exception'
}

// 扫描WiFi网络
const handleScanWifiNetworks = async () => {
  if (!selectedInterface.value || !isWirelessInterface(selectedInterface.value)) return

  scanning.value = true
  try {
    const res = await scanWifiNetworks(selectedInterface.value)
    if (res.msg === 'success') {
      wifiNetworks.value = res.data
    } else {
      ElMessage.error(res.data || '扫描WiFi网络失败')
    }
  } catch (error) {
    console.error('扫描WiFi网络失败:', error)
    ElMessage.error('扫描WiFi网络失败')
  }
  scanning.value = false
}

// 显示WiFi连接对话框
const showWifiConnectDialog = (network) => {
  selectedWifiNetwork.value = network
  wifiConfig.value.password = ''
  wifiDialogVisible.value = true
}

// 连接WiFi网络
const connectWifi = async () => {
  if (!wifiForm.value) return

  await wifiForm.value.validate(async (valid) => {
    if (valid) {
      connecting.value = true
      try {
        const res = await connectWifiNetwork({
          interface: selectedInterface.value,
          ssid: selectedWifiNetwork.value.ssid,
          password: wifiConfig.value.password
        })
        if (res.msg === 'success') {
          currentTaskId.value = res.data.task_id
          startStatusPolling(true)  // 传入 true 表示是 WiFi 连接
          wifiDialogVisible.value = false
        } else {
          ElMessage.error(res.data || 'WiFi连接失败')
        }
      } catch (error) {
        console.error('WiFi连接失败:', error)
        ElMessage.error('WiFi连接失败')
      }
      connecting.value = false
    }
  })
}

// 检查WiFi连接状态
const checkWifiStatus = async () => {
  if (!currentTaskId.value) return

  try {
    const res = await getWifiStatus(currentTaskId.value)
    if (res.msg === 'success') {
      const status = res.data.status
      if (status === 'running') {
        configProgress.value = 50
      } else if (status === 'success') {
        configProgress.value = 100
        isComplete.value = true
        clearTimer()
        dialogVisible.value = false
        configuring.value = false
        ElMessage.success('WiFi连接成功')
        loadNetworkConfig()
      }
    } else {
      configProgress.value = 0
      isComplete.value = true
      clearTimer()
      configuring.value = false
    }
  } catch (error) {
    console.error('检查WiFi连接状态失败:', error)
    configProgress.value = 0
    isComplete.value = true
    clearTimer()
    configuring.value = false
  }
}

// 网络配置表单
const networkForm = ref(null)
const wifiForm = ref(null)
const networkConfig = ref({
  interface: '',
  address: '',
  netmask: '',
  gateway: '',
  dns1: '',
  dns2: ''
})

// 表单验证规则
const rules = {
  address: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP地址格式不正确', trigger: 'blur' }
  ],
  netmask: [
    { required: true, message: '请输入子网掩码', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '子网掩码格式不正确', trigger: 'blur' }
  ],
  gateway: [
    { required: true, message: '请输入默认网关', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '网关地址格式不正确', trigger: 'blur' }
  ],
  dns1: [
    { required: true, message: '请输入首选DNS服务器', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'DNS服务器地址格式不正确', trigger: 'blur' }
  ],
  dns2: [
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'DNS服务器地址格式不正确', trigger: 'blur' }
  ]
}

// 状态轮询相关
const configuring = ref(false)
const dialogVisible = ref(false)
const configProgress = ref(0)
const currentTaskId = ref('')
let statusTimer = null

const configStatus = computed(() => {
  if (!isComplete.value) return ''
  return configProgress.value === 100 ? 'success' : 'exception'
})

const configStatusText = computed(() => {
  if (!isComplete.value) return '正在应用配置...'
  return configProgress.value === 100 ? '配置成功' : '配置失败'
})

const isComplete = ref(false)

// 格式化接口标签显示
const formatInterfaceLabel = (item) => {
  return `${item.name}`
}

// 加载网络接口列表
const loadInterfaces = async () => {
  try {
    const res = await getNetworkInterfaces()
    if (res.msg === 'success') {
      interfaces.value = res.data
      if (interfaces.value.length > 0) {
        selectedInterface.value = interfaces.value[0].name
        await loadNetworkConfig()
      }
    } else {
      ElMessage.error('获取网络接口列表失败')
    }
  } catch (error) {
    console.error('获取网络接口列表失败:', error)
    ElMessage.error('获取网络接口列表失败')
  }
}

// 加载选中接口的网络配置
const loadNetworkConfig = async () => {
  if (!selectedInterface.value) return

  try {
    const res = await getNetworkConfig(selectedInterface.value)
    if (res.msg === 'success') {
      networkConfig.value = res.data
    } else {
      ElMessage.error('获取网络配置失败')
    }
  } catch (error) {
    console.error('获取网络配置失败:', error)
    ElMessage.error('获取网络配置失败')
  }
}

// 检查配置任务状态
const checkConfigStatus = async () => {
  if (!currentTaskId.value) return

  try {
    const res = await getNetworkStatus(currentTaskId.value)
    if (res.msg === 'success') {
      const status = res.data.status
      if (status === 'running') {
        configProgress.value = 50 // 正在配置中
      } else if (status === 'success') {
        configProgress.value = 100
        isComplete.value = true
        clearTimer()
        dialogVisible.value = false
        configuring.value = false
        ElMessage.success('网络配置更新成功')
        loadInterfaces() // 重新加载接口状态
      }
    } else {
      configProgress.value = 0
      isComplete.value = true
      clearTimer()
      configuring.value = false
      // ElMessage.error('检查配置状态失败')
    }
  } catch (error) {
    console.error('检查配置状态失败:', error)
    configProgress.value = 0
    isComplete.value = true
    clearTimer()
    configuring.value = false
  }
}

// 清除定时器
const clearTimer = () => {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
}

// 开始状态轮询
const startStatusPolling = (isWifi = false) => {
  configProgress.value = 0
  isComplete.value = false
  dialogVisible.value = true
  statusTimer = setInterval(() => {
    if (isWifi) {
      checkWifiStatus()
    } else {
      checkConfigStatus()
    }
  }, 5000) // 每5秒检查一次
}

// 提交表单
const submitForm = async () => {
  if (!networkForm.value) return
  
  await networkForm.value.validate(async (valid) => {
    if (valid) {
      try {
        configuring.value = true
        const res = await setNetworkConfig(networkConfig.value)
        if (res.msg === 'success') {
          currentTaskId.value = res.data.task_id
          startStatusPolling()
        } else {
          configuring.value = false
          ElMessage.error(res.data || '保存网络配置失败')
        }
      } catch (error) {
        configuring.value = false
        console.error('保存网络配置失败:', error)
        ElMessage.error('保存网络配置失败')
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  if (networkForm.value) {
    networkForm.value.resetFields()
    loadNetworkConfig()
  }
}

// 检查是否为最后一个有效连接
const isLastActiveConnection = async (ifaceName) => {
  try {
    const res = await getNetworkInterfaces()
    if (res.msg === 'success') {
      // 过滤出已连接且不是当前接口的网络接口
      const activeConnections = res.data.filter(iface => 
        iface.name !== ifaceName && 
        iface.status.state === 'up' && 
        iface.status.carrier
      )
      return activeConnections.length === 0
    }
    return false
  } catch (error) {
    console.error('检查网络连接状态失败:', error)
    return false
  }
}

// 断开网络连接
const disconnectNetwork = async () => {
  try {
    const isLast = await isLastActiveConnection(selectedInterface.value)
    if (isLast) {
      ElMessage.warning('这是最后一个有效连接，不允许断开')
      return
    }

    configuring.value = true
    const res = await disconnectNetworkConnection(selectedInterface.value)
    if (res.msg === 'success') {
      ElMessage.success('网络连接已断开')
      loadInterfaces() // 重新加载接口状态
    } else {
      ElMessage.error(res.data || '断开网络连接失败')
    }
  } catch (error) {
    console.error('断开网络连接失败:', error)
    ElMessage.error('断开网络连接失败')
  } finally {
    configuring.value = false
  }
}

// 页面加载时获取网络接口列表
onMounted(() => {
  loadInterfaces()
})

// 组件销毁时清理定时器
onUnmounted(() => {
  clearTimer()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-form {
  max-width: 600px;
}

.el-select-dropdown__item {
  height: auto;
  padding: 8px 20px;
}

.progress-label {
  display: block;
  margin-top: 10px;
}

.progress-percentage {
  display: block;
  margin-top: 10px;
  font-size: 20px;
}

.el-divider {
  margin: 20px 0;
}
</style>