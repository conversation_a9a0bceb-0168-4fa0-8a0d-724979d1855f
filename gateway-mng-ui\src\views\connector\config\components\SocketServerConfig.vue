<template>
  <div class="socket-server-config">
    <el-form :model="serverConfig" :rules="rules" label-width="140px" ref="formRef">
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <span>Socket服务器配置</span>
            <el-tooltip content="Socket连接和通信配置管理" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
      
        <!-- 连接配置 -->
        <div class="config-section">
          <h4 class="section-title">连接配置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="连接类型" prop="type" required>
                <el-select 
                  v-model="serverConfig.type" 
                  placeholder="请选择连接类型"
                  
                  style="width: 100%"
                >
                  <el-option label="TCP" value="TCP">
                    <div class="protocol-option">
                      <span class="protocol-name">TCP</span>
                      <span class="protocol-desc">传输控制协议，可靠连接</span>
                    </div>
                  </el-option>
                  <el-option label="UDP" value="UDP">
                    <div class="protocol-option">
                      <span class="protocol-name">UDP</span>
                      <span class="protocol-desc">用户数据报协议，快速传输</span>
                    </div>
                  </el-option>
                </el-select>
                <div class="field-hint">选择Socket通信协议类型</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="服务器地址" prop="address" required>
                <el-input 
                  v-model="serverConfig.address" 
                  placeholder="127.0.0.1"
                  
                />
                <div class="field-hint">Socket服务器的IP地址</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="端口号" prop="port" required>
                <el-input-number 
                  v-model.number="serverConfig.port" 
                  :min="1" 
                  :max="65535"
                  controls-position="right"
                  placeholder="50000"
                  
                  style="width: 100%"
                />
                <div class="field-hint">服务器监听端口 (1-65535)</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="缓冲区大小" prop="bufferSize" required>
                <el-input-number 
                  v-model.number="serverConfig.bufferSize" 
                  :min="64" 
                  :max="65536"
                  :step="64"
                  controls-position="right"
                  placeholder="1024"
                  
                  style="width: 100%"
                />
                <span class="unit-suffix">字节</span>
                <div class="field-hint">数据接收缓冲区大小 (64-65536字节)</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 高级设置 -->
      <el-card class="advanced-settings">
        <template #header>
          <div class="card-header">
            <span>高级设置</span>
            <el-button 
              type="text" 
              @click="showAdvanced = !showAdvanced"
              :icon="showAdvanced ? ArrowUp : ArrowDown"
            >
              {{ showAdvanced ? '收起' : '展开' }}
            </el-button>
          </div>
        </template>
        
        <el-collapse-transition>
          <div v-show="showAdvanced">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="连接超时" prop="connectionTimeout">
                  <el-input-number 
                    v-model.number="serverConfig.connectionTimeout" 
                    :min="1000" 
                    :max="60000"
                    :step="1000"
                    controls-position="right"
                    placeholder="5000"
                    
                    style="width: 100%"
                  />
                  <span class="unit-suffix">毫秒</span>
                  <div class="field-hint">连接建立超时时间 (1-60秒)</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="读取超时" prop="readTimeout">
                  <el-input-number 
                    v-model.number="serverConfig.readTimeout" 
                    :min="1000" 
                    :max="300000"
                    :step="1000"
                    controls-position="right"
                    placeholder="10000"
                    
                    style="width: 100%"
                  />
                  <span class="unit-suffix">毫秒</span>
                  <div class="field-hint">数据读取超时时间 (1-300秒)</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="保持连接" prop="keepAlive">
                  <el-switch 
                    v-model="serverConfig.keepAlive"
                    
                  />
                  <div class="field-hint">启用TCP保持连接机制</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="重连间隔" prop="reconnectInterval">
                  <el-input-number 
                    v-model.number="serverConfig.reconnectInterval" 
                    :min="1000" 
                    :max="60000"
                    :step="1000"
                    controls-position="right"
                    placeholder="5000"
                    
                    style="width: 100%"
                  />
                  <span class="unit-suffix">毫秒</span>
                  <div class="field-hint">连接断开后重连间隔 (1-60秒)</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最大重连次数" prop="maxReconnectAttempts">
                  <el-input-number 
                    v-model.number="serverConfig.maxReconnectAttempts" 
                    :min="0" 
                    :max="100"
                    controls-position="right"
                    placeholder="3"
                    
                    style="width: 100%"
                  />
                  <div class="field-hint">最大重连尝试次数 (0表示无限重连)</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="启用日志" prop="enableLogging">
                  <el-switch 
                    v-model="serverConfig.enableLogging"
                    
                  />
                  <div class="field-hint">启用详细的连接和数据传输日志</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps, nextTick } from 'vue'
import { 
  QuestionFilled, 
  ArrowUp, 
  ArrowDown, 
  Connection, 
  Lightning, 
  Check, 
  Warning 
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      type: 'TCP',
      address: '127.0.0.1',
      port: 50000,
      bufferSize: 1024,
      connectionTimeout: 5000,
      readTimeout: 10000,
      keepAlive: true,
      reconnectInterval: 5000,
      maxReconnectAttempts: 3,
      enableLogging: false
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const showAdvanced = ref(false)

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 服务器配置
const serverConfig = reactive({
  type: 'TCP',
  address: '127.0.0.1',
  port: 50000,
  bufferSize: 1024,
  connectionTimeout: 5000,
  readTimeout: 10000,
  keepAlive: true,
  reconnectInterval: 5000,
  maxReconnectAttempts: 3,
  enableLogging: false,
  ...props.modelValue
})

// 表单验证规则
const rules = {
  type: [
    { required: true, message: '请选择连接类型', trigger: 'change' }
  ],
  address: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: '请输入有效的IP地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
  ],
  bufferSize: [
    { required: true, message: '请输入缓冲区大小', trigger: 'blur' },
    { type: 'number', min: 64, max: 65536, message: '缓冲区大小必须在64-65536字节之间', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value && newValue) {
    isInternalUpdate.value = true
    Object.assign(serverConfig, {
      type: 'TCP',
      address: '127.0.0.1',
      port: 50000,
      bufferSize: 1024,
      connectionTimeout: 5000,
      readTimeout: 10000,
      keepAlive: true,
      reconnectInterval: 5000,
      maxReconnectAttempts: 3,
      enableLogging: false,
      ...newValue
    })
    nextTick(() => {
      isInternalUpdate.value = false
    })
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(serverConfig, (newConfig) => {
  if (!isInternalUpdate.value) {
    emit('update:modelValue', { ...newConfig })
  }
}, { deep: true })

// 处理配置变化
const handleChange = () => {
  // 触发验证
  formRef.value?.validateField(Object.keys(rules))
}

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.socket-server-config {
  .config-card {
    border: 1px solid #e4e7ed;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
      font-size: 16px;
      
      .el-icon {
        color: #909399;
        cursor: help;
      }
    }
  }
  
  .config-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      border-left: 3px solid #409eff;
      padding-left: 8px;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .unit-suffix {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
  }
  
  .protocol-option {
    display: flex;
    flex-direction: column;
    
    .protocol-name {
      font-weight: 600;
      color: #303133;
    }
    
    .protocol-desc {
      font-size: 12px;
      color: #909399;
      margin-top: 2px;
    }
  }
  
  .protocol-comparison {
    .protocol-card {
      border: 2px solid #e4e7ed;
      transition: all 0.3s ease;
      
      &.active {
        border-color: #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }
      
      &.tcp-card.active {
        border-color: #67c23a;
        box-shadow: 0 4px 12px rgba(103, 194, 58, 0.15);
      }
      
      &.udp-card.active {
        border-color: #e6a23c;
        box-shadow: 0 4px 12px rgba(230, 162, 60, 0.15);
      }
      
      .protocol-header {
        display: flex;
        align-items: center;
        
        .protocol-icon {
          margin-right: 8px;
          font-size: 18px;
        }
        
        .protocol-title {
          font-weight: 600;
          color: #303133;
        }
      }
      
      .protocol-features {
        list-style: none;
        padding: 0;
        margin: 0 0 16px 0;
        
        li {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 13px;
          line-height: 1.5;
          
          .feature-icon {
            margin-right: 8px;
            font-size: 14px;
            
            &.success {
              color: #67c23a;
            }
            
            &.warning {
              color: #e6a23c;
            }
          }
        }
      }
      
      .protocol-usage {
        font-size: 12px;
        color: #606266;
        background: #f5f7fa;
        padding: 8px;
        border-radius: 4px;
        line-height: 1.4;
        
        strong {
          color: #303133;
        }
      }
    }
  }
  
  .advanced-settings {
    margin-top: 24px;
    border: 1px solid #e4e7ed;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
      font-size: 14px;
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 16px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      font-size: 13px;
    }
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
  
  :deep(.el-select) {
    width: 100%;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .socket-server-config {
    .protocol-comparison {
      .el-row {
        .el-col {
          margin-bottom: 16px;
        }
      }
    }
    
    :deep(.el-form-item) {
      margin-bottom: 12px;
      
      .el-form-item__label {
        font-size: 12px;
      }
    }
  }
}
</style> 