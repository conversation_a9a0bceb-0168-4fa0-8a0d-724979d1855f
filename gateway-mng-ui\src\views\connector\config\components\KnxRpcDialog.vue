<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑RPC配置' : '添加RPC配置'"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="rpcForm" :rules="rules" ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="请求类型" prop="requestType" required>
            <el-select v-model="rpcForm.requestType" placeholder="请选择请求类型">
              <el-option label="读取 (read)" value="read" />
              <el-option label="写入 (write)" value="write" />
            </el-select>
            <div class="field-hint">RPC操作类型</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方法名" prop="method" required>
            <el-input 
              v-model="rpcForm.method" 
              placeholder="get_name"
            />
            <div class="field-hint">RPC方法名称</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名筛选器" prop="deviceNameFilter" required>
            <el-input 
              v-model="rpcForm.deviceNameFilter" 
              placeholder=".*"
            />
            <div class="field-hint">正则表达式，用于匹配设备名称</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="KNX群组地址" prop="groupAddress" required>
            <el-input 
              v-model="rpcForm.groupAddress" 
              placeholder="1/0/5"
            />
            <div class="field-hint">KNX总线上的群组地址</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数据类型" prop="dataType" required>
            <el-select v-model="rpcForm.dataType" placeholder="请选择数据类型">
              <!-- 基础数据类型 -->
              <el-option-group label="基础数据类型">
                <el-option label="布尔值 (bool)" value="bool" />
                <el-option label="整数 (int)" value="int" />
                <el-option label="浮点数 (float)" value="float" />
                <el-option label="字符串 (string)" value="string" />
              </el-option-group>
              
              <!-- KNX专用数据类型 -->
              <el-option-group label="KNX数据类型">
                <el-option label="温度 (temperature)" value="temperature" />
                <el-option label="湿度 (humidity)" value="humidity" />
                <el-option label="亮度 (brightness)" value="brightness" />
                <el-option label="百分比 U8 (precent_U8)" value="precent_U8" />
                <el-option label="百分比 V8 (precent_V8)" value="precent_V8" />
                <el-option label="角度 (angle)" value="angle" />
                <el-option label="色彩 (color)" value="color" />
                <el-option label="时间 (time)" value="time" />
                <el-option label="日期 (date)" value="date" />
              </el-option-group>
            </el-select>
            <div class="field-hint">RPC数据的类型</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 配置说明 -->
      <div class="config-help">
        <el-alert
          title="RPC配置说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>功能说明:</strong></p>
            <p>RPC配置用于处理来自IoTCloud平台的远程过程调用，实现设备的远程控制和数据读取。</p>
            
            <p><strong>配置参数:</strong></p>
            <ul>
              <li><strong>请求类型</strong> - read（读取）或write（写入）操作</li>
              <li><strong>方法名</strong> - RPC方法的名称标识</li>
              <li><strong>设备名筛选器</strong> - 正则表达式，用于匹配目标设备</li>
              <li><strong>群组地址</strong> - KNX设备的群组地址</li>
              <li><strong>数据类型</strong> - 数据的格式类型</li>
            </ul>
            
            <p><strong>使用场景:</strong></p>
            <ul>
              <li>读取设备名: requestType="read", method="get_name", dataType="string"</li>
              <li>设置设备名: requestType="write", method="set_name", dataType="string"</li>
              <li>读取温度: requestType="read", method="get_temperature", dataType="temperature"</li>
            </ul>
          </div>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  rpc: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// RPC表单数据
const rpcForm = reactive({
  requestType: 'read',
  deviceNameFilter: '.*',
  method: 'get_name',
  dataType: 'string',
  groupAddress: '1/0/5'
})

// 表单验证规则
const rules = reactive({
  requestType: [
    { required: true, message: '请选择请求类型', trigger: 'change' }
  ],
  method: [
    { required: true, message: '请输入方法名', trigger: 'blur' }
  ],
  deviceNameFilter: [
    { required: true, message: '请输入设备名筛选器', trigger: 'blur' }
  ],
  groupAddress: [
    { required: true, message: '请输入KNX群组地址', trigger: 'blur' },
    { 
      pattern: /^(\d+\/\d+\/\d+|\d+)$/,
      message: '群组地址格式：x/y/z 或 xyz',
      trigger: 'blur'
    }
  ],
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.rpc) {
    // 加载RPC数据
    Object.assign(rpcForm, {
      requestType: 'read',
      deviceNameFilter: '.*',
      method: 'get_name',
      dataType: 'string',
      groupAddress: '1/0/5',
      ...props.rpc
    })
  } else if (newValue) {
    // 重置为默认值
    Object.assign(rpcForm, {
      requestType: 'read',
      deviceNameFilter: '.*',
      method: 'get_name',
      dataType: 'string',
      groupAddress: '1/0/5'
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...rpcForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? 'RPC配置更新成功' : 'RPC配置添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style> 