<template>
  <el-card class="extension-config-card" shadow="never">
    <template #header>
      <div class="config-header">
        <div class="config-info">
          <el-tag type="warning" size="small">扩展配置</el-tag>
          <span class="config-name">{{ configData.key || '未命名' }}</span>
          <el-tag :type="getTypeTagType(configData.type)" size="small">
            {{ getTypeDisplay(configData.type) }}
          </el-tag>
        </div>
        <el-button type="danger" size="small" link @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>

    <el-form :model="configData" label-width="100px">
      <!-- 基础配置 -->
      <div class="config-section">
        <div class="section-title">基础配置</div>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="变量名" required>
              <el-input 
                v-model="configData.key" 
                placeholder="temperature"
                @input="updateValue"
              />
              <div class="field-hint">解析后的变量名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据类型" required>
              <el-select v-model="configData.type" @change="handleTypeChange">
                <el-option label="浮点数" value="float" />
                <el-option label="整数" value="int" />
                <el-option label="长整数" value="long" />
                <el-option label="双精度" value="double" />
                <el-option label="布尔值" value="boolean" />
                <el-option label="字符串" value="string" />
                <el-option label="字节" value="byte" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据乘数">
              <el-input-number 
                v-model="configData.multiplier" 
                :precision="6"
                :step="0.1"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
              <div class="field-hint">对解析值进行倍数调整</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 字节解析配置 -->
      <div class="config-section" v-if="needsByteConfig">
        <div class="section-title">字节解析配置</div>
        <el-row :gutter="16">
          <el-col :span="6">
            <el-form-item label="起始字节">
              <el-input-number 
                v-model="configData.fromByte" 
                :min="0" 
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束字节">
              <el-input-number 
                v-model="configData.toByte" 
                :min="configData.fromByte || 0" 
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="字节序">
              <el-select v-model="configData.byteorder" @change="updateValue">
                <el-option label="大端序 (Big)" value="big" />
                <el-option label="小端序 (Little)" value="little" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="有符号">
              <el-switch 
                v-model="configData.signed" 
                @change="updateValue"
              />
              <div class="field-hint">是否为有符号数</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 位操作配置 -->
      <div class="config-section">
        <div class="section-title">
          <span>位操作配置</span>
          <el-switch 
            v-model="bitConfigEnabled" 
            @change="toggleBitConfig"
            size="small"
          />
        </div>
        
        <div v-if="bitConfigEnabled">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="字节地址">
                <el-input-number 
                  v-model="configData.byteAddress" 
                  :min="0" 
                  controls-position="right"
                  style="width: 100%"
                  @change="updateValue"
                />
                <div class="field-hint">要操作的字节地址</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="起始位">
                <el-input-number 
                  v-model="configData.fromBit" 
                  :min="0" 
                  :max="7" 
                  controls-position="right"
                  style="width: 100%"
                  @change="updateValue"
                />
                <div class="field-hint">位范围: 0-7</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="结束位">
                <el-input-number 
                  v-model="configData.toBit" 
                  :min="configData.fromBit || 0" 
                  :max="7" 
                  controls-position="right"
                  style="width: 100%"
                  @change="updateValue"
                />
                <div class="field-hint">位范围: 0-7</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 字符串配置 -->
      <div class="config-section" v-if="configData.type === 'string'">
        <div class="section-title">字符串配置</div>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="编码格式">
              <el-select v-model="configData.encoding" @change="updateValue">
                <el-option label="UTF-8" value="utf-8" />
                <el-option label="ASCII" value="ascii" />
                <el-option label="ISO-8859-1" value="iso-8859-1" />
                <el-option label="GBK" value="gbk" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="字符串长度">
              <el-input-number 
                v-model="configData.length" 
                :min="1" 
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
              <div class="field-hint">固定长度字符串</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="去除空字符">
              <el-switch 
                v-model="configData.trim" 
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 配置预览 -->
      <div class="config-section" v-if="configData.key">
        <div class="section-title">配置预览</div>
        <div class="preview-content">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="变量名">
              <el-tag size="small">{{ configData.key }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="数据类型">
              <el-tag :type="getTypeTagType(configData.type)" size="small">
                {{ getTypeDisplay(configData.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="字节范围" v-if="needsByteConfig">
              <code>{{ configData.fromByte }}-{{ configData.toByte }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="字节序" v-if="needsByteConfig">
              {{ configData.byteorder === 'big' ? '大端序' : '小端序' }}
            </el-descriptions-item>
            <el-descriptions-item label="有符号" v-if="needsByteConfig">
              {{ configData.signed ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item label="数据乘数">
              {{ configData.multiplier || 1 }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      key: '',
      type: 'float',
      fromByte: 0,
      toByte: 4,
      byteorder: 'big',
      signed: true,
      multiplier: 1
    })
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

const bitConfigEnabled = ref(false)

// 配置数据
const configData = computed({
  get: () => ({
    key: '',
    type: 'float',
    fromByte: 0,
    toByte: 4,
    byteorder: 'big',
    signed: true,
    multiplier: 1,
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 是否需要字节配置
const needsByteConfig = computed(() => {
  return ['float', 'int', 'long', 'double', 'byte'].includes(configData.value.type)
})

// 获取数据类型显示名称
const getTypeDisplay = (type) => {
  const typeMap = {
    'float': '浮点数',
    'int': '整数',
    'long': '长整数',
    'double': '双精度',
    'boolean': '布尔值',
    'string': '字符串',
    'byte': '字节'
  }
  return typeMap[type] || type
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  const typeTagMap = {
    'float': 'warning',
    'int': 'primary',
    'long': 'primary',
    'double': 'warning',
    'boolean': 'success',
    'string': 'info',
    'byte': 'danger'
  }
  return typeTagMap[type] || 'info'
}

// 数据类型变化处理
const handleTypeChange = () => {
  const newConfig = { ...configData.value }
  
  // 根据数据类型设置默认值
  switch (newConfig.type) {
    case 'float':
    case 'double':
      newConfig.fromByte = 0
      newConfig.toByte = 4
      newConfig.signed = true
      break
    case 'int':
      newConfig.fromByte = 0
      newConfig.toByte = 4
      newConfig.signed = true
      break
    case 'long':
      newConfig.fromByte = 0
      newConfig.toByte = 8
      newConfig.signed = true
      break
    case 'boolean':
      newConfig.fromByte = 0
      newConfig.toByte = 1
      newConfig.signed = false
      break
    case 'byte':
      newConfig.fromByte = 0
      newConfig.toByte = 1
      newConfig.signed = false
      break
    case 'string':
      newConfig.encoding = 'utf-8'
      newConfig.length = 10
      newConfig.trim = true
      break
  }
  
  updateValue(newConfig)
}

// 切换位配置
const toggleBitConfig = (enabled) => {
  const newConfig = { ...configData.value }
  
  if (enabled) {
    newConfig.byteAddress = 0
    newConfig.fromBit = 0
    newConfig.toBit = 7
  } else {
    delete newConfig.byteAddress
    delete newConfig.fromBit
    delete newConfig.toBit
  }
  
  updateValue(newConfig)
}

// 更新值
const updateValue = (customConfig = null) => {
  const config = customConfig || { ...configData.value }
  emit('update:modelValue', config)
}

// 处理删除
const handleDelete = () => {
  emit('delete')
}

// 初始化位配置状态
watch(() => props.modelValue, (newValue) => {
  bitConfigEnabled.value = !!(newValue.byteAddress !== undefined || newValue.fromBit !== undefined)
}, { immediate: true })
</script>

<style lang="scss" scoped>
.extension-config-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  
  &:hover {
    border-color: #c0c4cc;
  }
  
  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .config-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .config-name {
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .config-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .preview-content {
    code {
      background: #f5f7fa;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
      color: #e6a23c;
      font-size: 12px;
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f2f5;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style> 