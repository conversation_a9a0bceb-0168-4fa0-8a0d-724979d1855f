<template>
  <div class="rest-http-headers">
    <div class="headers-list">
      <div 
        v-for="(value, key, index) in headers" 
        :key="index" 
        class="header-item"
      >
        <el-row :gutter="12" align="middle">
          <el-col :span="10">
            <el-input 
              :model-value="getHeader<PERSON>ey(key)"
              @update:model-value="updateHeader<PERSON>ey(key, $event)"
              placeholder="Header名称"
              size="small"
            />
          </el-col>
          <el-col :span="12">
            <el-input 
              v-model="headers[key]" 
              placeholder="Header值"
              @input="updateValue"
              size="small"
            />
          </el-col>
          <el-col :span="2">
            <el-button 
              type="danger" 
              size="small" 
              :icon="Delete" 
              @click="removeHeader(key)"
              circle
            />
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="header-actions">
      <el-row :gutter="12">
        <el-col :span="20">
          <el-button 
            type="primary" 
            size="small" 
            :icon="Plus" 
            @click="addHeader"
          >
            添加HTTP头
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-dropdown @command="addCommonHeader" trigger="click">
            <el-button size="small" type="info">
              常用头
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="Content-Type: application/json">Content-Type: JSON</el-dropdown-item>
                <el-dropdown-item command="Content-Type: application/xml">Content-Type: XML</el-dropdown-item>
                <el-dropdown-item command="Content-Type: text/plain">Content-Type: Text</el-dropdown-item>
                <el-dropdown-item command="Accept: application/json">Accept: JSON</el-dropdown-item>
                <el-dropdown-item command="Authorization: Bearer token">Authorization: Bearer</el-dropdown-item>
                <el-dropdown-item command="User-Agent: IoTCloud-Gateway">User-Agent</el-dropdown-item>
                <el-dropdown-item command="Cache-Control: no-cache">Cache-Control</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-col>
      </el-row>
    </div>

    <!-- 配置预览 -->
    <div v-if="Object.keys(headers).length > 0" class="headers-preview">
      <div class="preview-title">HTTP头预览</div>
      <div class="preview-content">
        <pre><code>{{ JSON.stringify(headers, null, 2) }}</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus, Delete, ArrowDown } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      'Content-Type': 'application/json'
    })
  }
})

const emit = defineEmits(['update:modelValue'])

// 管理可编辑的header键名
const editableHeaders = ref({})

// Headers数据
const headers = computed({
  get: () => ({ ...props.modelValue }),
  set: (value) => emit('update:modelValue', value)
})

// 初始化可编辑headers
watch(() => props.modelValue, (newValue) => {
  Object.keys(newValue).forEach(key => {
    if (!editableHeaders.value[key]) {
      editableHeaders.value[key] = { key }
    }
  })
}, { immediate: true })

// 添加新的header
const addHeader = () => {
  const newHeaders = { ...headers.value }
  let headerName = 'X-Custom-Header'
  let counter = 1
  
  // 避免重复的header名称
  while (newHeaders[headerName]) {
    headerName = `X-Custom-Header-${counter}`
    counter++
  }
  
  newHeaders[headerName] = ''
  editableHeaders.value[headerName] = { key: headerName }
  
  emit('update:modelValue', newHeaders)
}

// 删除header
const removeHeader = (key) => {
  const newHeaders = { ...headers.value }
  delete newHeaders[key]
  delete editableHeaders.value[key]
  
  emit('update:modelValue', newHeaders)
}

// 更新header键名
const updateHeaderKey = (oldKey, newKey) => {
  if (oldKey === newKey) return
  
  const newHeaders = { ...headers.value }
  const value = newHeaders[oldKey]
  
  // 检查新键名是否已存在
  if (newHeaders[newKey] !== undefined) {
    // 如果已存在，恢复原来的键名
    editableHeaders.value[oldKey].key = oldKey
    return
  }
  
  // 删除旧键，添加新键
  delete newHeaders[oldKey]
  newHeaders[newKey] = value
  
  // 更新editableHeaders
  delete editableHeaders.value[oldKey]
  editableHeaders.value[newKey] = { key: newKey }
  
  emit('update:modelValue', newHeaders)
}

// 添加常用header
const addCommonHeader = (command) => {
  const [headerName, headerValue] = command.split(': ')
  const newHeaders = { ...headers.value }
  
  newHeaders[headerName] = headerValue
  editableHeaders.value[headerName] = { key: headerName }
  
  emit('update:modelValue', newHeaders)
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...headers.value })
}

// 获取header键名
const getHeaderKey = (key) => {
  return editableHeaders.value[key]?.key || key
}
</script>

<style lang="scss" scoped>
.rest-http-headers {
  .headers-list {
    .header-item {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .header-actions {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }

  .headers-preview {
    margin-top: 16px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    
    .preview-title {
      font-size: 12px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 8px;
    }
    
    .preview-content {
      pre {
        margin: 0;
        padding: 8px;
        background: #fff;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        overflow-x: auto;
        
        code {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
          font-size: 12px;
          line-height: 1.4;
          color: #24292e;
        }
      }
    }
  }
}

:deep(.el-input) {
  .el-input__wrapper {
    font-size: 13px;
  }
}

:deep(.el-button) {
  &.is-circle {
    padding: 6px;
  }
}

:deep(.el-dropdown) {
  .el-button {
    width: 100%;
  }
}
</style> 