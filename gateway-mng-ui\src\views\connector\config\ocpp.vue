<template>
  <div class="ocpp-config">
    <el-tabs v-model="activeTab" class="ocpp-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- 中央系统配置标签页 -->
      <el-tab-pane label="中央系统配置" name="centralSystem">
        <OcppCentralSystemConfig 
          v-model="config.centralSystem" 
        />
      </el-tab-pane>

      <!-- 充电桩配置标签页 -->
      <el-tab-pane label="充电桩配置" name="chargePoints">
        <OcppChargePointsConfig 
          v-model="config.chargePoints" 
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import OcppCentralSystemConfig from './components/OcppCentralSystemConfig.vue'
import OcppChargePointsConfig from './components/OcppChargePointsConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  centralSystem: {
    name: 'Central System',
    host: '127.0.0.1',
    port: 9000,
    connection: {
      type: 'insecure'
    },
    security: []
  },
  chargePoints: []
}

// OCPP 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'ocpp', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
        
      console.log('OCPP 连接器配置初始化成功')
    } else {
      ElMessage.warning('OCPP 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('OCPP 连接器初始化失败:', error)
    ElMessage.error('OCPP 连接器初始化失败')
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  ocpp: config
})
</script>

<style lang="scss" scoped>
.ocpp-config {
  .ocpp-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
}
</style>