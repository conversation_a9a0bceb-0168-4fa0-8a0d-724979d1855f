#!/bin/bash
#服务使能脚本
cd /home/<USER>/

chmod a+x thingsboard-gateway
chmod a+x tb-gateway-configurator
chmod a+x tb-gateway-shell

cp thingsboard-gateway ./venv/bin/
cp tb-gateway-configurator ./venv/bin/
cp tb-gateway-shell ./venv/bin/

cp gateway_mng_api.service /etc/systemd/system/
cp gateway.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable gateway_mng_api.service
systemctl start gateway_mng_api.service
systemctl enable gateway.service
systemctl start gateway.service
