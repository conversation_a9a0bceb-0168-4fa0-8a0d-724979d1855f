<template>
  <div class="can-data-points">
    <div class="config-section">
      <div class="section-header">
        <h4>{{ title }}</h4>
        <p class="section-description">{{ description }}</p>
        <el-button type="primary" @click="handleAddDataPoint">
          <el-icon><Plus /></el-icon>添加{{ getDataTypeLabel() }}
        </el-button>
      </div>
      
      <div v-if="dataPoints.length === 0" class="empty-state">
        <el-empty :description="`暂无${getDataTypeLabel()}配置`">
          <el-button type="primary" @click="handleAddDataPoint">
            添加第一个{{ getDataTypeLabel() }}
          </el-button>
        </el-empty>
      </div>
      
      <div v-else class="data-points-list">
        <el-card 
          v-for="(point, index) in dataPoints" 
          :key="index" 
          class="data-point-card"
          shadow="hover"
        >
          <template #header>
            <div class="card-header">
              <span class="card-title">
                {{ getDataTypeLabel() }} {{ index + 1 }}: {{ getPointDisplayName(point) }}
              </span>
              <el-button type="danger" text @click="handleRemoveDataPoint(index)">
                <el-icon><Delete /></el-icon>删除
              </el-button>
            </div>
          </template>
          
          <!-- 不同数据类型的表单内容 -->
          <div v-if="dataType === 'attributes' || dataType === 'timeseries'">
            <!-- 属性和遥测数据配置 -->
            <el-form :model="point" label-width="120px" size="small">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="数据键名">
                    <el-input v-model="point.key" placeholder="rpm" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="节点ID">
                    <el-input-number 
                      v-model="point.nodeId" 
                      :min="0" 
                      :max="0x1FFFFFFF"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="扩展ID">
                    <el-switch v-model="point.isExtendedId" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="命令格式">
                    <el-input v-model="point.command" placeholder="2:2:big:8717" />
                    <div class="field-hint">格式: 起始位:长度:字节序:数据类型</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="值格式">
                    <el-input v-model="point.value" placeholder="4:2:big:int" />
                    <div class="field-hint">格式: 起始位:长度:字节序:数据类型</div>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="表达式">
                    <el-input v-model="point.expression" placeholder="value / 4" />
                    <div class="field-hint">数据处理表达式，如 value / 4 或 bool(value & 0b00000100)</div>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <!-- 轮询配置 -->
              <div class="polling-config">
                <h5>轮询配置</h5>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="轮询类型">
                      <el-select v-model="point.polling.type" style="width: 100%">
                        <el-option label="仅一次" value="once" />
                        <el-option label="始终轮询" value="always" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="point.polling.type === 'always'">
                    <el-form-item label="轮询周期">
                      <el-input-number 
                        v-model="point.polling.period" 
                        :min="1" 
                        controls-position="right"
                        style="width: 100%"
                      />
                      <span class="unit-suffix">秒</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item label="数据(Hex)">
                      <el-input v-model="point.polling.dataInHex" placeholder="AA BB CC DD EE FF" />
                      <div class="field-hint">发送的十六进制数据，用空格分隔</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-form>
          </div>
          
          <div v-else-if="dataType === 'attributeUpdates'">
            <!-- 属性更新配置 -->
            <el-form :model="point" label-width="120px" size="small">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="IoTCloud属性">
                    <el-input v-model="point.attributeOnThingsBoard" placeholder="softwareVersion" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="节点ID">
                    <el-input-number 
                      v-model="point.nodeId" 
                      :min="0" 
                      :max="0x1FFFFFFF"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="扩展ID">
                    <el-switch v-model="point.isExtendedId" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="数据长度">
                    <el-input-number 
                      v-model="point.dataLength" 
                      :min="1" 
                      :max="64"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="字节序">
                    <el-select v-model="point.dataByteorder" style="width: 100%">
                      <el-option label="大端" value="big" />
                      <el-option label="小端" value="little" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="数据表达式">
                    <el-input v-model="point.dataExpression" placeholder="value + 5" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          
          <div v-else-if="dataType === 'serverSideRpc'">
            <!-- RPC方法配置 -->
            <el-form :model="point" label-width="120px" size="small">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="方法名">
                    <el-input v-model="point.method" placeholder="setLightLevel" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="节点ID">
                    <el-input-number 
                      v-model="point.nodeId" 
                      :min="0" 
                      :max="0x1FFFFFFF"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="扩展ID">
                    <el-switch v-model="point.isExtendedId" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="CAN FD">
                    <el-switch v-model="point.isFd" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="比特率切换">
                    <el-switch v-model="point.bitrateSwitch" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="数据长度">
                    <el-input-number 
                      v-model="point.dataLength" 
                      :min="1" 
                      :max="64"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="字节序">
                    <el-select v-model="point.dataByteorder" style="width: 100%">
                      <el-option label="大端" value="big" />
                      <el-option label="小端" value="little" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="数据(Hex)">
                    <el-input v-model="point.dataInHex" placeholder="AA BB CC DD" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="前置数据">
                    <el-input v-model="point.dataBefore" placeholder="00AA" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="后置数据">
                    <el-input v-model="point.dataAfter" placeholder="0102" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="数据表达式">
                    <el-input v-model="point.dataExpression" placeholder="userSpeed if maxAllowedSpeed > userSpeed else maxAllowedSpeed" />
                    <div class="field-hint">数据处理表达式，用于动态计算发送数据</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries', 'attributeUpdates', 'serverSideRpc'].includes(value)
  },
  title: {
    type: String,
    default: '数据配置'
  },
  description: {
    type: String,
    default: '配置数据点信息'
  }
})

const emit = defineEmits(['update:modelValue'])

const dataPoints = ref([...props.modelValue])

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  dataPoints.value = [...newValue]
}, { deep: true, immediate: true })

// 监听内部数据变化
watch(dataPoints, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

// 获取数据类型标签
const getDataTypeLabel = () => {
  const labels = {
    attributes: '属性',
    timeseries: '遥测',
    attributeUpdates: '属性更新',
    serverSideRpc: 'RPC方法'
  }
  return labels[props.dataType] || '数据点'
}

// 获取数据点显示名称
const getPointDisplayName = (point) => {
  if (props.dataType === 'attributes' || props.dataType === 'timeseries') {
    return point.key || '未命名'
  } else if (props.dataType === 'attributeUpdates') {
    return point.attributeOnThingsBoard || '未命名'
  } else if (props.dataType === 'serverSideRpc') {
    return point.method || '未命名'
  }
  return '未命名'
}

// 创建默认数据点
const createDefaultDataPoint = () => {
  if (props.dataType === 'attributes' || props.dataType === 'timeseries') {
    return {
      key: '',
      nodeId: 0,
      isExtendedId: false,
      command: '',
      value: '',
      expression: '',
      polling: {
        type: 'always',
        period: 5,
        dataInHex: ''
      }
    }
  } else if (props.dataType === 'attributeUpdates') {
    return {
      attributeOnThingsBoard: '',
      nodeId: 0,
      isExtendedId: false,
      dataLength: 4,
      dataExpression: '',
      dataByteorder: 'little'
    }
  } else if (props.dataType === 'serverSideRpc') {
    return {
      method: '',
      nodeId: 0,
      isExtendedId: false,
      isFd: false,
      bitrateSwitch: false,
      dataLength: 8,
      dataByteorder: 'big',
      dataInHex: '',
      dataBefore: '',
      dataAfter: '',
      dataExpression: ''
    }
  }
  return {}
}

// 数据点操作
const handleAddDataPoint = () => {
  dataPoints.value.push(createDefaultDataPoint())
}

const handleRemoveDataPoint = (index) => {
  dataPoints.value.splice(index, 1)
}
</script>

<style lang="scss" scoped>
.can-data-points {
  .config-section {
    .section-header {
      margin-bottom: 20px;
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .section-description {
        margin: 0 0 16px 0;
        font-size: 14px;
        color: #606266;
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 40px 0;
    }
  }
  
  .data-points-list {
    .data-point-card {
      margin-bottom: 16px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .card-title {
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .unit-suffix {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
  }
  
  .polling-config {
    margin-top: 20px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #fafafa;
    
    h5 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  :deep(.el-card__header) {
    padding: 12px 20px;
    background: #f5f7fa;
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style> 