<template>
  <div class="mqtt-timeseries-config">
    <el-divider content-position="left">
      <div class="section-header">
        <span>{{ title }}</span>
        <el-button type="primary" size="small" @click="addTimeseries">
          <el-icon><Plus /></el-icon>
          添加时序数据
        </el-button>
      </div>
    </el-divider>
    
    <div v-if="timeseries.length === 0" class="empty-state">
      <el-empty :description="`暂无${title}配置`" :image-size="80">
        <el-button type="primary" @click="addTimeseries">添加第一个时序数据</el-button>
      </el-empty>
    </div>
    
    <div v-else class="timeseries-list">
      <div 
        v-for="(ts, index) in timeseries" 
        :key="index"
        class="timeseries-item"
      >
        <el-card shadow="hover" size="small">
          <template #header>
            <div class="timeseries-header">
              <div class="timeseries-info">
                <span class="timeseries-name">{{ ts.key || `时序数据 ${index + 1}` }}</span>
                <el-tag v-if="ts.type" :type="getTypeColor(ts.type)" size="small">
                  {{ getTypeDisplay(ts.type) }}
                </el-tag>
              </div>
              <el-button 
                type="danger" 
                size="small" 
                link
                @click="removeTimeseries(index)"
              >
                <el-icon><Delete /></el-icon>删除
              </el-button>
            </div>
          </template>
          
          <el-form :model="ts" label-width="100px" size="small">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="时序键" required>
                  <el-input v-model="ts.key" placeholder="temperature" />
                  <div class="field-hint">时序数据的唯一标识符</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数据类型" required>
                  <el-select v-model="ts.type" style="width: 100%">
                    <el-option label="浮点数" value="double" />
                    <el-option label="整数" value="int" />
                    <el-option label="长整数" value="long" />
                    <el-option label="字符串" value="string" />
                    <el-option label="布尔值" value="bool" />
                    <el-option label="JSON" value="json" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="值表达式" required>
                  <el-input v-model="ts.value" placeholder="${temperature}" />
                  <div class="field-hint">从消息中提取值的表达式</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    
    <div v-if="description" class="description">
      <p>{{ description }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: '时序数据配置'
  },
  description: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

// 标准的isInternalUpdate模式
const isInternalUpdate = ref(false)

const timeseries = ref([])

// 获取数据类型显示名称
const getTypeDisplay = (type) => {
  const typeMap = {
    double: '浮点数',
    int: '整数',
    long: '长整数',
    string: '字符串',
    bool: '布尔值',
    json: 'JSON'
  }
  return typeMap[type] || type
}

// 获取数据类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    double: 'warning',
    int: 'success',
    long: 'success',
    string: '',
    bool: 'info',
    json: 'info'    // 改用info而不是primary
  }
  return colorMap[type] || ''
}

// 创建默认时序数据
const createDefaultTimeseries = () => {
  return {
    key: '',
    type: 'double',
    value: ''
  }
}

// 添加时序数据
const addTimeseries = () => {
  const newTimeseries = createDefaultTimeseries()
  timeseries.value.push(newTimeseries)
  ElMessage.success('时序数据添加成功')
}

// 移除时序数据
const removeTimeseries = (index) => {
  timeseries.value.splice(index, 1)
  ElMessage.success('时序数据删除成功')
}

// 监听props变化 - 标准模式
watch(() => props.modelValue, (newValue) => {
  if (isInternalUpdate.value) return
  
  timeseries.value = [...(newValue || [])]
}, { deep: true, immediate: true })

// 监听时序数据变化 - 标准模式
watch(timeseries, (newTimeseries) => {
  isInternalUpdate.value = true
  nextTick(() => {
    isInternalUpdate.value = false
  })
  
  emit('update:modelValue', [...newTimeseries])
}, { deep: true })
</script>

<style lang="scss" scoped>
.mqtt-timeseries-config {
  margin-top: 16px;

  .section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #303133;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }

  .timeseries-list {
    margin-top: 16px;
    
    .timeseries-item {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .timeseries-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .timeseries-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .timeseries-name {
            font-weight: 600;
            color: #303133;
          }
        }
      }
    }
  }

  .description {
    margin-top: 16px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    
    p {
      margin: 0;
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
    }
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
}

:deep(.el-divider__text) {
  background: #fff;
  padding: 0 16px;
}

:deep(.el-form-item) {
  margin-bottom: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background: #fafafa;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style> 