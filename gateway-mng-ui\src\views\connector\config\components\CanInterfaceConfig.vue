<template>
  <div class="can-interface-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>CAN接口配置</span>
          <el-tooltip content="配置CAN总线的接口类型、通道和后端参数" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="interfaceConfig" :rules="rules" label-width="140px" ref="formRef">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="接口类型" prop="interface" required>
              <el-select 
                v-model="interfaceConfig.interface" 
                placeholder="请选择接口类型"
                @change="handleInterfaceChange"
              >
                <el-option 
                  v-for="item in interfaceOptions" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value"
                />
              </el-select>
              <div class="field-hint">{{ getInterfaceHint(interfaceConfig.interface) }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通道名称" prop="channel" required>
              <el-input 
                v-model="interfaceConfig.channel" 
                placeholder="vcan0"
                clearable
                
              />
              <div class="field-hint">CAN设备的通道标识符</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重连周期" prop="reconnectPeriod">
              <el-input-number 
                v-model="interfaceConfig.reconnectPeriod" 
                :min="1" 
                :step="1"
                controls-position="right"
                placeholder="5"
                style="width: 100%"
                
              />
              <span class="unit-suffix">秒</span>
              <div class="field-hint">连接断开后的重新连接等待时间</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 后端配置 -->
    <el-card class="config-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>后端配置</span>
          <el-tooltip content="根据选择的接口类型配置相应的后端参数" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <CanBackendConfig 
        :interface-type="interfaceConfig.interface"
        v-model="interfaceConfig.backend"
        
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick, defineEmits, defineProps } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import CanBackendConfig from './CanBackendConfig.vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: 'socketcan'
  },
  channel: {
    type: String,
    default: 'vcan0'
  },
  backend: {
    type: Object,
    default: () => ({ fd: true })
  },
  reconnectPeriod: {
    type: Number,
    default: 5
  }
})

const emit = defineEmits(['update:modelValue', 'update:channel', 'update:backend', 'update:reconnectPeriod'])

const formRef = ref()

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 确保数值字段为数字类型
const ensureNumberTypes = (config) => {
  const result = { ...config }
  if (result.reconnectPeriod !== undefined) {
    result.reconnectPeriod = typeof result.reconnectPeriod === 'string' ? parseInt(result.reconnectPeriod) || 5 : result.reconnectPeriod
  }
  return result
}

// 接口配置数据
const interfaceConfig = reactive({
  interface: 'socketcan',
  channel: 'vcan0',
  backend: { fd: true },
  reconnectPeriod: 5
})

// CAN接口选项
const interfaceOptions = ref([
  { value: 'socketcan', label: 'Socket CAN' },
  { value: 'canalystii', label: 'CANalyst-II' },
  { value: 'cantact', label: 'CANtact' },
  { value: 'etas', label: 'ETAS' },
  { value: 'gs_usb', label: 'GS USB' },
  { value: 'iscan', label: 'iSCAN' },
  { value: 'ixxat', label: 'IXXAT' },
  { value: 'kvaser', label: 'Kvaser' },
  { value: 'neousys', label: 'Neousys' },
  { value: 'neovi', label: 'NeoVI' },
  { value: 'nican', label: 'NI-CAN' },
  { value: 'nixnet', label: 'NI-XNET' },
  { value: 'pcan', label: 'PCAN' },
  { value: 'robotell', label: 'Robotell' },
  { value: 'seeedstudio', label: 'Seeed Studio' },
  { value: 'serial', label: 'Serial' },
  { value: 'slcan', label: 'SLCAN' },
  { value: 'socketcand', label: 'Socket CANd' },
  { value: 'systec', label: 'SYS TEC' },
  { value: 'udp_multicast', label: 'UDP Multicast' },
  { value: 'usb2can', label: 'USB2CAN' },
  { value: 'vector', label: 'Vector' },
  { value: 'virtual', label: 'Virtual' }
])

// 表单验证规则
const rules = reactive({
  interface: [
    { required: true, message: '请选择接口类型', trigger: 'change' }
  ],
  channel: [
    { required: true, message: '请输入通道名称', trigger: 'blur' }
  ],
  reconnectPeriod: [
    { required: true, message: '请输入重连周期', trigger: 'blur' },
    { type: 'number', min: 1, message: '重连周期不能小于1秒', trigger: 'blur' }
  ]
})

// 获取接口提示信息
const getInterfaceHint = (interfaceType) => {
  const hints = {
    socketcan: 'Linux SocketCAN接口，最常用的CAN接口',
    canalystii: 'CANalyst-II USB-CAN适配器',
    cantact: 'CANtact开源CAN接口',
    kvaser: 'Kvaser公司的CAN接口卡',
    pcan: 'PEAK System的PCAN接口',
    vector: 'Vector公司的CAN接口',
    virtual: '虚拟CAN接口，用于测试',
    serial: '串口CAN接口'
  }
  return hints[interfaceType] || '选择相应的CAN接口类型'
}

// 处理接口类型变化
const handleInterfaceChange = (interfaceType) => {
  // 根据接口类型设置默认后端配置
  const defaultBackends = {
    socketcan: { fd: true },
    canalystii: { 
      device: 0, 
      kwargs: {}, 
      unique_hardware_id: 0, 
      serial: 'COM1', 
      tx_buffer_entries: 4096, 
      rx_buffer_entries: 4096, 
      dll: 'usb2can.dll' 
    },
    kvaser: { channel: 0, bitrate: 500000 },
    pcan: { device: 'PCAN_USBBUS1', bitrate: 500000 },
    vector: { app_name: 'CANoe', channel: 0, bitrate: 500000 },
    virtual: {},
    serial: { port: '/dev/ttyUSB0', baudrate: 115200 }
  }
  
  interfaceConfig.backend = defaultBackends[interfaceType] || {}
  handleChange()
}

// 处理配置变化
const handleChange = () => {
  emit('update:modelValue', interfaceConfig.interface)
  emit('update:channel', interfaceConfig.channel)
  emit('update:backend', { ...interfaceConfig.backend })
  emit('update:reconnectPeriod', interfaceConfig.reconnectPeriod)
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value) {
    interfaceConfig.interface = newValue
  }
}, { immediate: true })

watch(() => props.channel, (newValue) => {
  if (!isInternalUpdate.value) {
    interfaceConfig.channel = newValue
  }
}, { immediate: true })

watch(() => props.backend, (newValue) => {
  if (!isInternalUpdate.value) {
    Object.assign(interfaceConfig.backend, newValue)
  }
}, { deep: true, immediate: true })

watch(() => props.reconnectPeriod, (newValue) => {
  if (!isInternalUpdate.value) {
    const normalizedValue = typeof newValue === 'string' ? parseInt(newValue) || 5 : newValue
    interfaceConfig.reconnectPeriod = normalizedValue
  }
}, { immediate: true })

// 监听内部配置变化
watch(interfaceConfig, (newValue) => {
  isInternalUpdate.value = true
  emit('update:modelValue', newValue.interface)
  emit('update:channel', newValue.channel)
  emit('update:backend', { ...newValue.backend })
  emit('update:reconnectPeriod', newValue.reconnectPeriod)
  nextTick(() => {
    isInternalUpdate.value = false
  })
}, { deep: true })
</script>

<style lang="scss" scoped>
.can-interface-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .unit-suffix {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-select) {
    width: 100%;
  }
}
</style> 