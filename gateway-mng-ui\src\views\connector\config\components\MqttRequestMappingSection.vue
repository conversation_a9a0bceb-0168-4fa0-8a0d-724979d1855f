<template>
  <div class="mqtt-request-mapping-section">
    <div class="section-header">
      <div class="header-info">
        <h4>{{ title }}</h4>
        <p class="description">{{ description }}</p>
      </div>
      <el-button type="primary" size="small" @click="addRequest">
        <el-icon><Plus /></el-icon>
        添加请求
      </el-button>
    </div>

    <div v-if="requests.length === 0" class="empty-state">
      <el-empty :description="`暂无${title}配置`">
        <el-button type="primary" @click="addRequest">添加第一个请求</el-button>
      </el-empty>
    </div>

    <div v-else class="requests-list">
      <div 
        v-for="(request, index) in requests" 
        :key="index"
        class="request-item"
      >
        <el-card shadow="hover">
          <template #header>
            <div class="request-header">
              <div class="request-info">
                <span class="request-name">{{ getRequestDisplayName(request, index) }}</span>
                <el-tag v-if="request.type" :type="getRequestTypeColor(request.type)" size="small">
                  {{ request.type }}
                </el-tag>
              </div>
              <el-button 
                type="danger" 
                size="small" 
                link
                @click="removeRequest(index)"
              >
                <el-icon><Delete /></el-icon>删除
              </el-button>
            </div>
          </template>
          
          <el-form :model="request" label-width="140px" size="small">
            <!-- connectRequests和disconnectRequests配置 -->
            <div v-if="requestType === 'connectRequests' || requestType === 'disconnectRequests'">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="主题过滤器" required>
                    <el-input 
                      v-model="request.topicFilter" 
                      :placeholder="requestType === 'connectRequests' ? 'sensor/connect' : 'sensor/disconnect'"
                    />
                    <div class="field-hint">MQTT主题过滤器，支持通配符 + 和 #</div>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <!-- 设备信息配置 -->
              <el-divider content-position="left">设备信息配置</el-divider>
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="设备名称来源">
                    <el-select v-model="request.deviceInfo.deviceNameExpressionSource" style="width: 100%">
                      <el-option label="消息内容" value="message" />
                      <el-option label="主题" value="topic" />
                      <el-option label="常量" value="constant" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备名称表达式" required>
                    <el-input v-model="request.deviceInfo.deviceNameExpression" placeholder="${serialNumber}" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备类型来源">
                    <el-select v-model="request.deviceInfo.deviceProfileExpressionSource" style="width: 100%">
                      <el-option label="消息内容" value="message" />
                      <el-option label="主题" value="topic" />
                      <el-option label="常量" value="constant" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="设备类型表达式">
                    <el-input v-model="request.deviceInfo.deviceProfileExpression" placeholder="Thermometer" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- attributeRequests配置 -->
            <div v-if="requestType === 'attributeRequests'">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="主题过滤器" required>
                    <el-input v-model="request.topicFilter" placeholder="v1/devices/me/attributes/request" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="保留消息">
                    <el-switch v-model="request.retain" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="主题表达式">
                    <el-input v-model="request.topicExpression" placeholder="devices/${deviceName}/attrs" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="属性名表达式来源">
                    <el-select v-model="request.attributeNameExpressionSource" style="width: 100%">
                      <el-option label="消息内容" value="message" />
                      <el-option label="主题" value="topic" />
                      <el-option label="常量" value="constant" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="属性名表达式">
                    <el-input v-model="request.attributeNameExpression" placeholder="${versionAttribute}, ${pduAttribute}" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="值表达式">
                    <el-input v-model="request.valueExpression" placeholder="${attributeKey}: ${attributeValue}" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <!-- 设备信息配置 -->
              <el-divider content-position="left">设备信息配置</el-divider>
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="设备名称来源">
                    <el-select v-model="request.deviceInfo.deviceNameExpressionSource" style="width: 100%">
                      <el-option label="消息内容" value="message" />
                      <el-option label="主题" value="topic" />
                      <el-option label="常量" value="constant" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备名称表达式" required>
                    <el-input v-model="request.deviceInfo.deviceNameExpression" placeholder="${serialNumber}" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备类型来源">
                    <el-select v-model="request.deviceInfo.deviceProfileExpressionSource" style="width: 100%">
                      <el-option label="消息内容" value="message" />
                      <el-option label="主题" value="topic" />
                      <el-option label="常量" value="constant" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="设备类型表达式">
                    <el-input v-model="request.deviceInfo.deviceProfileExpression" placeholder="Thermometer" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- attributeUpdates配置 -->
            <div v-if="requestType === 'attributeUpdates'">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="设备名过滤器" required>
                    <el-input v-model="request.deviceNameFilter" placeholder=".*" />
                    <div class="field-hint">正则表达式匹配设备名称</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="属性过滤器" required>
                    <el-input v-model="request.attributeFilter" placeholder="firmwareVersion" />
                    <div class="field-hint">要更新的属性名称</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="保留消息">
                    <el-switch v-model="request.retain" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="主题表达式" required>
                    <el-input v-model="request.topicExpression" placeholder="sensor/${deviceName}/${attributeKey}" />
                    <div class="field-hint">发布属性更新的主题模板</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="值表达式" required>
                    <el-input v-model="request.valueExpression" placeholder='{&quot;${attributeKey}&quot;:&quot;${attributeValue}&quot;}' />
                    <div class="field-hint">属性值的格式模板</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- serverSideRpc配置 -->
            <div v-if="requestType === 'serverSideRpc'">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="RPC类型" required>
                    <el-select v-model="request.type" style="width: 100%">
                      <el-option label="双向RPC" value="twoWay" />
                      <el-option label="单向RPC" value="oneWay" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备名过滤器" required>
                    <el-input v-model="request.deviceNameFilter" placeholder=".*" />
                    <div class="field-hint">正则表达式匹配设备名称</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="方法过滤器" required>
                    <el-input v-model="request.methodFilter" placeholder="echo" />
                    <div class="field-hint">匹配的RPC方法名</div>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="请求主题表达式" required>
                    <el-input v-model="request.requestTopicExpression" placeholder="sensor/${deviceName}/request/${methodName}/${requestId}" />
                    <div class="field-hint">发送RPC请求的主题模板</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12" v-if="request.type === 'twoWay'">
                  <el-form-item label="响应主题表达式">
                    <el-input v-model="request.responseTopicExpression" placeholder="sensor/${deviceName}/response/${methodName}/${requestId}" />
                    <div class="field-hint">接收RPC响应的主题模板</div>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16" v-if="request.type === 'twoWay'">
                <el-col :span="8">
                  <el-form-item label="响应QoS">
                    <el-select v-model="request.responseTopicQoS" style="width: 100%">
                      <el-option label="QoS 0" :value="0" />
                      <el-option label="QoS 1" :value="1" />
                      <el-option label="QoS 2" :value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="响应超时(ms)">
                    <el-input-number 
                      v-model="request.responseTimeout" 
                      :min="1000" 
                      :step="1000"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="值表达式">
                    <el-input v-model="request.valueExpression" placeholder="${params}" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="16" v-if="request.type === 'oneWay'">
                <el-col :span="12">
                  <el-form-item label="值表达式">
                    <el-input v-model="request.valueExpression" placeholder="${params}" />
                    <div class="field-hint">RPC参数的格式模板</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  requestType: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

// 标准的isInternalUpdate模式
const isInternalUpdate = ref(false)

const requests = ref([])

// 获取请求显示名称
const getRequestDisplayName = (request, index) => {
  if (request.topicFilter) {
    return `${request.topicFilter}`
  } else if (request.deviceNameFilter && request.methodFilter) {
    return `${request.methodFilter} (${request.deviceNameFilter})`
  } else if (request.deviceNameFilter && request.attributeFilter) {
    return `${request.attributeFilter} (${request.deviceNameFilter})`
  }
  return `请求 ${index + 1}`
}

// 获取请求类型颜色
const getRequestTypeColor = (type) => {
  const colorMap = {
    twoWay: 'info',      // 双向RPC使用info类型
    oneWay: 'success'    // 单向RPC使用success类型
  }
  return colorMap[type] || ''
}

// 创建默认请求
const createDefaultRequest = () => {
  switch (props.requestType) {
    case 'connectRequests':
      return {
        topicFilter: 'sensor/connect',
        deviceInfo: {
          deviceNameExpressionSource: 'message',
          deviceNameExpression: '${serialNumber}',
          deviceProfileExpressionSource: 'constant',
          deviceProfileExpression: 'Thermometer'
        }
      }
    case 'disconnectRequests':
      return {
        topicFilter: 'sensor/disconnect',
        deviceInfo: {
          deviceNameExpressionSource: 'message',
          deviceNameExpression: '${serialNumber}',
          deviceProfileExpressionSource: 'constant',
          deviceProfileExpression: 'Thermometer'
        }
      }
    case 'attributeRequests':
      return {
        retain: false,
        topicFilter: 'v1/devices/me/attributes/request',
        deviceInfo: {
          deviceNameExpressionSource: 'message',
          deviceNameExpression: '${serialNumber}',
          deviceProfileExpressionSource: 'constant',
          deviceProfileExpression: 'Thermometer'
        },
        attributeNameExpressionSource: 'message',
        attributeNameExpression: '${versionAttribute}, ${pduAttribute}',
        topicExpression: 'devices/${deviceName}/attrs',
        valueExpression: '${attributeKey}: ${attributeValue}'
      }
    case 'attributeUpdates':
      return {
        retain: true,
        deviceNameFilter: '.*',
        attributeFilter: 'firmwareVersion',
        topicExpression: 'sensor/${deviceName}/${attributeKey}',
        valueExpression: '{"${attributeKey}":"${attributeValue}"}'
      }
    case 'serverSideRpc':
      return {
        type: 'twoWay',
        deviceNameFilter: '.*',
        methodFilter: 'echo',
        requestTopicExpression: 'sensor/${deviceName}/request/${methodName}/${requestId}',
        responseTopicExpression: 'sensor/${deviceName}/response/${methodName}/${requestId}',
        responseTopicQoS: 1,
        responseTimeout: 10000,
        valueExpression: '${params}'
      }
    default:
      return {}
  }
}

// 添加请求
const addRequest = () => {
  const newRequest = createDefaultRequest()
  requests.value.push(newRequest)
  ElMessage.success('请求添加成功')
}

// 移除请求
const removeRequest = (index) => {
  requests.value.splice(index, 1)
  ElMessage.success('请求删除成功')
}

// 监听props变化 - 标准模式
watch(() => props.modelValue, (newValue) => {
  if (isInternalUpdate.value) return
  
  requests.value = [...(newValue || [])]
}, { deep: true, immediate: true })

// 监听请求变化 - 标准模式
watch(requests, (newRequests) => {
  isInternalUpdate.value = true
  nextTick(() => {
    isInternalUpdate.value = false
  })
  
  emit('update:modelValue', [...newRequests])
}, { deep: true })
</script>

<style lang="scss" scoped>
.mqtt-request-mapping-section {
  padding: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .header-info {
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .description {
        margin: 0;
        font-size: 13px;
        color: #606266;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
  }

  .requests-list {
    .request-item {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .request-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .request-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .request-name {
            font-weight: 600;
            color: #303133;
            word-break: break-all;
          }
        }
      }
    }
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-divider__text) {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style> 