<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="dataPointForm" :rules="rules" ref="formRef" label-width="140px">
      <!-- 遥测数据配置 -->
      <template v-if="dataType === 'telemetry'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="变量名" prop="key" required>
              <el-input 
                v-model="dataPointForm.key" 
                placeholder="temperature"
              />
              <div class="field-hint">在IoTCloud中显示的变量名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理方法" prop="method" required>
              <el-select v-model="dataPointForm.method" placeholder="请选择处理方法">
                <el-option label="通知 (notify)" value="notify" />
                <el-option label="读取 (read)" value="read" />
              </el-select>
              <div class="field-hint">BLE特征值的读取方式</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="特征UUID" prop="characteristicUUID" required>
              <el-input 
                v-model="dataPointForm.characteristicUUID" 
                placeholder="226CAA55-**************-66734470666D"
              />
              <div class="field-hint">BLE设备特征的UUID标识符</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="值表达式" prop="valueExpression" required>
              <el-input 
                v-model="dataPointForm.valueExpression" 
                placeholder="[2]"
              />
              <div class="field-hint">数据解析表达式，如 [0] 表示第一个字节，[0:2] 表示前两个字节</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 属性数据配置 -->
      <template v-if="dataType === 'attributes'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="属性名" prop="key" required>
              <el-input 
                v-model="dataPointForm.key" 
                placeholder="name"
              />
              <div class="field-hint">在IoTCloud中显示的属性名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理方法" prop="method" required>
              <el-select v-model="dataPointForm.method" placeholder="请选择处理方法">
                <el-option label="通知 (notify)" value="notify" />
                <el-option label="读取 (read)" value="read" />
              </el-select>
              <div class="field-hint">BLE特征值的读取方式</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="特征UUID" prop="characteristicUUID" required>
              <el-input 
                v-model="dataPointForm.characteristicUUID" 
                placeholder="00002A00-0000-1000-8000-00805F9B34FB"
              />
              <div class="field-hint">BLE设备特征的UUID标识符</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="值表达式" prop="valueExpression" required>
              <el-input 
                v-model="dataPointForm.valueExpression" 
                placeholder="[0:2]cm [2:]A"
              />
              <div class="field-hint">数据解析表达式，支持字符串拼接和字节切片</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 属性更新配置 -->
      <template v-if="dataType === 'attributeUpdates'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="IoTCloud属性名" prop="attributeOnThingsBoard" required>
              <el-input 
                v-model="dataPointForm.attributeOnThingsBoard" 
                placeholder="sharedName"
              />
              <div class="field-hint">IoTCloud平台上的共享属性名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="特征UUID" prop="characteristicUUID" required>
              <el-input 
                v-model="dataPointForm.characteristicUUID" 
                placeholder="00002A00-0000-1000-8000-00805F9B34FB"
              />
              <div class="field-hint">要写入的BLE设备特征UUID</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- RPC配置 -->
      <template v-if="dataType === 'serverSideRpc'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="RPC方法名" prop="methodRPC" required>
              <el-input 
                v-model="dataPointForm.methodRPC" 
                placeholder="rpcMethod1"
              />
              <div class="field-hint">远程过程调用的方法名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理方法" prop="methodProcessing" required>
              <el-select v-model="dataPointForm.methodProcessing" placeholder="请选择处理方法">
                <el-option label="通知 (notify)" value="notify" />
                <el-option label="读取 (read)" value="read" />
                <el-option label="写入 (write)" value="write" />
                <el-option label="扫描 (scan)" value="scan" />
              </el-select>
              <div class="field-hint">RPC调用时的BLE操作类型</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否需要响应">
              <el-switch v-model="dataPointForm.withResponse" />
              <div class="field-hint">RPC调用是否需要返回响应数据</div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataPointForm.methodProcessing !== 'scan'">
            <el-form-item label="特征UUID" prop="characteristicUUID">
              <el-input 
                v-model="dataPointForm.characteristicUUID" 
                placeholder="00002A00-0000-1000-8000-00805F9B34FB"
              />
              <div class="field-hint">scan方法可选，其他方法必填</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dataPoint: {
    type: Object,
    default: null
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['telemetry', 'attributes', 'attributeUpdates', 'serverSideRpc'].includes(value)
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 数据点表单数据
const dataPointForm = reactive({
  // 通用字段
  key: '',
  method: 'read',
  characteristicUUID: '',
  valueExpression: '',
  
  // 属性更新特有字段
  attributeOnThingsBoard: '',
  
  // RPC特有字段
  methodRPC: '',
  withResponse: true,
  methodProcessing: 'read'
})

// 表单验证规则
const rules = computed(() => {
  const baseRules = {}
  
  if (props.dataType === 'telemetry' || props.dataType === 'attributes') {
    baseRules.key = [
      { required: true, message: '请输入变量名', trigger: 'blur' }
    ]
    baseRules.method = [
      { required: true, message: '请选择处理方法', trigger: 'change' }
    ]
    baseRules.characteristicUUID = [
      { required: true, message: '请输入特征UUID', trigger: 'blur' },
      { pattern: /^[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}$/, message: '请输入有效的UUID格式', trigger: 'blur' }
    ]
    baseRules.valueExpression = [
      { required: true, message: '请输入值表达式', trigger: 'blur' }
    ]
  } else if (props.dataType === 'attributeUpdates') {
    baseRules.attributeOnThingsBoard = [
      { required: true, message: '请输入IoTCloud属性名', trigger: 'blur' }
    ]
    baseRules.characteristicUUID = [
      { required: true, message: '请输入特征UUID', trigger: 'blur' },
      { pattern: /^[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}$/, message: '请输入有效的UUID格式', trigger: 'blur' }
    ]
  } else if (props.dataType === 'serverSideRpc') {
    baseRules.methodRPC = [
      { required: true, message: '请输入RPC方法名', trigger: 'blur' }
    ]
    baseRules.methodProcessing = [
      { required: true, message: '请选择处理方法', trigger: 'change' }
    ]
    
    // scan方法不需要UUID，其他方法需要
    if (dataPointForm.methodProcessing !== 'scan') {
      baseRules.characteristicUUID = [
        { required: true, message: '请输入特征UUID', trigger: 'blur' },
        { pattern: /^[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}$/, message: '请输入有效的UUID格式', trigger: 'blur' }
      ]
    }
  }
  
  return baseRules
})

// 获取对话框标题
const getDialogTitle = () => {
  const typeNames = {
    telemetry: '遥测数据',
    attributes: '属性数据',
    attributeUpdates: '属性更新',
    serverSideRpc: 'RPC方法'
  }
  const action = props.isEdit ? '编辑' : '添加'
  return `${action}${typeNames[props.dataType] || '数据点'}`
}

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.dataPoint) {
    // 加载数据点数据
    Object.assign(dataPointForm, getDefaultFormData(), props.dataPoint)
  } else if (newValue) {
    // 重置为默认值
    Object.assign(dataPointForm, getDefaultFormData())
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 获取默认表单数据
const getDefaultFormData = () => {
  switch (props.dataType) {
    case 'telemetry':
      return {
        key: '',
        method: 'notify',
        characteristicUUID: '',
        valueExpression: '[0]'
      }
    case 'attributes':
      return {
        key: '',
        method: 'read',
        characteristicUUID: '',
        valueExpression: '[:]'
      }
    case 'attributeUpdates':
      return {
        attributeOnThingsBoard: '',
        characteristicUUID: ''
      }
    case 'serverSideRpc':
      return {
        methodRPC: '',
        withResponse: true,
        characteristicUUID: '',
        methodProcessing: 'read'
      }
    default:
      return {}
  }
}

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据，只保留对应类型需要的字段
    let saveData = {}
    
    if (props.dataType === 'telemetry' || props.dataType === 'attributes') {
      saveData = {
        key: dataPointForm.key,
        method: dataPointForm.method,
        characteristicUUID: dataPointForm.characteristicUUID,
        valueExpression: dataPointForm.valueExpression
      }
    } else if (props.dataType === 'attributeUpdates') {
      saveData = {
        attributeOnThingsBoard: dataPointForm.attributeOnThingsBoard,
        characteristicUUID: dataPointForm.characteristicUUID
      }
    } else if (props.dataType === 'serverSideRpc') {
      saveData = {
        methodRPC: dataPointForm.methodRPC,
        withResponse: dataPointForm.withResponse,
        methodProcessing: dataPointForm.methodProcessing
      }
      
      // scan方法不需要UUID
      if (dataPointForm.methodProcessing !== 'scan') {
        saveData.characteristicUUID = dataPointForm.characteristicUUID
      }
    }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '数据点更新成功' : '数据点添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-input) {
  .el-input__inner {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  }
}

:deep(.el-dialog__body) {
  max-height: 60vh;
  overflow-y: auto;
}
</style> 