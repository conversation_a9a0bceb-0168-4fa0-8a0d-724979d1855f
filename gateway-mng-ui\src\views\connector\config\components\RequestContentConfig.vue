<template>
  <div class="content-config">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="内容类型">
          <el-select v-model="contentType" @change="handleContentTypeChange">
            <el-option label="JSON对象" value="json" />
            <el-option label="表单数据" value="form" />
            <el-option label="原始文本" value="raw" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="16">
        <div class="content-info">
          <el-text type="info" size="small">{{ getContentTypeDescription() }}</el-text>
        </div>
      </el-col>
    </el-row>

    <!-- JSON内容编辑 -->
    <div v-if="contentType === 'json'" class="json-content">
      <div class="section-header">
        <span class="section-title">JSON内容</span>
        <div class="json-actions">
          <el-button size="small" @click="formatJson" :icon="Refresh">格式化</el-button>
          <el-button size="small" @click="clearContent" :icon="Delete">清空</el-button>
        </div>
      </div>
      
      <div class="json-editor">
        <el-input
          v-model="jsonContent"
          type="textarea"
          :rows="8"
          placeholder='{"name": "morpheus", "job": "leader"}'
          @input="updateContent"
        />
      </div>
      
      <div v-if="jsonError" class="json-error">
        <el-alert type="error" :title="jsonError" show-icon :closable="false" />
      </div>
    </div>

    <!-- 表单数据编辑 -->
    <div v-if="contentType === 'form'" class="form-content">
      <div class="section-header">
        <span class="section-title">表单字段</span>
        <el-button type="primary" size="small" @click="addFormField" :icon="Plus">
          添加字段
        </el-button>
      </div>
      
      <div v-if="formFields.length === 0" class="empty-form">
        <el-empty description="暂无表单字段" :image-size="60">
          <el-button type="primary" size="small" @click="addFormField">添加第一个字段</el-button>
        </el-empty>
      </div>
      
      <div v-else class="form-fields">
        <div v-for="(field, index) in formFields" :key="index" class="form-field">
          <el-card shadow="never">
            <el-row :gutter="16" align="middle">
              <el-col :span="10">
                <el-form-item label="字段名" :label-width="60">
                  <el-input 
                    v-model="field.key" 
                    placeholder="username"
                    @input="updateFormContent"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字段值" :label-width="60">
                  <el-input 
                    v-model="field.value" 
                    placeholder="admin"
                    @input="updateFormContent"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <el-button 
                  type="danger" 
                  size="small" 
                  :icon="Delete" 
                  circle 
                  @click="removeFormField(index)"
                />
              </el-col>
            </el-row>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 原始文本编辑 -->
    <div v-if="contentType === 'raw'" class="raw-content">
      <div class="section-header">
        <span class="section-title">原始内容</span>
        <el-button size="small" @click="clearContent" :icon="Delete">清空</el-button>
      </div>
      
      <el-input
        v-model="rawContent"
        type="textarea"
        :rows="6"
        placeholder="请输入原始文本内容..."
        @input="updateContent"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus, Delete, Refresh } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: [Object, String],
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const contentType = ref('json')
const jsonContent = ref('')
const rawContent = ref('')
const jsonError = ref('')

// 表单字段数据
const formFields = computed(() => {
  if (contentType.value === 'form' && typeof props.modelValue === 'object') {
    return Object.entries(props.modelValue || {}).map(([key, value]) => ({
      key,
      value: String(value || '')
    }))
  }
  return []
})

// 内容类型描述
const getContentTypeDescription = () => {
  switch (contentType.value) {
    case 'json':
      return 'JSON格式的请求体，适用于REST API调用'
    case 'form':
      return '表单数据格式，类似HTML表单提交'
    case 'raw':
      return '原始文本内容，支持任意格式'
    default:
      return ''
  }
}

// 检测当前数据类型
const detectContentType = (value) => {
  if (!value) return 'json'
  if (typeof value === 'string') return 'raw'
  if (typeof value === 'object') return Object.keys(value).length > 0 ? 'json' : 'json'
  return 'json'
}

// 初始化内容
const initializeContent = () => {
  const value = props.modelValue
  contentType.value = detectContentType(value)
  
  if (contentType.value === 'json') {
    try {
      jsonContent.value = typeof value === 'object' 
        ? JSON.stringify(value, null, 2) 
        : JSON.stringify({}, null, 2)
    } catch (error) {
      jsonContent.value = '{}'
    }
  } else if (contentType.value === 'raw') {
    rawContent.value = String(value || '')
  }
}

// 格式化JSON
const formatJson = () => {
  try {
    const parsed = JSON.parse(jsonContent.value || '{}')
    jsonContent.value = JSON.stringify(parsed, null, 2)
    jsonError.value = ''
    updateContent()
  } catch (error) {
    jsonError.value = `JSON格式错误: ${error.message}`
  }
}

// 清空内容
const clearContent = () => {
  if (contentType.value === 'json') {
    jsonContent.value = '{}'
    jsonError.value = ''
  } else if (contentType.value === 'raw') {
    rawContent.value = ''
  }
  updateContent()
}

// 更新内容
const updateContent = () => {
  let newValue = {}
  
  try {
    if (contentType.value === 'json') {
      newValue = jsonContent.value ? JSON.parse(jsonContent.value) : {}
      jsonError.value = ''
    } else if (contentType.value === 'raw') {
      newValue = rawContent.value
    } else if (contentType.value === 'form') {
      newValue = {}
      formFields.value.forEach(field => {
        if (field.key && field.key.trim()) {
          newValue[field.key.trim()] = field.value || ''
        }
      })
    }
    
    emit('update:modelValue', newValue)
  } catch (error) {
    jsonError.value = `JSON格式错误: ${error.message}`
  }
}

// 内容类型变化处理
const handleContentTypeChange = () => {
  if (contentType.value === 'json') {
    jsonContent.value = JSON.stringify(props.modelValue || {}, null, 2)
    jsonError.value = ''
  } else if (contentType.value === 'raw') {
    rawContent.value = typeof props.modelValue === 'string' 
      ? props.modelValue 
      : JSON.stringify(props.modelValue || {})
  } else if (contentType.value === 'form') {
    // 表单字段会通过computed自动处理
  }
  updateContent()
}

// 添加表单字段
const addFormField = () => {
  const newFields = [...formFields.value]
  let newKey = 'field'
  let counter = 1
  
  // 确保键名唯一
  const existingKeys = newFields.map(f => f.key)
  while (existingKeys.includes(newKey)) {
    newKey = `field${counter}`
    counter++
  }
  
  newFields.push({ key: newKey, value: '' })
  updateFormFields(newFields)
}

// 删除表单字段
const removeFormField = (index) => {
  const newFields = [...formFields.value]
  newFields.splice(index, 1)
  updateFormFields(newFields)
  ElMessage.success('字段删除成功')
}

// 更新表单内容
const updateFormContent = () => {
  updateContent()
}

// 更新表单字段
const updateFormFields = (fields) => {
  const formData = {}
  fields.forEach(field => {
    if (field.key && field.key.trim()) {
      formData[field.key.trim()] = field.value || ''
    }
  })
  emit('update:modelValue', formData)
}

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== undefined) {
    initializeContent()
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.content-config {
  .content-info {
    display: flex;
    align-items: center;
    height: 32px;
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .section-title {
      font-weight: 600;
      color: #606266;
    }
    
    .json-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .json-content {
    margin-top: 16px;
    
    .json-editor {
      margin-bottom: 12px;
    }
    
    .json-error {
      margin-top: 12px;
    }
  }
  
  .form-content {
    margin-top: 16px;
    
    .empty-form {
      text-align: center;
      padding: 20px 0;
    }
    
    .form-fields {
      .form-field {
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  
  .raw-content {
    margin-top: 16px;
  }
}

:deep(.el-card__body) {
  padding: 12px 16px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
}
</style> 