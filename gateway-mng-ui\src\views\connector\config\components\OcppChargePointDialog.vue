<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="chargePointForm" :rules="rules" ref="formRef" label-width="140px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础信息 -->
        <el-tab-pane label="基础信息" name="basic">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="ID正则表达式" prop="idRegexpPattern" required>
                <el-input v-model="chargePointForm.idRegexpPattern" placeholder="bidon/hello/CP_1" />
                <div class="field-hint">充电桩ID的正则匹配模式</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备名表达式" prop="deviceNameExpression" required>
                <el-input v-model="chargePointForm.deviceNameExpression" placeholder="${Vendor} ${Model}" />
                <div class="field-hint">设备名称的生成表达式</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备类型表达式" prop="deviceTypeExpression" required>
                <el-input v-model="chargePointForm.deviceTypeExpression" placeholder="default" />
                <div class="field-hint">设备类型的生成表达式</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- OCPP表达式说明 -->
          <div class="expression-help">
            <el-alert
              title="OCPP表达式语法说明"
              type="info"
              :closable="false"
              show-icon
            >
              <div class="help-content">
                <p><strong>设备信息表达式:</strong></p>
                <ul>
                  <li><strong>${Vendor}</strong> - 充电桩厂商信息</li>
                  <li><strong>${Model}</strong> - 充电桩型号信息</li>
                  <li><strong>${SerialNumber}</strong> - 充电桩序列号</li>
                  <li><strong>${FirmwareVersion}</strong> - 固件版本</li>
                </ul>
                
                <p><strong>数据提取表达式:</strong></p>
                <ul>
                  <li><strong>${meter_value[:].sampled_value[:].value}</strong> - 提取计量值</li>
                  <li><strong>${connector_id}</strong> - 连接器ID</li>
                  <li><strong>${data.temp}</strong> - 数据传输中的温度值</li>
                  <li><strong>${transaction_id}</strong> - 事务ID</li>
                </ul>
              </div>
            </el-alert>
          </div>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <OcppDataPoints
            v-model="chargePointForm.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从OCPP消息提取的属性数据"
          />
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="timeseries">
          <OcppDataPoints
            v-model="chargePointForm.timeseries"
            data-type="timeseries"
            title="遥测数据配置"
            description="配置从OCPP消息提取的遥测数据"
          />
        </el-tab-pane>

        <!-- 属性更新配置 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <div class="config-section">
            <div class="section-header">
              <h4>属性更新配置</h4>
              <p class="description">配置IoTCloud平台属性更新处理</p>
              <el-button type="primary" size="small" @click="addAttributeUpdate">
                <el-icon><Plus /></el-icon>
                添加属性更新
              </el-button>
            </div>
            
            <div v-if="chargePointForm.attributeUpdates.length === 0" class="empty-state">
              <el-empty description="暂无属性更新配置" :image-size="60">
                <el-button type="primary" @click="addAttributeUpdate">添加第一个属性更新</el-button>
              </el-empty>
            </div>
            
            <div v-else class="updates-list">
              <el-card 
                v-for="(update, index) in chargePointForm.attributeUpdates" 
                :key="index" 
                class="update-card"
                shadow="hover"
              >
                <template #header>
                  <div class="update-header">
                    <span class="update-title">属性更新 #{{ index + 1 }}</span>
                    <el-button type="danger" size="small" link @click="deleteAttributeUpdate(index)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="IoTCloud属性" :prop="`attributeUpdates.${index}.attributeOnThingsBoard`">
                      <el-select v-model="update.attributeOnThingsBoard" placeholder="请选择属性类型">
                        <el-option label="共享属性 (shared)" value="shared" />
                        <el-option label="客户端属性 (client)" value="client" />
                        <el-option label="服务端属性 (server)" value="server" />
                      </el-select>
                      <div class="field-hint">属性在IoTCloud上的存储类型</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="值表达式" :prop="`attributeUpdates.${index}.valueExpression`">
                      <el-input v-model="update.valueExpression" placeholder='{"${attributeKey}":"${attributeValue}"}' />
                      <div class="field-hint">属性值的JSON表达式</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- RPC配置 -->
        <el-tab-pane label="RPC配置" name="serverSideRpc">
          <div class="config-section">
            <div class="section-header">
              <h4>服务端RPC配置</h4>
              <p class="description">配置从IoTCloud到充电桩的RPC调用</p>
              <el-button type="primary" size="small" @click="addServerSideRpc">
                <el-icon><Plus /></el-icon>
                添加RPC方法
              </el-button>
            </div>
            
            <div v-if="chargePointForm.serverSideRpc.length === 0" class="empty-state">
              <el-empty description="暂无RPC配置" :image-size="60">
                <el-button type="primary" @click="addServerSideRpc">添加第一个RPC方法</el-button>
              </el-empty>
            </div>
            
            <div v-else class="rpc-list">
              <el-card 
                v-for="(rpc, index) in chargePointForm.serverSideRpc" 
                :key="index" 
                class="rpc-card"
                shadow="hover"
              >
                <template #header>
                  <div class="rpc-header">
                    <div class="rpc-info">
                      <span class="rpc-title">{{ rpc.methodRPC || 'RPC方法' }}</span>
                      <el-tag size="small" :type="rpc.withResponse ? 'success' : 'info'">
                        {{ rpc.withResponse ? '有响应' : '无响应' }}
                      </el-tag>
                    </div>
                    <el-button type="danger" size="small" link @click="deleteServerSideRpc(index)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="RPC方法名" :prop="`serverSideRpc.${index}.methodRPC`">
                      <el-input v-model="rpc.methodRPC" placeholder="rpc1" />
                      <div class="field-hint">RPC方法的名称标识</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否需要响应">
                      <el-switch v-model="rpc.withResponse" />
                      <div class="field-hint">RPC调用是否需要返回响应</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="值表达式" :prop="`serverSideRpc.${index}.valueExpression`">
                      <el-input v-model="rpc.valueExpression" placeholder="${params}" />
                      <div class="field-hint">RPC参数的值表达式</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import OcppDataPoints from './OcppDataPoints.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  chargePoint: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const activeTab = ref('basic')
const saving = ref(false)

// 充电桩表单数据
const chargePointForm = reactive({
  idRegexpPattern: '',
  deviceNameExpression: '',
  deviceTypeExpression: '',
  attributes: [],
  timeseries: [],
  attributeUpdates: [],
  serverSideRpc: []
})

// 获取对话框标题
const getDialogTitle = () => {
  return props.isEdit ? '编辑充电桩配置' : '添加充电桩配置'
}

// 表单验证规则
const rules = reactive({
  idRegexpPattern: [
    { required: true, message: '请输入ID正则表达式', trigger: 'blur' }
  ],
  deviceNameExpression: [
    { required: true, message: '请输入设备名表达式', trigger: 'blur' }
  ],
  deviceTypeExpression: [
    { required: true, message: '请输入设备类型表达式', trigger: 'blur' }
  ]
})

// 添加属性更新
const addAttributeUpdate = () => {
  chargePointForm.attributeUpdates.push({
    attributeOnThingsBoard: 'shared',
    valueExpression: '{"${attributeKey}":"${attributeValue}"}'
  })
}

// 删除属性更新
const deleteAttributeUpdate = (index) => {
  chargePointForm.attributeUpdates.splice(index, 1)
}

// 添加服务端RPC
const addServerSideRpc = () => {
  chargePointForm.serverSideRpc.push({
    methodRPC: '',
    withResponse: true,
    valueExpression: '${params}'
  })
}

// 删除服务端RPC
const deleteServerSideRpc = (index) => {
  chargePointForm.serverSideRpc.splice(index, 1)
}

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.chargePoint) {
    // 加载充电桩数据
    Object.assign(chargePointForm, {
      idRegexpPattern: '',
      deviceNameExpression: '${Vendor} ${Model}',
      deviceTypeExpression: 'default',
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      serverSideRpc: [],
      ...props.chargePoint
    })
  } else if (newValue) {
    // 重置为默认值
    Object.assign(chargePointForm, {
      idRegexpPattern: '',
      deviceNameExpression: '${Vendor} ${Model}',
      deviceTypeExpression: 'default',
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      serverSideRpc: []
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    activeTab.value = 'basic'
  }
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...chargePointForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '充电桩更新成功' : '充电桩添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.expression-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }
}

.config-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .description {
      margin: 0;
      font-size: 12px;
      color: #909399;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .updates-list, .rpc-list {
    .update-card, .rpc-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .update-header, .rpc-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .update-title, .rpc-info {
          font-weight: 500;
          color: #303133;
        }
        
        .rpc-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .rpc-title {
            font-weight: 500;
            color: #303133;
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style> 