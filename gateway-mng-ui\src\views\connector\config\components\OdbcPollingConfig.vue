<template>
  <div class="odbc-polling-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>数据轮询配置</span>
          <el-tooltip content="配置定期执行的SQL查询和数据获取策略" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="pollingConfig" :rules="rules" label-width="140px" ref="formRef">
        <!-- 基础轮询配置 -->
        <div class="config-section">
          <div class="section-title">基础配置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="轮询周期 (秒)" prop="period" required>
                <el-input-number 
                  v-model="pollingConfig.period" 
                  :min="1" 
                  :precision="0"
                  controls-position="right"
                  
                />
                <div class="field-hint">执行查询的时间间隔</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- SQL查询配置 -->
        <div class="config-section">
          <div class="section-title">SQL查询配置</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="查询语句" prop="query" required>
                <el-input 
                  v-model="pollingConfig.query" 
                  type="textarea" 
                  :rows="5"
                  placeholder="SELECT bool_v, str_v, dbl_v, long_v, entity_id, ts FROM ts_kv WHERE ts > ? ORDER BY ts ASC LIMIT 10"
                  
                />
                <div class="field-hint">要执行的SQL查询语句，支持参数占位符</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- SQL帮助信息 -->
          <div class="sql-help">
            <el-alert
              title="SQL查询说明"
              type="info"
              :closable="false"
              show-icon
            >
              <div class="help-content">
                <p><strong>参数占位符:</strong> 使用 ? 作为参数占位符，配合迭代器使用</p>
                <p><strong>推荐模式:</strong> 查询新增数据，避免重复处理</p>
                <p><strong>性能优化:</strong> 添加合适的索引，使用 LIMIT 限制返回行数</p>
                <p><strong>示例:</strong> SELECT id, name, value, timestamp FROM sensor_data WHERE timestamp > ? ORDER BY timestamp ASC LIMIT 100</p>
              </div>
            </el-alert>
          </div>
        </div>

        <!-- 迭代器配置 -->
        <div class="config-section">
          <div class="section-title">
            迭代器配置
            <el-switch v-model="iteratorEnabled" @change="toggleIterator" />
          </div>
          
          <div v-if="iteratorEnabled">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="迭代列名" prop="iterator.column" required>
                  <el-input 
                    v-model="pollingConfig.iterator.column" 
                    placeholder="ts"
                    
                  />
                  <div class="field-hint">用于记录查询进度的列名</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="持久化状态">
                  <el-switch v-model="pollingConfig.iterator.persistent"  />
                  <div class="field-hint">是否在重启后保持迭代状态</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="初始化查询" prop="iterator.query">
                  <el-input 
                    v-model="pollingConfig.iterator.query" 
                    type="textarea" 
                    :rows="3"
                    placeholder="SELECT MIN(ts) - 1 FROM ts_kv"
                    
                  />
                  <div class="field-hint">获取迭代起始值的查询语句</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- 迭代器说明 -->
            <div class="iterator-help">
              <el-alert
                title="迭代器工作原理"
                type="warning"
                :closable="false"
                show-icon
              >
                <div class="help-content">
                  <p><strong>功能:</strong> 记录上次查询的最后一个值，确保数据不重复、不遗漏</p>
                  <p><strong>工作流程:</strong></p>
                  <ol>
                    <li>首次运行时执行初始化查询获取起始值</li>
                    <li>每次轮询使用该值作为参数执行主查询</li>
                    <li>更新迭代值为查询结果中指定列的最大值</li>
                    <li>重复步骤2-3</li>
                  </ol>
                  <p><strong>注意:</strong> 迭代列应该是单调递增的（如时间戳、自增ID）</p>
                </div>
              </el-alert>
            </div>
          </div>
        </div>

        <!-- 查询测试 -->
        <div class="test-section">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-button 
                type="primary" 
                :loading="testing" 
                @click="testQuery"
                :disabled="!isQueryValid"
              >
                <el-icon><CaretRight /></el-icon>
                测试查询
              </el-button>
              <el-button 
                type="success" 
                :loading="testingIterator" 
                @click="testIteratorQuery"
                :disabled="!isIteratorValid"
                v-if="iteratorEnabled"
              >
                <el-icon><Timer /></el-icon>
                测试迭代器
              </el-button>
              <span class="test-hint">点击测试SQL查询是否正确</span>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { QuestionFilled, CaretRight, Timer } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      query: 'SELECT bool_v, str_v, dbl_v, long_v, entity_id, ts FROM ts_kv WHERE ts > ? ORDER BY ts ASC LIMIT 10',
      period: 10,
      iterator: {
        column: 'ts',
        query: 'SELECT MIN(ts) - 1 FROM ts_kv',
        persistent: false
      }
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const testing = ref(false)
const testingIterator = ref(false)
const iteratorEnabled = ref(true)

// 轮询配置数据
const pollingConfig = reactive({
  query: 'SELECT bool_v, str_v, dbl_v, long_v, entity_id, ts FROM ts_kv WHERE ts > ? ORDER BY ts ASC LIMIT 10',
  period: 10,
  iterator: {
    column: 'ts',
    query: 'SELECT MIN(ts) - 1 FROM ts_kv',
    persistent: false
  }
})

// 表单验证规则
const rules = reactive({
  period: [
    { required: true, message: '请输入轮询周期', trigger: 'blur' },
    { type: 'number', min: 1, message: '轮询周期必须大于0', trigger: 'blur' }
  ],
  query: [
    { required: true, message: '请输入查询语句', trigger: 'blur' },
    { min: 10, message: '查询语句不能为空', trigger: 'blur' }
  ],
  'iterator.column': [
    { required: true, message: '请输入迭代列名', trigger: 'blur' }
  ]
})

// 检查查询是否有效
const isQueryValid = computed(() => {
  return !!(pollingConfig.query && pollingConfig.query.trim() && pollingConfig.period > 0)
})

// 检查迭代器是否有效
const isIteratorValid = computed(() => {
  return !!(iteratorEnabled.value && pollingConfig.iterator.column && pollingConfig.iterator.query)
})

// 切换迭代器
const toggleIterator = (enabled) => {
  if (!enabled) {
    // 禁用时清除迭代器配置
    pollingConfig.iterator = {
      column: '',
      query: '',
      persistent: false
    }
  } else {
    // 启用时设置默认值
    pollingConfig.iterator = {
      column: 'ts',
      query: 'SELECT MIN(ts) - 1 FROM ts_kv',
      persistent: false
    }
  }
  handleChange()
}

// 测试查询
const testQuery = async () => {
  try {
    await formRef.value?.validateField('query')
    testing.value = true
    
    // 模拟查询测试
    setTimeout(() => {
      testing.value = false
      ElMessage.success('SQL查询语法验证通过！')
    }, 1500)
    
  } catch (error) {
    console.error('查询验证失败:', error)
    ElMessage.error('请检查SQL查询语法')
  }
}

// 测试迭代器查询
const testIteratorQuery = async () => {
  try {
    await formRef.value?.validateField('iterator.query')
    testingIterator.value = true
    
    // 模拟迭代器查询测试
    setTimeout(() => {
      testingIterator.value = false
      ElMessage.success('迭代器查询验证通过！')
    }, 1500)
    
  } catch (error) {
    console.error('迭代器查询验证失败:', error)
    ElMessage.error('请检查迭代器查询语法')
  }
}

// 处理配置变化
const handleChange = () => {
  const config = { ...pollingConfig }
  if (!iteratorEnabled.value) {
    delete config.iterator
  }
  emit('update:modelValue', config)
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(pollingConfig, {
      query: 'SELECT bool_v, str_v, dbl_v, long_v, entity_id, ts FROM ts_kv WHERE ts > ? ORDER BY ts ASC LIMIT 10',
      period: 10,
      iterator: {
        column: 'ts',
        query: 'SELECT MIN(ts) - 1 FROM ts_kv',
        persistent: false
      },
      ...newValue
    })
    
    // 检查是否有迭代器配置
    iteratorEnabled.value = !!(newValue.iterator && Object.keys(newValue.iterator).length > 0)
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(pollingConfig, () => {
  handleChange()
}, { deep: true })
</script>

<style lang="scss" scoped>
.odbc-polling-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .config-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f8f9fa;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .sql-help, .iterator-help {
    margin-top: 16px;
    
    .help-content {
      line-height: 1.6;
      
      p {
        margin: 8px 0;
        
        strong {
          color: #409eff;
        }
      }
      
      ol {
        margin: 8px 0;
        padding-left: 20px;
        
        li {
          margin: 4px 0;
        }
      }
    }
  }

  .test-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f0f9ff;
    
    .test-hint {
      margin-left: 12px;
      font-size: 12px;
      color: #909399;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-textarea) {
    .el-textarea__inner {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
    }
  }
}
</style> 