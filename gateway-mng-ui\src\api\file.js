import request from '@/utils/request'

// 获取所有json文件
export function getAllFile() {
  return request({
    url: '/gateway/v1/read_filename',
    method: 'get'
  })
}

// 获取指定json文件
export function getFileFromName(fileName) {
  return request({
    url: '/gateway/v1/read_json?file_name=' + fileName,
    method: 'get'
  })
}

// 新增、修改
export function updataFile(data) {
  return request({
    url: '/gateway/v1/write_json',
    method: 'post',
    data: data
  })
}

// 删除json文件
export function deletaFile(fileName) {
  return request({
    url: '/gateway/v1/delete_json?file_name=' + fileName,
    method: 'get'
  })
}

// 查询状态
export function getState(type, file_name) {
  return request({
    url: '/gateway/v1/file_status?type_=' + type + '&connector_name=' + (file_name ? file_name : null),
    method: 'get'
  })
}


// 查询全部配置文件
export function getAllConfig() {
  return request({
    url: '/gateway/v1/file_config/list',
    method: 'get'
  })
}

// 查询最后活跃时间
export function getLastActive() {
  return request({
    url: '/gateway/v1/file_lasttime/list',
    method: 'get'
  })
}

// 查询连接器下的设备列表
export function getDeviceFromTable(data) {
  return request({
    url: '/gateway/v1/devices/connectors',
    method: 'post',
    data: data
  })
}


// 查询状态列表
export function getStateList(data) {
  return request({
    url: '/gateway/v1/file_status/list',
    method: 'post',
    data: data
  })
}
