<template>
  <div class="ftp-paths-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>文件路径配置</span>
          <el-button type="primary" size="small" @click="addPath">
            <el-icon><Plus /></el-icon>
            添加路径
          </el-button>
        </div>
      </template>
      
      <div v-if="pathsConfig.length === 0" class="empty-state">
        <el-empty description="暂无配置路径">
          <el-button type="primary" @click="addPath">添加第一个路径</el-button>
        </el-empty>
      </div>
      
      <div v-else class="paths-list">
        <el-card 
          v-for="(path, index) in pathsConfig" 
          :key="index" 
          class="path-card"
          shadow="hover"
        >
          <template #header>
            <div class="path-header">
              <div class="path-info">
                <span class="path-name">{{ path.devicePatternName || '未命名路径' }}</span>
                <el-tag size="small" type="info">{{ path.path || '未设置路径' }}</el-tag>
              </div>
              <div class="path-actions">
                <el-button type="primary" size="small" link @click="editPath(path, index)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" link @click="deletePath(index)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="path-summary">
            <el-row :gutter="16">
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">设备类型:</span>
                  <span class="value">{{ path.devicePatternType || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">读取模式:</span>
                  <span class="value">{{ path.readMode || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">轮询周期:</span>
                  <span class="value">{{ path.pollPeriod }}秒</span>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="summary-item">
                  <span class="label">数据点:</span>
                  <span class="value">
                    属性{{ (path.attributes || []).length }}个，
                    遥测{{ (path.timeseries || []).length }}个
                  </span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 路径配置对话框 -->
    <FtpPathDialog
      v-model="dialogVisible"
      :path="currentPath"
      :is-edit="isEdit"
      @save="handleSavePath"
      @cancel="handleCancelPath"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick, defineEmits, defineProps } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import FtpPathDialog from './FtpPathDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const currentPath = ref(null)
const isEdit = ref(false)
const currentIndex = ref(-1)

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 路径配置数据
const pathsConfig = reactive([])

// 添加路径
const addPath = () => {
  currentPath.value = null
  isEdit.value = false
  currentIndex.value = -1
  dialogVisible.value = true
}

// 编辑路径
const editPath = (path, index) => {
  currentPath.value = { ...path }
  isEdit.value = true
  currentIndex.value = index
  dialogVisible.value = true
}

// 删除路径
const deletePath = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个文件路径配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    pathsConfig.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存路径
const handleSavePath = (pathData) => {
  if (isEdit.value && currentIndex.value >= 0) {
    // 编辑模式
    pathsConfig.splice(currentIndex.value, 1, pathData)
    ElMessage.success('路径更新成功')
  } else {
    // 添加模式
    pathsConfig.push(pathData)
    ElMessage.success('路径添加成功')
  }
  
  dialogVisible.value = false
  handleChange()
}

// 取消操作
const handleCancelPath = () => {
  dialogVisible.value = false
}

// 处理配置变化
const handleChange = () => {
  if (!isInternalUpdate.value) {
    const paths = pathsConfig.map(path => ({ ...path }))
    emit('update:modelValue', paths)
  }
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value && Array.isArray(newValue)) {
    isInternalUpdate.value = true
    pathsConfig.splice(0, pathsConfig.length, ...newValue)
    nextTick(() => {
      isInternalUpdate.value = false
    })
  }
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
.ftp-paths-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .paths-list {
    .path-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .path-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .path-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .path-name {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .path-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .path-summary {
        margin-top: 16px;
        
        .summary-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .label {
            font-size: 12px;
            color: #909399;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style> 