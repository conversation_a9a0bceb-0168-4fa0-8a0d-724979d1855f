<template>
    <div class="gateway-device-filtering-config">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>设备过滤配置</span>
            <el-switch
              v-model="localConfig.enable"
              @change="handleConfigChange"
              active-text="启用"
              inactive-text="禁用"
            />
          </div>
        </template>
  
        <div class="config-content">
          <!-- 基础配置 -->
          <div class="basic-settings">
            <el-form :model="localConfig" label-width="120px" label-position="left">
              <el-form-item label="过滤文件名">
                <el-input
                  v-model="localConfig.filterFile"
                  placeholder="请输入过滤配置文件名"
                  @input="handleConfigChange"
                  :disabled="!localConfig.enable"
                >
                  <template #suffix>
                    <span class="file-extension">.json</span>
                  </template>
                </el-input>
                <div class="form-item-tip">
                  指定设备过滤规则的配置文件名，文件将保存在网关配置目录中
                </div>
              </el-form-item>
            </el-form>
          </div>
  
          <!-- 过滤规则配置 -->
          <div v-if="localConfig.enable" class="filtering-rules">
            <el-divider content-position="left">
              <span class="divider-text">过滤规则配置</span>
            </el-divider>
  
            <div class="rules-container">
              <!-- 拒绝规则 -->
              <div class="rule-section">
                            <div class="section-header">
              <div class="header-left">
                <h4>拒绝规则 (Deny)</h4>
                <el-tag v-if="fileLoading" type="info" size="small">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  加载中...
                </el-tag>
                <el-tag v-else-if="fileSaving" type="warning" size="small">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  保存中...
                </el-tag>
                <el-tag v-else-if="localConfig.filterFile" type="success" size="small">
                  {{ localConfig.filterFile }}
                </el-tag>
              </div>
              <el-button 
                type="primary" 
                size="small" 
                @click="addDenyRule"
                icon="Plus"
              >
                添加拒绝规则
              </el-button>
            </div>
                <div class="section-description">
                  配置需要拒绝访问的设备，这些设备将被阻止连接到指定的连接器
                </div>
                
                <div class="rules-list">
                  <div 
                    v-for="(devices, connectorName, index) in filterRules.deny" 
                    :key="`deny-${index}`"
                    class="rule-card"
                  >
                    <div class="rule-header">
                      <el-input
                        v-model="filterRules.deny[connectorName]._connectorName"
                        placeholder="连接器名称"
                        @input="updateDenyConnectorName(connectorName, $event)"
                        class="connector-input"
                      />
                      <el-button 
                        type="danger" 
                        size="small" 
                        @click="removeDenyRule(connectorName)"
                        icon="Delete"
                      >
                        删除
                      </el-button>
                    </div>
                    <div class="devices-list">
                      <el-tag
                        v-for="(device, deviceIndex) in devices.filter(d => d !== '_connectorName')"
                        :key="deviceIndex"
                        closable
                        @close="removeDenyDevice(connectorName, deviceIndex)"
                        class="device-tag"
                      >
                        {{ device }}
                      </el-tag>
                      <el-input
                        v-model="newDenyDevice[connectorName]"
                        placeholder="输入设备名称或正则表达式"
                        @keyup.enter="addDenyDevice(connectorName)"
                        class="device-input"
                        size="small"
                      >
                        <template #append>
                          <el-button @click="addDenyDevice(connectorName)" icon="Plus" />
                        </template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </div>
  
              <!-- 允许规则 -->
              <div class="rule-section">
                <div class="section-header">
                  <h4>允许规则 (Allow)</h4>
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="addAllowRule"
                    icon="Plus"
                  >
                    添加允许规则
                  </el-button>
                </div>
                <div class="section-description">
                  配置明确允许访问的设备，这些设备将不受拒绝规则限制
                </div>
                
                <div class="rules-list">
                  <div 
                    v-for="(devices, connectorName, index) in filterRules.allow" 
                    :key="`allow-${index}`"
                    class="rule-card"
                  >
                    <div class="rule-header">
                      <el-input
                        v-model="filterRules.allow[connectorName]._connectorName"
                        placeholder="连接器名称"
                        @input="updateAllowConnectorName(connectorName, $event)"
                        class="connector-input"
                      />
                      <el-button 
                        type="danger" 
                        size="small" 
                        @click="removeAllowRule(connectorName)"
                        icon="Delete"
                      >
                        删除
                      </el-button>
                    </div>
                    <div class="devices-list">
                      <el-tag
                        v-for="(device, deviceIndex) in devices.filter(d => d !== '_connectorName')"
                        :key="deviceIndex"
                        closable
                        @close="removeAllowDevice(connectorName, deviceIndex)"
                        class="device-tag"
                        type="success"
                      >
                        {{ device }}
                      </el-tag>
                      <el-input
                        v-model="newAllowDevice[connectorName]"
                        placeholder="输入设备名称或正则表达式"
                        @keyup.enter="addAllowDevice(connectorName)"
                        class="device-input"
                        size="small"
                      >
                        <template #append>
                          <el-button @click="addAllowDevice(connectorName)" icon="Plus" />
                        </template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </div>
            </div>
  
            <!-- 配置预览 -->
            <div class="config-preview">
              <el-divider content-position="left">
                <span class="divider-text">配置预览</span>
              </el-divider>
              <el-input
                :model-value="previewConfig"
                type="textarea"
                :rows="10"
                readonly
                class="preview-textarea"
              />
              <div class="preview-actions">
                <el-button @click="manualSaveFile" size="small" icon="Upload" type="primary" :loading="fileSaving">
                  保存到文件
                </el-button>
                <el-button @click="manualLoadFile" size="small" icon="Download" :loading="fileLoading">
                  从文件加载
                </el-button>
                <el-button @click="copyConfig" size="small" icon="CopyDocument">
                  复制配置
                </el-button>
                <el-button @click="downloadConfig" size="small" icon="Download">
                  下载配置文件
                </el-button>
              </div>
            </div>
          </div>
  
          <!-- 帮助信息 -->
          <div class="help-section">
            <el-divider content-position="left">
              <span class="divider-text">使用说明</span>
            </el-divider>
            <el-alert
              title="设备过滤功能说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="help-content">
                  <p><strong>设备过滤功能</strong>允许您基于特定条件定义设备过滤规则：</p>
                  <ul>
                    <li><strong>拒绝规则 (Deny)</strong>：指定应被拒绝访问特定连接器的设备</li>
                    <li><strong>允许规则 (Allow)</strong>：指定明确允许访问特定连接器的设备</li>
                    <li><strong>正则表达式</strong>：支持使用正则表达式匹配设备名称</li>
                    <li><strong>优先级</strong>：允许规则优先级高于拒绝规则</li>
                  </ul>
                  <p><strong>示例</strong>：</p>
                  <ul>
                    <li>设备名称：<code>Temperature Device</code></li>
                    <li>正则表达式：<code>(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)</code> (匹配邮箱格式)</li>
                  </ul>
                </div>
              </template>
            </el-alert>
          </div>
        </div>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, computed, watch, defineEmits, defineProps } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Plus, Delete, CopyDocument, Download, Loading } from '@element-plus/icons-vue'
  import { updataFile, getFileFromName } from '@/api/file'
  
  const props = defineProps({
    modelValue: {
      type: Object,
      default: () => ({
        enable: false,
        filterFile: 'list.json'
      })
    }
  })
  
  const emit = defineEmits(['update:modelValue', 'change'])
  
  // 本地配置
  const localConfig = reactive({
    enable: false,
    filterFile: 'list.json'
  })
  
  // 过滤规则
  const filterRules = reactive({
    deny: {},
    allow: {}
  })
  
  // 新设备输入
  const newDenyDevice = reactive({})
  const newAllowDevice = reactive({})

  // 文件操作状态
  const fileLoading = ref(false)
  const fileSaving = ref(false)
  
  // 监听props变化
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      Object.assign(localConfig, {
        enable: newValue.enable || false,
        filterFile: newValue.filterFile || 'list.json'
      })
    }
  }, { deep: true, immediate: true })
  
  // 配置预览
  const previewConfig = computed(() => {
    const config = {
      deny: {},
      allow: {}
    }
    
    // 处理拒绝规则
    Object.keys(filterRules.deny).forEach(connectorName => {
      const devices = filterRules.deny[connectorName].filter(d => d !== '_connectorName')
      if (devices.length > 0) {
        config.deny[connectorName] = devices
      }
    })
    
    // 处理允许规则
    Object.keys(filterRules.allow).forEach(connectorName => {
      const devices = filterRules.allow[connectorName].filter(d => d !== '_connectorName')
      if (devices.length > 0) {
        config.allow[connectorName] = devices
      }
    })
    
    return JSON.stringify(config, null, 2)
  })
  
  // 处理配置变化
  const handleConfigChange = async () => {
    const config = {
      enable: localConfig.enable,
      filterFile: localConfig.filterFile
    }
    
    emit('update:modelValue', config)
    emit('change', config)

    // 如果启用了过滤功能，保存过滤规则配置文件
    if (localConfig.enable) {
      await saveFilterRules()
    }
  }

  // 保存过滤规则到文件
  const saveFilterRules = async () => {
    if (!localConfig.enable || !localConfig.filterFile) {
      return
    }

    try {
      fileSaving.value = true
      const response = await updataFile({
        file_name: localConfig.filterFile,
        file_text: previewConfig.value
      })
      
      if (response.msg === 'success') {
        ElMessage.success(`过滤规则已保存到文件: ${localConfig.filterFile}`)
      } else {
        ElMessage.error(`保存失败: ${response.data}`)
      }
    } catch (error) {
      console.error('保存过滤规则失败:', error)
      ElMessage.error(`保存失败: ${error.message}`)
    } finally {
      fileSaving.value = false
    }
  }

  // 从文件加载过滤规则
  const loadFilterRules = async () => {
    if (!localConfig.filterFile) {
      ElMessage.error('请先设置过滤配置文件名')
      return
    }

    try {
      fileLoading.value = true
      const response = await getFileFromName(localConfig.filterFile)
      
      if (response.data) {
        // 清空现有规则
        Object.keys(filterRules.deny).forEach(key => delete filterRules.deny[key])
        Object.keys(filterRules.allow).forEach(key => delete filterRules.allow[key])
        Object.keys(newDenyDevice).forEach(key => delete newDenyDevice[key])
        Object.keys(newAllowDevice).forEach(key => delete newAllowDevice[key])
        
        // 加载新规则
        if (response.data.deny) {
          Object.assign(filterRules.deny, response.data.deny)
          Object.keys(response.data.deny).forEach(key => {
            newDenyDevice[key] = ''
          })
        }
        
        if (response.data.allow) {
          Object.assign(filterRules.allow, response.data.allow)
          Object.keys(response.data.allow).forEach(key => {
            newAllowDevice[key] = ''
          })
        }
        
        ElMessage.success(`已加载过滤配置文件: ${localConfig.filterFile}`)
      } else {
        ElMessage.warning(`配置文件 ${localConfig.filterFile} 不存在或格式错误`)
      }
    } catch (error) {
      console.error('加载过滤配置文件失败:', error)
      ElMessage.warning(`配置文件 ${localConfig.filterFile} 不存在`)
    } finally {
      fileLoading.value = false
    }
  }

  // 手动保存配置文件
  const manualSaveFile = async () => {
    await saveFilterRules()
  }

  // 手动加载配置文件
  const manualLoadFile = async () => {
    await loadFilterRules()
  }
  
  // 添加拒绝规则
  const addDenyRule = async () => {
    const connectorName = `新连接器${Object.keys(filterRules.deny).length + 1}`
    filterRules.deny[connectorName] = ['_connectorName']
    filterRules.deny[connectorName]._connectorName = connectorName
    newDenyDevice[connectorName] = ''
    // 自动保存到文件
    await saveFilterRules()
  }
  
  // 删除拒绝规则
  const removeDenyRule = async (connectorName) => {
    delete filterRules.deny[connectorName]
    delete newDenyDevice[connectorName]
    // 自动保存到文件
    await saveFilterRules()
  }
  
  // 更新拒绝规则连接器名称
  const updateDenyConnectorName = async (oldName, newName) => {
    if (oldName !== newName && newName.trim()) {
      const devices = filterRules.deny[oldName].filter(d => d !== '_connectorName')
      delete filterRules.deny[oldName]
      filterRules.deny[newName] = [...devices, '_connectorName']
      filterRules.deny[newName]._connectorName = newName
      
      newDenyDevice[newName] = newDenyDevice[oldName] || ''
      delete newDenyDevice[oldName]
      
      // 自动保存到文件
      await saveFilterRules()
    }
  }
  
  // 添加拒绝设备
  const addDenyDevice = async (connectorName) => {
    const deviceName = newDenyDevice[connectorName]?.trim()
    if (deviceName && !filterRules.deny[connectorName].includes(deviceName)) {
      filterRules.deny[connectorName].push(deviceName)
      newDenyDevice[connectorName] = ''
      // 自动保存到文件
      await saveFilterRules()
    }
  }
  
  // 删除拒绝设备
  const removeDenyDevice = async (connectorName, deviceIndex) => {
    const devices = filterRules.deny[connectorName].filter(d => d !== '_connectorName')
    const actualIndex = filterRules.deny[connectorName].indexOf(devices[deviceIndex])
    if (actualIndex > -1) {
      filterRules.deny[connectorName].splice(actualIndex, 1)
      // 自动保存到文件
      await saveFilterRules()
    }
  }
  
  // 添加允许规则
  const addAllowRule = async () => {
    const connectorName = `新连接器${Object.keys(filterRules.allow).length + 1}`
    filterRules.allow[connectorName] = ['_connectorName']
    filterRules.allow[connectorName]._connectorName = connectorName
    newAllowDevice[connectorName] = ''
    // 自动保存到文件
    await saveFilterRules()
  }
  
  // 删除允许规则
  const removeAllowRule = async (connectorName) => {
    delete filterRules.allow[connectorName]
    delete newAllowDevice[connectorName]
    // 自动保存到文件
    await saveFilterRules()
  }
  
  // 更新允许规则连接器名称
  const updateAllowConnectorName = async (oldName, newName) => {
    if (oldName !== newName && newName.trim()) {
      const devices = filterRules.allow[oldName].filter(d => d !== '_connectorName')
      delete filterRules.allow[oldName]
      filterRules.allow[newName] = [...devices, '_connectorName']
      filterRules.allow[newName]._connectorName = newName
      
      newAllowDevice[newName] = newAllowDevice[oldName] || ''
      delete newAllowDevice[oldName]
      
      // 自动保存到文件
      await saveFilterRules()
    }
  }
  
  // 添加允许设备
  const addAllowDevice = async (connectorName) => {
    const deviceName = newAllowDevice[connectorName]?.trim()
    if (deviceName && !filterRules.allow[connectorName].includes(deviceName)) {
      filterRules.allow[connectorName].push(deviceName)
      newAllowDevice[connectorName] = ''
      // 自动保存到文件
      await saveFilterRules()
    }
  }
  
  // 删除允许设备
  const removeAllowDevice = async (connectorName, deviceIndex) => {
    const devices = filterRules.allow[connectorName].filter(d => d !== '_connectorName')
    const actualIndex = filterRules.allow[connectorName].indexOf(devices[deviceIndex])
    if (actualIndex > -1) {
      filterRules.allow[connectorName].splice(actualIndex, 1)
      // 自动保存到文件
      await saveFilterRules()
    }
  }
  
  // 复制配置
  const copyConfig = async () => {
    try {
      await navigator.clipboard.writeText(previewConfig.value)
      ElMessage.success('配置已复制到剪贴板')
    } catch (error) {
      ElMessage.error('复制失败，请手动复制')
    }
  }
  
  // 下载配置文件
  const downloadConfig = () => {
    const blob = new Blob([previewConfig.value], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = localConfig.filterFile || 'list.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    ElMessage.success('配置文件下载成功')
  }
  
  // 初始化示例数据
  const initializeExampleData = () => {
    // 添加示例拒绝规则
    filterRules.deny['MQTT Broker Connector'] = [
      'Temperature Device',
      '(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$)',
      '_connectorName'
    ]
    filterRules.deny['MQTT Broker Connector']._connectorName = 'MQTT Broker Connector'
    
    filterRules.deny['Modbus Connector'] = [
      'My Modbus Device',
      '_connectorName'
    ]
    filterRules.deny['Modbus Connector']._connectorName = 'Modbus Connector'
    
    // 添加示例允许规则
    filterRules.allow['MQTT Broker Connector'] = [
      'My Temperature Sensor',
      '_connectorName'
    ]
    filterRules.allow['MQTT Broker Connector']._connectorName = 'MQTT Broker Connector'
    
    // 初始化输入框
    newDenyDevice['MQTT Broker Connector'] = ''
    newDenyDevice['Modbus Connector'] = ''
    newAllowDevice['MQTT Broker Connector'] = ''
  }
  
  // 组件挂载时初始化示例数据
  initializeExampleData()
  </script>
  
  <style lang="scss" scoped>
  .gateway-device-filtering-config {
    .config-card {
      border: 1px solid #e4e7ed;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        color: #303133;
        font-size: 16px;
      }
    }
    
    .config-content {
      .basic-settings {
        margin-bottom: 24px;
        
        .file-extension {
          color: #909399;
          font-size: 12px;
        }
        
        .form-item-tip {
          font-size: 12px;
          color: #909399;
          margin-top: 4px;
          line-height: 1.4;
        }
      }
      
      .filtering-rules {
        .divider-text {
          font-weight: 600;
          color: #303133;
        }
        
        .rules-container {
          .rule-section {
            margin-bottom: 32px;
            
            .section-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
              
              .header-left {
                display: flex;
                align-items: center;
                gap: 12px;
              }
              
              h4 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #303133;
              }
            }
            
            .section-description {
              font-size: 14px;
              color: #606266;
              margin-bottom: 16px;
              line-height: 1.5;
            }
            
            .rules-list {
              .rule-card {
                border: 1px solid #e4e7ed;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 12px;
                background: #fafafa;
                
                .rule-header {
                  display: flex;
                  gap: 12px;
                  align-items: center;
                  margin-bottom: 12px;
                  
                  .connector-input {
                    flex: 1;
                  }
                }
                
                .devices-list {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 8px;
                  align-items: center;
                  
                  .device-tag {
                    margin: 0;
                  }
                  
                  .device-input {
                    min-width: 200px;
                    max-width: 300px;
                  }
                }
              }
            }
          }
        }
      }
      
      .config-preview {
        margin-top: 24px;
        
        .preview-textarea {
          margin-bottom: 12px;
          
          :deep(.el-textarea__inner) {
            font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            line-height: 1.5;
            background: #f8f9fa;
          }
        }
        
        .preview-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .help-section {
        margin-top: 24px;
        
        .help-content {
          p {
            margin: 8px 0;
            line-height: 1.6;
          }
          
          ul {
            margin: 8px 0;
            padding-left: 20px;
            
            li {
              margin: 4px 0;
              line-height: 1.5;
              
              code {
                background: #f1f2f3;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
    
    :deep(.el-card__body) {
      padding: 24px;
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .gateway-device-filtering-config {
      .config-content {
        .filtering-rules {
          .rules-container {
            .rule-section {
              .section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
              }
              
              .rules-list {
                .rule-card {
                  .rule-header {
                    flex-direction: column;
                    align-items: stretch;
                  }
                  
                  .devices-list {
                    .device-input {
                      min-width: 100%;
                    }
                  }
                }
              }
            }
          }
        }
        
        .config-preview {
          .preview-actions {
            flex-direction: column;
          }
        }
      }
    }
  }
  </style>