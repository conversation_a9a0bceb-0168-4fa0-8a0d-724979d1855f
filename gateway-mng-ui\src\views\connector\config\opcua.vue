<template>
  <div class="opcua-config">
    <el-tabs v-model="activeTab" class="opcua-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- 服务器配置标签页 -->
      <el-tab-pane label="服务器配置" name="server">
        <OpcuaServerConfig 
          v-if="config.server"
          v-model="config.server" 
        />
      </el-tab-pane>

      <!-- 设备映射标签页 -->
      <el-tab-pane label="设备映射" name="mapping">
        <OpcuaMappingConfig 
          v-if="config.mapping !== undefined"
          v-model="config.mapping" 
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import OpcuaServerConfig from './components/OpcuaServerConfig.vue'
import OpcuaMappingConfig from './components/OpcuaMappingConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  server: {
    url: 'opc.tcp://localhost:4840',
    timeoutInMillis: 5000,
    scanPeriodInMillis: 5000,
    pollPeriodInMillis: 5000,
    enableSubscriptions: true,
    subCheckPeriodInMillis: 100,
    showMap: false,
    security: 'Basic128Rsa15',
    identity: {
      type: 'anonymous'
    }
  },
  mapping: []
}

// 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'opcua', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
        
      console.log('OPC-UA 连接器配置初始化成功')
    } else {
      ElMessage.warning('OPC-UA 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('OPC-UA 连接器初始化失败:', error)
    ElMessage.error('OPC-UA 连接器初始化失败')
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  opcua: config
})
</script>

<style lang="scss" scoped>
.opcua-config {
  .opcua-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
}
</style>