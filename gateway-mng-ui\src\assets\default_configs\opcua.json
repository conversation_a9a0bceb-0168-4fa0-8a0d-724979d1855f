{"server": {"url": "localhost:4840/freeopcua/server/", "timeoutInMillis": 5000, "scanPeriodInMillis": 3600000, "pollPeriodInMillis": 5000, "enableSubscriptions": true, "subCheckPeriodInMillis": 100, "subDataMaxBatchSize": 1000, "subDataMinBatchCreationTimeMs": 200, "subscriptionProcessBatchSize": 2000, "sessionTimeoutInMillis": 120000, "showMap": false, "security": "Basic128Rsa15", "identity": {"type": "anonymous"}}, "mapping": [{"deviceNodePattern": "Root\\.Objects\\.Device1", "deviceNodeSource": "path", "deviceInfo": {"deviceNameExpression": "Device ${Root\\.Objects\\.Device1\\.serialNumber}", "deviceNameExpressionSource": "path", "deviceProfileExpression": "<PERSON><PERSON>", "deviceProfileExpressionSource": "constant"}, "attributes": [{"key": "temperature °C", "type": "path", "value": "${ns=2;i=5}"}], "timeseries": [{"key": "humidity", "type": "path", "value": "${Root\\.Objects\\.Device1\\.TemperatureAndHumiditySensor\\.Humidity}"}, {"key": "batteryLevel", "type": "path", "value": "${Battery\\.batteryLevel}"}], "rpc_methods": [{"method": "multiply", "arguments": [{"type": "integer", "value": 2}, {"type": "integer", "value": 4}]}], "attributes_updates": [{"key": "deviceName", "type": "path", "value": "Root\\.Objects\\.Device1\\.serialNumber"}]}]}