<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑设备' : '添加设备'"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="deviceForm" :rules="rules" ref="formRef" label-width="140px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称" prop="name" required>
                <el-input 
                  v-model="deviceForm.name" 
                  placeholder="Temperature and humidity sensor"
                />
                <div class="field-hint">BLE设备的显示名称</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="MAC地址" prop="MACAddress" required>
                <el-input 
                  v-model="deviceForm.MACAddress" 
                  placeholder="4C:65:A8:DF:85:C0"
                />
                <div class="field-hint">设备的蓝牙MAC地址</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="轮询周期" prop="pollPeriod" required>
                <el-input-number 
                  v-model="deviceForm.pollPeriod" 
                  :min="100" 
                  :step="1000"
                  controls-position="right"
                  placeholder="500000"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">数据轮询间隔时间</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="连接超时" prop="timeout" required>
                <el-input-number 
                  v-model="deviceForm.timeout" 
                  :min="1000" 
                  :step="1000"
                  controls-position="right"
                  placeholder="10000"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">BLE连接超时时间</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="显示映射">
                <el-switch v-model="deviceForm.showMap" />
                <div class="field-hint">是否显示设备映射信息</div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 高级连接配置 -->
          <el-card class="advanced-settings">
            <template #header>
              <div class="card-header">
                <span>连接配置</span>
                <el-button 
                  type="text" 
                  @click="showAdvanced = !showAdvanced"
                  :icon="showAdvanced ? ArrowUp : ArrowDown"
                >
                  {{ showAdvanced ? '收起' : '展开' }}
                </el-button>
              </div>
            </template>
            
            <el-collapse-transition>
              <div v-show="showAdvanced">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="重试次数" prop="connectRetry">
                      <el-input-number 
                        v-model="deviceForm.connectRetry" 
                        :min="0" 
                        :max="20"
                        controls-position="right"
                        placeholder="5"
                      />
                      <div class="field-hint">连接失败时的最大重试次数</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="重试间隔" prop="connectRetryInSeconds">
                      <el-input-number 
                        v-model="deviceForm.connectRetryInSeconds" 
                        :min="0" 
                        :max="60"
                        controls-position="right"
                        placeholder="0"
                      />
                      <span class="unit-suffix">秒</span>
                      <div class="field-hint">每次重试之间的间隔时间</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="等待时间" prop="waitAfterConnectRetries">
                      <el-input-number 
                        v-model="deviceForm.waitAfterConnectRetries" 
                        :min="1" 
                        :max="300"
                        controls-position="right"
                        placeholder="10"
                      />
                      <span class="unit-suffix">秒</span>
                      <div class="field-hint">达到最大重试次数后的等待时间</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-transition>
          </el-card>
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="telemetry">
          <BleDataPoints
            v-model="deviceForm.telemetry"
            data-type="telemetry"
            title="遥测数据配置"
            description="配置从设备读取的遥测数据"
          />
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <BleDataPoints
            v-model="deviceForm.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从设备读取的属性数据"
          />
        </el-tab-pane>

        <!-- 属性更新 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <BleDataPoints
            v-model="deviceForm.attributeUpdates"
            data-type="attributeUpdates"
            title="属性更新配置"
            description="配置向设备写入属性的操作"
          />
        </el-tab-pane>

        <!-- RPC配置 -->
        <el-tab-pane label="RPC配置" name="serverSideRpc">
          <BleDataPoints
            v-model="deviceForm.serverSideRpc"
            data-type="serverSideRpc"
            title="RPC方法配置"
            description="配置设备的远程过程调用方法"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BleDataPoints from './BleDataPoints.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  device: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const activeTab = ref('basic')
const showAdvanced = ref(false)
const saving = ref(false)

// 设备表单数据
const deviceForm = reactive({
  name: 'Temperature and humidity sensor',
  MACAddress: '',
  pollPeriod: 500000,
  showMap: false,
  timeout: 10000,
  connectRetry: 5,
  connectRetryInSeconds: 0,
  waitAfterConnectRetries: 10,
  telemetry: [],
  attributes: [],
  attributeUpdates: [],
  serverSideRpc: []
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  MACAddress: [
    { required: true, message: '请输入MAC地址', trigger: 'blur' },
    { pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/, message: '请输入有效的MAC地址格式', trigger: 'blur' }
  ],
  pollPeriod: [
    { required: true, message: '请输入轮询周期', trigger: 'blur' },
    { type: 'number', min: 100, message: '轮询周期不能小于100毫秒', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请输入连接超时时间', trigger: 'blur' },
    { type: 'number', min: 1000, message: '超时时间不能小于1000毫秒', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.device) {
    // 加载设备数据
    Object.assign(deviceForm, {
      name: 'Temperature and humidity sensor',
      MACAddress: '',
      pollPeriod: 500000,
      showMap: false,
      timeout: 10000,
      connectRetry: 5,
      connectRetryInSeconds: 0,
      waitAfterConnectRetries: 10,
      telemetry: [],
      attributes: [],
      attributeUpdates: [],
      serverSideRpc: [],
      ...props.device
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    // 重置表单
    activeTab.value = 'basic'
    showAdvanced.value = false
  }
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...deviceForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '设备更新成功' : '设备添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.unit-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.advanced-settings {
  margin-top: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .el-button {
      padding: 0;
      font-size: 14px;
      
      .el-icon {
        margin-left: 4px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  
  .el-input-number {
    width: 100%;
  }
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 