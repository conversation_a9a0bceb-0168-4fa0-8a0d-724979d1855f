<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="timeseriesForm" :rules="rules" ref="formRef" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="遥测名称" prop="name" required>
            <el-input 
              v-model="timeseriesForm.name" 
              placeholder="value"
            />
            <div class="field-hint">遥测数据在IoTCloud中的键名</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="值表达式" prop="value" required>
            <el-input 
              v-model="timeseriesForm.value" 
              placeholder="[i for i in [str_v, long_v, dbl_v, bool_v] if i is not None][0]"
            />
            <div class="field-hint">从查询结果提取值的Python表达式</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 表达式说明 -->
      <div class="expression-help">
        <el-alert
          title="Python表达式语法说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>基础语法:</strong></p>
            <ul>
              <li><strong>列名引用:</strong> 直接使用SQL查询结果的列名，如: str_v、long_v、dbl_v</li>
              <li><strong>条件表达式:</strong> value if condition else default_value</li>
              <li><strong>列表推导:</strong> [expression for item in list if condition]</li>
              <li><strong>函数调用:</strong> int(str_v)、float(dbl_v)、str(long_v)</li>
            </ul>
            
            <p><strong>常用表达式示例:</strong></p>
            <ul>
              <li><strong>选择非空值:</strong> [i for i in [str_v, long_v, dbl_v, bool_v] if i is not None][0]</li>
              <li><strong>类型转换:</strong> float(dbl_v) if dbl_v is not None else 0.0</li>
              <li><strong>字符串拼接:</strong> str(entity_id) + '_' + str(ts)</li>
              <li><strong>数学运算:</strong> dbl_v * 100 if dbl_v else 0</li>
              <li><strong>条件判断:</strong> 'high' if long_v > 100 else 'low'</li>
            </ul>
            
            <p><strong>注意事项:</strong></p>
            <ul>
              <li>确保表达式中使用的列名在SQL查询结果中存在</li>
              <li>处理可能的空值情况，避免运行时错误</li>
              <li>表达式结果应该是JSON可序列化的类型</li>
            </ul>
          </div>
        </el-alert>
      </div>

      <!-- 表达式测试 -->
      <div class="test-section">
        <el-form-item label="测试数据">
          <el-input 
            v-model="testData" 
            type="textarea" 
            :rows="3"
            placeholder='{"str_v": "test", "long_v": 123, "dbl_v": 45.67, "bool_v": true, "entity_id": "device1", "ts": 1634567890000}'
          />
          <div class="field-hint">输入模拟的查询结果JSON数据用于测试表达式</div>
        </el-form-item>
        <el-button type="success" @click="testExpression" :loading="testing">
          <el-icon><Promotion /></el-icon>
          测试表达式
        </el-button>
        <div v-if="testResult" class="test-result">
          <p><strong>测试结果:</strong> {{ testResult }}</p>
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { Promotion } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  timeseries: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)
const testing = ref(false)
const testData = ref('{"str_v": "test", "long_v": 123, "dbl_v": 45.67, "bool_v": true, "entity_id": "device1", "ts": 1634567890000}')
const testResult = ref('')

// 遥测表单数据
const timeseriesForm = reactive({
  name: '',
  value: ''
})

// 获取对话框标题
const getDialogTitle = () => {
  return props.isEdit ? '编辑遥测数据映射' : '添加遥测数据映射'
}

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入遥测名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '遥测名称必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入值表达式', trigger: 'blur' }
  ]
})

// 测试表达式
const testExpression = () => {
  if (!timeseriesForm.value) {
    ElMessage.warning('请先输入值表达式')
    return
  }
  
  if (!testData.value) {
    ElMessage.warning('请输入测试数据')
    return
  }
  
  testing.value = true
  testResult.value = ''
  
  try {
    // 解析测试数据
    const data = JSON.parse(testData.value)
    
    // 模拟表达式计算
    // 注意：这里只是模拟，实际应该发送到后端进行安全的表达式计算
    setTimeout(() => {
      try {
        // 简单的表达式处理示例
        let result
        const expression = timeseriesForm.value
        
        if (expression.includes('[i for i in') && expression.includes('if i is not None][0]')) {
          // 处理选择非空值的表达式
          const values = [data.str_v, data.long_v, data.dbl_v, data.bool_v].filter(i => i !== null && i !== undefined)
          result = values.length > 0 ? values[0] : null
        } else if (expression.includes('float(dbl_v)')) {
          result = data.dbl_v ? parseFloat(data.dbl_v) : 0.0
        } else if (expression.includes('int(long_v)')) {
          result = data.long_v ? parseInt(data.long_v) : 0
        } else {
          // 简单的列名替换
          const columns = Object.keys(data)
          let evalExpression = expression
          columns.forEach(col => {
            const regex = new RegExp(`\\b${col}\\b`, 'g')
            evalExpression = evalExpression.replace(regex, JSON.stringify(data[col]))
          })
          result = evalExpression
        }
        
        testResult.value = JSON.stringify(result)
        ElMessage.success('表达式测试成功')
      } catch (error) {
        testResult.value = '表达式计算错误: ' + error.message
        ElMessage.error('表达式计算失败')
      }
      testing.value = false
    }, 1000)
    
  } catch (error) {
    testing.value = false
    ElMessage.error('测试数据格式错误，请输入有效的JSON')
  }
}

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.timeseries) {
    // 加载遥测数据
    Object.assign(timeseriesForm, {
      name: '',
      value: '[i for i in [str_v, long_v, dbl_v, bool_v] if i is not None][0]',
      ...props.timeseries
    })
  } else if (newValue) {
    // 重置为默认值
    Object.assign(timeseriesForm, {
      name: '',
      value: '[i for i in [str_v, long_v, dbl_v, bool_v] if i is not None][0]'
    })
  }
  // 清空测试结果
  testResult.value = ''
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...timeseriesForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '遥测映射更新成功' : '遥测映射添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.expression-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
      
      strong {
        color: #409eff;
      }
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }
}

.test-section {
  margin-top: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f0f9ff;
  
  .test-result {
    margin-top: 12px;
    padding: 8px 12px;
    border-radius: 4px;
    background: #f5f7fa;
    border-left: 4px solid #409eff;
    
    p {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-textarea) {
  .el-textarea__inner {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
  }
}
</style> 