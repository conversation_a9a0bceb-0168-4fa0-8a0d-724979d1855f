<template>
  <el-card class="rest-attribute-request-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="card-title">
          <span>属性请求 {{ index + 1 }}</span>
          <el-tag :type="getTypeTagType(requestData.type)" size="small">
            {{ getTypeLabel(requestData.type) }}
          </el-tag>
          <el-tag v-for="method in requestData.HTTPMethods" :key="method" :type="getMethodTagType(method)" size="small">
            {{ method }}
          </el-tag>
        </div>
        <el-button type="danger" size="small" @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>
    
    <el-form :model="requestData" label-width="120px">
      <!-- 基础配置 -->
      <div class="section">
        <div class="section-title">请求配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="端点路径" required>
              <el-input 
                v-model="requestData.endpoint" 
                placeholder="/sharedAttributes"
                @input="updateValue"
              />
              <div class="field-hint">属性请求的端点路径</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="属性类型" required>
              <el-select v-model="requestData.type" @change="updateValue">
                <el-option label="共享属性" value="shared" />
                <el-option label="客户端属性" value="client" />
              </el-select>
              <div class="field-hint">请求的属性类型</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="HTTP方法" required>
              <el-select 
                v-model="requestData.HTTPMethods" 
                multiple 
                placeholder="选择HTTP方法"
                @change="updateValue"
              >
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
              </el-select>
              <div class="field-hint">允许的HTTP请求方法</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 安全配置 -->
      <div class="section">
        <div class="section-title">安全配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认证类型">
              <el-select v-model="requestData.security.type" @change="handleSecurityTypeChange">
                <el-option label="匿名访问" value="anonymous" />
                <el-option label="基本认证" value="basic" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="requestData.security.type === 'basic'">
            <el-form-item label="用户名">
              <el-input 
                v-model="requestData.security.username" 
                placeholder="username"
                @input="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="requestData.security.type === 'basic'">
            <el-form-item label="密码">
              <el-input 
                v-model="requestData.security.password" 
                type="password"
                placeholder="password"
                show-password
                @input="updateValue"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表达式配置 -->
      <div class="section">
        <div class="section-title">表达式配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="超时时间(秒)" required>
              <el-input-number 
                v-model="requestData.timeout" 
                :min="1" 
                :max="300"
                :step="1"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
              <div class="field-hint">请求超时时间</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名表达式" required>
              <el-input 
                v-model="requestData.deviceNameExpression" 
                placeholder="${deviceName}"
                @input="updateValue"
              />
              <div class="field-hint">从请求中提取设备名的表达式</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="属性名表达式" required>
              <el-input 
                v-model="requestData.attributeNameExpression" 
                placeholder="${attribute}"
                @input="updateValue"
              />
              <div class="field-hint">从请求中提取属性名的表达式，支持多个属性用逗号分隔，如：${pduAttribute}, ${versionAttribute}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表达式帮助 -->
      <div class="section">
        <el-collapse size="small">
          <el-collapse-item name="expressions" title="表达式使用说明">
            <div class="help-content">
              <h4>表达式语法</h4>
              <ul>
                <li><code>${deviceName}</code> - 从请求体中获取设备名</li>
                <li><code>${attribute}</code> - 从请求体中获取单个属性名</li>
                <li><code>${attr1}, ${attr2}</code> - 获取多个属性，用逗号分隔</li>
                <li><code>${data.deviceId}</code> - 获取嵌套对象中的值</li>
              </ul>
              
              <h4>请求示例</h4>
              <p>当客户端发送以下请求时：</p>
              <pre><code>{
  "deviceName": "SensorDevice001", 
  "attribute": "temperature"
}</code></pre>
              <p>系统会向IoTCloud请求设备 <code>SensorDevice001</code> 的 <code>temperature</code> 属性</p>
              
              <h4>多属性请求示例</h4>
              <pre><code>{
  "deviceName": "SensorDevice001",
  "pduAttribute": "powerData",
  "versionAttribute": "firmwareVersion"
}</code></pre>
              <p>使用表达式 <code>${pduAttribute}, ${versionAttribute}</code> 可以同时请求多个属性</p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 配置预览 -->
      <div class="section" v-if="requestData.endpoint">
        <div class="section-title">配置预览</div>
        <div class="preview-content">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="端点路径">
              <code class="endpoint-code">{{ requestData.endpoint }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="属性类型">
              <el-tag :type="getTypeTagType(requestData.type)" size="small">
                {{ getTypeLabel(requestData.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="HTTP方法">
              <el-tag 
                v-for="method in requestData.HTTPMethods" 
                :key="method" 
                :type="getMethodTagType(method)" 
                size="small"
                style="margin-right: 4px;"
              >
                {{ method }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="安全类型">
              <el-tag :type="getSecurityTagType(requestData.security.type)" size="small">
                {{ getSecurityLabel(requestData.security.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="超时时间">
              {{ requestData.timeout }}秒
            </el-descriptions-item>
            <el-descriptions-item label="设备名表达式">
              <code>{{ requestData.deviceNameExpression }}</code>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

// 请求数据
const requestData = computed({
  get: () => ({
    endpoint: '/sharedAttributes',
    type: 'shared',
    HTTPMethods: ['POST'],
    security: {
      type: 'anonymous'
    },
    timeout: 10,
    deviceNameExpression: '${deviceName}',
    attributeNameExpression: '${attribute}',
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 获取属性类型标签类型
const getTypeTagType = (type) => {
  return type === 'shared' ? 'success' : 'primary'
}

const getTypeLabel = (type) => {
  return type === 'shared' ? '共享属性' : '客户端属性'
}

// 获取HTTP方法标签类型
const getMethodTagType = (method) => {
  const methodTagMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning'
  }
  return methodTagMap[method] || 'info'
}

// 获取安全类型标签
const getSecurityTagType = (type) => {
  return type === 'anonymous' ? 'info' : 'success'
}

const getSecurityLabel = (type) => {
  return type === 'anonymous' ? '匿名' : '认证'
}

// 安全类型变化处理
const handleSecurityTypeChange = () => {
  const newData = { ...requestData.value }
  if (newData.security.type === 'anonymous') {
    delete newData.security.username
    delete newData.security.password
  } else {
    newData.security.username = ''
    newData.security.password = ''
  }
  emit('update:modelValue', newData)
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...requestData.value })
}

// 删除配置
const handleDelete = () => {
  emit('delete')
}
</script>

<style lang="scss" scoped>
.rest-attribute-request-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .help-content {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
    }
    
    ul {
      margin: 0 0 16px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
        font-size: 13px;
        color: #606266;
        line-height: 1.5;
        
        code {
          background: #f5f7fa;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
          color: #e6a23c;
        }
      }
    }
    
    p {
      margin: 8px 0;
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
    }
    
    pre {
      margin: 8px 0;
      padding: 12px;
      background: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
        font-size: 12px;
        line-height: 1.4;
        color: #24292e;
      }
    }
  }
  
  .preview-content {
    .endpoint-code {
      background: #f5f7fa;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
      color: #e6a23c;
      font-size: 12px;
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .el-collapse-item__content {
    padding: 12px 0 0 0;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style> 