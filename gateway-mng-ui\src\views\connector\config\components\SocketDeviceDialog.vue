<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑设备配置' : '添加设备配置'"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="deviceForm" :rules="rules" ref="formRef" label-width="140px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备地址筛选" prop="address" required>
                <el-input 
                  v-model="deviceForm.address" 
                  placeholder="*:* 或 *************:8080"
                />
                <div class="field-hint">设备地址筛选规则，支持通配符 * 匹配所有</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备名称" prop="deviceName" required>
                <el-input 
                  v-model="deviceForm.deviceName" 
                  placeholder="Device Example"
                />
                <div class="field-hint">设备在IoTCloud中显示的名称</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备类型" prop="deviceType" required>
                <el-input 
                  v-model="deviceForm.deviceType" 
                  placeholder="default"
                />
                <div class="field-hint">设备类型标识</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="编码方式" prop="encoding" required>
                <el-select 
                  v-model="deviceForm.encoding" 
                  placeholder="请选择编码方式"
                >
                  <el-option label="UTF-8" value="utf-8" />
                  <el-option label="UTF-16" value="utf-16" />
                  <el-option label="ASCII" value="ascii" />
                  <el-option label="Unicode" value="unicode" />
                  <el-option label="GBK" value="gbk" />
                  <el-option label="GB2312" value="gb2312" />
                  <el-option label="ANSI" value="ansi" />
                </el-select>
                <div class="field-hint">数据传输使用的字符编码</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <SocketDataKeys
            v-model="deviceForm.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从Socket接收的属性数据"
          />
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="telemetry">
          <SocketDataKeys
            v-model="deviceForm.telemetry"
            data-type="telemetry"
            title="遥测数据配置"
            description="配置从Socket接收的遥测数据"
          />
        </el-tab-pane>

        <!-- 属性请求配置 -->
        <el-tab-pane label="属性请求" name="attributeRequests">
          <SocketDataKeys
            v-model="deviceForm.attributeRequests"
            data-type="attributeRequests"
            title="属性请求配置"
            description="配置向设备请求属性的操作"
          />
        </el-tab-pane>

        <!-- 属性更新配置 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <SocketDataKeys
            v-model="deviceForm.attributeUpdates"
            data-type="attributeUpdates"
            title="属性更新配置"
            description="配置向设备写入属性的操作"
          />
        </el-tab-pane>

        <!-- RPC配置 -->
        <el-tab-pane label="RPC配置" name="serverSideRpc">
          <SocketDataKeys
            v-model="deviceForm.serverSideRpc"
            data-type="serverSideRpc"
            title="RPC方法配置"
            description="配置设备的远程过程调用方法"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import SocketDataKeys from './SocketDataKeys.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  device: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const activeTab = ref('basic')
const saving = ref(false)

// 设备表单数据
const deviceForm = reactive({
  address: '*:*',
  deviceName: 'Device Example',
  deviceType: 'default',
  encoding: 'utf-8',
  attributes: [],
  telemetry: [],
  attributeRequests: [],
  attributeUpdates: [],
  serverSideRpc: []
})

// 表单验证规则
const rules = reactive({
  address: [
    { required: true, message: '请输入设备地址筛选规则', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '请输入设备类型', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  encoding: [
    { required: true, message: '请选择编码方式', trigger: 'change' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.device) {
    // 加载设备数据
    Object.assign(deviceForm, {
      address: '*:*',
      deviceName: 'Device Example',
      deviceType: 'default',
      encoding: 'utf-8',
      attributes: [],
      telemetry: [],
      attributeRequests: [],
      attributeUpdates: [],
      serverSideRpc: [],
      ...props.device
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    // 重置表单
    activeTab.value = 'basic'
  }
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...deviceForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '设备更新成功' : '设备添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 