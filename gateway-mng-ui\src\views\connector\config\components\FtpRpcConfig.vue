<template>
  <div class="ftp-rpc-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>RPC配置</span>
          <el-button type="primary" size="small" @click="addRpc">
            <el-icon><Plus /></el-icon>
            添加RPC
          </el-button>
        </div>
      </template>
      
      <div v-if="rpcConfig.length === 0" class="empty-state">
        <el-empty description="暂无RPC配置">
          <el-button type="primary" @click="addRpc">添加第一个RPC</el-button>
        </el-empty>
      </div>
      
      <div v-else class="rpc-list">
        <el-card 
          v-for="(rpc, index) in rpcConfig" 
          :key="index" 
          class="rpc-card"
          shadow="hover"
        >
          <template #header>
            <div class="rpc-header">
              <div class="rpc-info">
                <span class="rpc-method">{{ rpc.methodFilter || '未设置方法' }}</span>
                <el-tag size="small" type="success">RPC</el-tag>
              </div>
              <div class="rpc-actions">
                <el-button type="primary" size="small" link @click="editRpc(rpc, index)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" link @click="deleteRpc(index)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="rpc-summary">
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">设备名筛选器:</span>
                  <span class="value">{{ rpc.deviceNameFilter || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">方法筛选:</span>
                  <span class="value">{{ rpc.methodFilter || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">值表达式:</span>
                  <span class="value">{{ rpc.valueExpression || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- RPC配置对话框 -->
    <FtpRpcDialog
      v-model="dialogVisible"
      :rpc="currentRpc"
      :is-edit="isEdit"
      @save="handleSaveRpc"
      @cancel="handleCancelRpc"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps, nextTick } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import FtpRpcDialog from './FtpRpcDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const currentRpc = ref(null)
const isEdit = ref(false)
const currentIndex = ref(-1)

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// RPC配置数据
const rpcConfig = reactive([])

// 添加RPC
const addRpc = () => {
  currentRpc.value = null
  isEdit.value = false
  currentIndex.value = -1
  dialogVisible.value = true
}

// 编辑RPC
const editRpc = (rpc, index) => {
  currentRpc.value = { ...rpc }
  isEdit.value = true
  currentIndex.value = index
  dialogVisible.value = true
}

// 删除RPC
const deleteRpc = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个RPC配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    rpcConfig.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存RPC
const handleSaveRpc = (rpcData) => {
  if (isEdit.value && currentIndex.value >= 0) {
    // 编辑模式
    rpcConfig.splice(currentIndex.value, 1, rpcData)
    ElMessage.success('RPC配置更新成功')
  } else {
    // 添加模式
    rpcConfig.push(rpcData)
    ElMessage.success('RPC配置添加成功')
  }
  
  dialogVisible.value = false
  handleChange()
}

// 取消操作
const handleCancelRpc = () => {
  dialogVisible.value = false
}

// 处理配置变化
const handleChange = () => {
  if (!isInternalUpdate.value) {
    const rpcs = rpcConfig.map(rpc => ({ ...rpc }))
    emit('update:modelValue', rpcs)
  }
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value && Array.isArray(newValue)) {
    isInternalUpdate.value = true
    rpcConfig.splice(0, rpcConfig.length, ...newValue)
    nextTick(() => {
      isInternalUpdate.value = false
    })
  }
}, { deep: true, immediate: true })

// 监听RPC配置变化
watch(rpcConfig, () => {
  handleChange()
}, { deep: true })
</script>

<style lang="scss" scoped>
.ftp-rpc-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .rpc-list {
    .rpc-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .rpc-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .rpc-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .rpc-method {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .rpc-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .rpc-summary {
        margin-top: 16px;
        
        .summary-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .label {
            font-size: 12px;
            color: #909399;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
            word-break: break-all;
          }
        }
      }
    }
  }
}
</style> 