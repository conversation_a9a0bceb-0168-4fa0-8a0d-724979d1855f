<template>
  <div class="base_box">
    <div class="background">
      <div class="top">
        <!-- 服务器信息 -->
        <div class="section-title">服务器配置</div>
        <el-row>
          <el-col :span="4">
            <span class="top_tital">服务器地址</span>
            <span class="top_data">{{ serverData?.thingsboard?.host }}</span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">服务器MQTT端口</span>
            <span class="top_data">{{ serverData?.thingsboard?.port }}</span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">消息安全等级</span>
            <span class="top_data">{{ serverData?.thingsboard?.qos }}</span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">数据统计</span>
            <span class="top_data">{{ serverData?.thingsboard?.statistics?.enable ? '开启' : '关闭' }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4">
            <span class="top_tital">设备筛选</span>
            <span class="top_data">{{ serverData?.thingsboard?.deviceFiltering?.enable ? '开启' : '关闭' }}</span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">身份认证方式</span>
            <span class="top_data"> {{ serverData?.thingsboard?.security?.type === 'token' ? 'TLS + token'
              : serverData?.thingsboard?.security?.type === 'private' ? 'TLS + 私钥' :
                serverData?.thingsboard?.security?.type
              }}
            </span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">设备活跃检查</span>
            <span class="top_data">{{ serverData?.thingsboard?.checkingDeviceActivity?.checkDeviceInactivity ? '开启' :
              '关闭'
              }}</span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">服务器连接状态</span>
            <span v-if="serverStatus" class="top_data">
              <el-icon color="#5BD25D">
                <SuccessFilled />
              </el-icon>
              正常
            </span>
            <span v-else class="top_data">
              <el-icon color="#F56C6C">
                <CircleCloseFilled />
              </el-icon>
              失败
            </span>
          </el-col>
        </el-row>

        <!-- 系统监控信息 -->
        <div class="section-title" style="margin-top: 20px;">系统监控</div>
        <el-row>
          <el-col :span="4">
            <span class="top_tital">CPU使用率</span>
            <span class="top_data">
              <el-progress 
                :percentage="systemData.cpu?.usage_percent || 0" 
                :color="getProgressColor(systemData.cpu?.usage_percent || 0)"
                :stroke-width="8"
                :show-text="false"
                style="width: 60px; display: inline-block; margin-right: 5px;"
              />
              {{ systemData.cpu?.usage_percent || 0 }}%
            </span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">内存使用率</span>
            <span class="top_data">
              <el-progress 
                :percentage="systemData.memory?.usage_percent || 0" 
                :color="getProgressColor(systemData.memory?.usage_percent || 0)"
                :stroke-width="8"
                :show-text="false"
                style="width: 60px; display: inline-block; margin-right: 5px;"
              />
              {{ systemData.memory?.usage_percent || 0 }}%
            </span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">系统温度</span>
            <span class="top_data">
              <svg 
                :style="{ color: getTemperatureColor(systemData.temperature?.cpu_average || systemData.temperature?.temperatures?.[0]?.temperature || 0), marginRight: '5px', width: '16px', height: '16px' }"
                viewBox="0 0 24 24" 
                fill="currentColor"
              >
                <path d="M12 2a1 1 0 0 1 1 1v1.5a1 1 0 1 1-2 0V3a1 1 0 0 1 1-1zm0 15a1 1 0 0 1 1 1v1a1 1 0 1 1-2 0v-1a1 1 0 0 1 1-1zm-5-5a1 1 0 0 1 1-1h1.5a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1zm11 0a1 1 0 0 1-1 1h-1.5a1 1 0 1 1 0-2H17a1 1 0 0 1 1 1z"/>
                <path d="M12 6a6 6 0 0 0-6 6c0 2.5 1.5 4.5 4 5.5V20a1 1 0 1 0 2 0v-2.5c2.5-1 4-3 4-5.5a6 6 0 0 0-6-6zm0 10a4 4 0 1 1 0-8 4 4 0 0 1 0 8z"/>
              </svg>
              {{ systemData.temperature?.cpu_average || systemData.temperature?.temperatures?.[0]?.temperature || 0 }}°C
              <span v-if="systemData.temperature?.cpu_cores?.length > 0" style="font-size: 12px; color: #909399;">
                ({{ systemData.temperature.cpu_cores.length }}核)
              </span>
            </span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">硬盘使用率</span>
            <span class="top_data">
              <el-progress 
                :percentage="getRootDiskUsage()" 
                :color="getProgressColor(getRootDiskUsage())"
                :stroke-width="8"
                :show-text="false"
                style="width: 60px; display: inline-block; margin-right: 5px;"
              />
              {{ getRootDiskUsage() }}%
            </span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4">
            <span class="top_tital">CPU核心数</span>
            <span class="top_data">{{ systemData.cpu?.cores || 0 }} 核</span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">内存总量</span>
            <span class="top_data">{{ formatBytes(systemData.memory?.total || 0) }}</span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">硬盘总量</span>
            <span class="top_data">{{ getRootDiskTotal() }}</span>
          </el-col>
          <el-col :span="4">
            <span class="top_tital">更新时间</span>
            <span class="top_data">{{ formatTime(systemData.timestamp) }}</span>
          </el-col>
        </el-row>
      </div>
    </div>

    <el-row :gutter="12" class="middle">
      <el-col :span="16">
        <div class="middle-left">
          <div class="middle-title">
            <div class="middle-icon"></div>
            <div>连接器类型数据</div>
          </div>
          <div ref="connectorBar" class="footer-left"> </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="middle-left">
          <div class="middle-title">
            <div class="middle-icon"></div>
            <div>连接器状态分布</div>
          </div>
          <el-empty v-show="isEmpty" :image-size="120" description="暂无数据" />
          <div v-show="!isEmpty" ref="connectorPie" class="footer-left"> </div>
        </div>
      </el-col>
    </el-row>

    <div class="bottom">
      <div class="middle-title" style="margin-bottom: 20px;">
        <div class="middle-icon"></div>
        <div>连接器列表</div>
      </div>
      <el-table :data="allConnector">
        <el-table-column prop="name" label="名称">
          <template #default="scope">
            <div style="display: flex;cursor: pointer;" @click="handleOpen(scope.row.name)">
              <img src="@/assets/images/home_icon.png" style="width: 22px;margin-right: 9px;" />
              <div class="tital-hover">{{ scope.row.name }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" />
        <el-table-column prop="configuration" label="文件名" />
        <el-table-column prop="status" label="连接状态">
          <template #default="scope">
            <div v-show="scope.row.status === 'config'" class="config">未配置</div>
            <div v-show="scope.row.status === 'success'" class="success">已连接</div>
            <div v-show="scope.row.status === 'fail'" class="fail">连接失败</div>
          </template>
        </el-table-column>
        <el-table-column prop="activeStatus" label="活跃状态">
          <template #default="scope">
            <div :class="scope.row.activeStatus ? 'success' : 'config'">{{ scope.row.activeStatus ? '活跃' : '不活跃' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="activeTime" label="最近活跃" />
      </el-table>

      <el-pagination :hide-on-single-page="!tableData.length" v-model:current-page="page" v-model:page-size="size"
        :page-sizes="[5, 10, 15]" layout="total, sizes,->, prev, pager, next" :total="tableData.length" background
        style="margin-top: 12px;" @size-change="getList" @current-change="getList" />
    </div>

    <el-drawer v-model="deviceDrawer" title="设备列表" direction="rtl" size="35%">
      <el-table :data="deviceData" v-loading="loading">
        <el-table-column property="index" label="序号" />
        <el-table-column property="deviceName" label="设备名" />
      </el-table>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { getAllFile, getFileFromName, getState, getLastActive, getDeviceFromTable, getStateList } from '@/api/file.js'
import { getSystemOverview } from '@/api/system.js'
import { getTimeDate, timestampToTime } from '@/utils/index'
import * as echarts from 'echarts';

const serverData = ref({})
const systemData = ref({
  temperature: {
    temperatures: [],
    cpu_average: null,
    cpu_cores: []
  },
  cpu: {},
  memory: {},
  disk: [],
  timestamp: 0
})
const connectorBar = ref()
const connectorPie = ref()
const tableData = ref([])
const serverStatus = ref(false)
const allConnector = ref([])
const page = ref(1)
const size = ref(10)
const isEmpty = ref(false)
const deviceDrawer = ref(false)
const deviceData = ref([])
const loading = ref(false)

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage < 50) return '#67C23A'
  if (percentage < 80) return '#E6A23C'
  return '#F56C6C'
}

// 获取温度颜色
const getTemperatureColor = (temperature) => {
  if (temperature < 50) return '#67C23A'
  if (temperature < 70) return '#E6A23C'
  return '#F56C6C'
}

// 格式化字节数
const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '--'
  const date = new Date(timestamp * 1000)
  return date.toLocaleTimeString()
}

// 获取系统监控数据
const getSystemData = async () => {
  try {
    const res = await getSystemOverview()
    if (res.msg === 'success') {
      // 直接使用返回的数据结构
      systemData.value = res.data
    }
  } catch (error) {
    console.error('获取系统监控数据失败:', error)
  }
}

// 定时更新系统监控数据
let systemTimer = null
const startSystemMonitor = () => {
  getSystemData()
  systemTimer = setInterval(getSystemData, 30000) // 每30秒更新一次
}

const stopSystemMonitor = () => {
  if (systemTimer) {
    clearInterval(systemTimer)
    systemTimer = null
  }
}

const getList = () => {
  // 先对数据进行排序
  const sortedData = [...tableData.value].sort((a, b) => {
    // 1. 按连接状态排序：已连接(success) > 已配置但未连接(fail) > 未配置(config)
    const statusOrder = { success: 0, fail: 1, config: 2 };
    if (statusOrder[a.status] !== statusOrder[b.status]) {
      return statusOrder[a.status] - statusOrder[b.status];
    }
    
    // 2. 按活跃状态排序：活跃 > 不活跃
    if (a.activeStatus !== b.activeStatus) {
      return a.activeStatus ? -1 : 1;
    }
    
    // 3. 按类型排序：有类型 > 无类型
    if (!!a.type !== !!b.type) {
      return a.type ? -1 : 1;
    }
    
    return 0;
  });
  
  // 分页
  allConnector.value = sortedData.slice(
    (page.value - 1) * size.value,
    page.value * size.value
  );
};

import { getConnectorTypes, getConnectorTypeName } from '@/api/connector.js'

onMounted(async () => {
  nextTick(async () => {
    // 启动系统监控
    startSystemMonitor()
    
    getState('gateway_s').then(res => {
      serverStatus.value = res.msg === 'success'
    })
    getFileFromName('tb_gateway.json').then(({ data }) => {
      serverData.value = data
    })
    
    // 获取连接器类型配置
    const typesRes = await getConnectorTypes()
    const connectorTypes = typesRes.data || { connector_instances: {} }

    getAllFile().then(res => {
      getState('connector_l').then(list => {
        if (list.msg === 'success') {
          // 过滤获取连接器列表
          const connectorFiles = res.data.filter(item => 
            !['tb_gateway.json', 'logs.json', 'connected_devices.json', 'list.json', 'statistics.json', 'connector_types.json'].includes(item)
          )
          
          // 获取连接器状态和活跃信息
          Promise.all([
            getStateList({ file_list: list.data }),
            getLastActive()
          ]).then(([statusRes, activeRes]) => {
            const connectorData = connectorFiles.map((item) => {
              const fileName = item.substring(0, item.indexOf(".json"))
              const connectorMetadata = connectorTypes.connector_instances[fileName] || {}
              const connectorType = connectorMetadata.type || ''
              
              // 直接使用 connector_types.json 中的名称，无需读取配置文件
              const actualName = connectorMetadata.name || fileName

              const obj = {
                type: connectorType,
                name: actualName, // 使用统一元数据中的名称
                configuration: item,
                status: 'config',
                activeStatus: false,
                activeTime: '暂无'
              }
              // 处理连接状态
              if (statusRes.data && Array.isArray(statusRes.data)) {
                for (const status of statusRes.data) {
                  for (const [key, value] of Object.entries(status)) {
                    if (key === actualName) {
                      try {
                        const connStatus = JSON.parse(value)
                        obj.status = connStatus.connected === true ? 'success' : 'fail'
                      } catch (error) {
                        console.warn(`解析连接状态失败:`, error)
                      }
                      break
                    }
                  }
                }
              }

              // 处理活跃状态和时间
              if (activeRes.data && Array.isArray(activeRes.data)) {
                const activeInfo = activeRes.data.find(it => it.connector_name === actualName)
                if (activeInfo) {
                  const lastTime = parseInt(activeInfo.last_time)
                  obj.activeStatus = getTimeDate(lastTime) < 10
                  obj.activeTime = timestampToTime(lastTime)
                }
              }

              return obj
            })
            tableData.value = connectorData.filter(Boolean)
            getList()
            
            // 统计连接器状态分布
            const statusCounts = tableData.value.reduce((acc, connector) => {
              if (connector.status === 'success') acc.connected++
              else if (connector.status === 'fail') acc.failed++
              else acc.unconfigured++
              return acc
            }, { connected: 0, failed: 0, unconfigured: 0 })
            
            isEmpty.value = tableData.value.length === 0
            const connectorPieIntance = echarts.init(connectorPie.value)
            connectorPieIntance.setOption({
              tooltip: {
                trigger: 'item',
                formatter: '{b}: {c} ({d}%)'
              },
              legend: {
                bottom: 'bottom',
                left: 'center'
              },
              color: ['#63D188', '#F35551', '#909399'],
              series: [
                {
                  name: '连接器状态',
                  type: 'pie',
                  radius: ['40%', '70%'],
                  avoidLabelOverlap: false,
                  label: {
                    show: false,
                    position: 'center'
                  },
                  emphasis: {
                    label: {
                      show: true,
                      fontSize: 40,
                      fontWeight: 'bold'
                    }
                  },
                  labelLine: {
                    show: false
                  },
                  data: [
                    { value: statusCounts.connected, name: '已连接' },
                    { value: statusCounts.failed, name: '连接失败' },
                    { value: statusCounts.unconfigured, name: '未配置' }
                  ]
                }
              ]
            })
            window.addEventListener('resize', () => {
              connectorPieIntance.resize()
            })

            // 生成连接器类型数据
            const typeList = tableData.value.map(connector => 
              connector.type ? getConnectorTypeName(connector.type) : null
            ).filter(Boolean)
            
            const type_X = [
              'MQTT', 'Modbus', 'OPC-UA', 'BLE', 'HTTP请求', 'CAN', 'BACnet', 'ODBC', 'REST', 'SNMP', 'FTP', 'Socket', 'XMPP', 'OCPP', 'GPIO'
            ]

            const type_YObj = typeList.reduce((obj, name) => {
              if (name in obj) {
                obj[name]++
              } else {
                obj[name] = 1
              }
              return obj
            }, {})

            const type_Y = type_X.map(item => type_YObj[item] || 0)

            const connectorBarIntance = echarts.init(connectorBar.value)
            connectorBarIntance.setOption({
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'shadow'
                }
              },
              xAxis: {
                type: 'category',
                data: type_X,
                axisLabel: {
                  interval: 0,
                  rotate: 45
                }
              },
              yAxis: {
                type: 'value',
                minInterval: 1
              },
              series: [
                {
                  data: type_Y,
                  type: 'bar',
                  barWidth: 22,
                  itemStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#3DB4F8'
                        },
                        {
                          offset: 1,
                          color: '#2E61DE'
                        }
                      ]
                    }
                  }
                }
              ]
            })
            window.addEventListener('resize', () => {
              connectorBarIntance.resize()
            })
          })
        } else {
          isEmpty.value = true
          tableData.value = []
          getList()
        }
      })

      // 统计连接器类型分布
      const typeList = tableData.value.map(connector => connector.type).filter(Boolean)
      let type_X = [
        'mqtt', 'modbus', 'opcua', 'ble', 'request', 'can', 'bacnet', 'odbc', 'rest', 'snmp', 'ftp', 'socket', 'xmpp', 'ocpp','gpio'
      ]
      let type_YObj = typeList.reduce((obj, name) => {
        if (name in obj) {
          obj[name]++
        } else {
          obj[name] = 1
        }
        return obj
      }, {})
      let type_Y = type_X.map(item => {
        return type_YObj[item] || 0
      })

      const connectorBarIntance = echarts.init(connectorBar.value);
      connectorBarIntance.setOption({
        xAxis: {
          type: 'category',
          data: type_X,
          axisLabel: {
            interval: 0,
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: type_Y,
            type: 'bar',
            barWidth: 22,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#3DB4F8'
                  },
                  {
                    offset: 1,
                    color: '#2E61DE'
                  }
                ]
              }
            }
          }
        ]
      })
      window.addEventListener('resize', () => {
        connectorBarIntance.resize()
      })
    })
  })
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopSystemMonitor()
})

const handleOpen = (name) => {
  loading.value = true
  deviceDrawer.value = true
  getDeviceFromTable({ device_list: [name] }).then(res => {
    let list = Object.values(res.data[0])[0]
    deviceData.value = list.map((item, index) => {
      return {
        index: (index + 1),
        deviceName: item.substring(item.lastIndexOf("\:") + 1, item.length).replace(/(^\s*)|(\s*$)/g, "")
      }
    })
    loading.value = false
  })
}

// 获取根目录硬盘使用率
const getRootDiskUsage = () => {
  if (!systemData.value.disk || !Array.isArray(systemData.value.disk)) {
    return 0
  }
  const rootDisk = systemData.value.disk.find(disk => disk.mount_point === '/')
  return rootDisk ? rootDisk.usage_percent : 0
}

// 获取根目录硬盘总量
const getRootDiskTotal = () => {
  if (!systemData.value.disk || !Array.isArray(systemData.value.disk)) {
    return '0B'
  }
  const rootDisk = systemData.value.disk.find(disk => disk.mount_point === '/')
  return rootDisk ? rootDisk.total : '0B'
}
</script>

<style lang="scss" scoped>
.base_box {
  background: transparent;
  box-shadow: 0 0px 0px #ccc;

  .background {
    background: #fff;
  }

  .top {
    background: linear-gradient(to left, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1)), url('@/assets/images/home_top.png') no-repeat 100%;
    background-size: cover;
    padding: 30px 30px 3px 30px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 15px;
      padding-left: 10px;
      border-left: 3px solid #3B8EFF;
    }

    .top_tital {
      font-size: 14px;
      color: #868686;
    }

    .top_data {
      display: inline-block;
      font-size: 14px;
      color: #242932;
      margin-left: 15px;
      vertical-align: middle;
    }

    .el-col {
      margin-bottom: 27px;
    }
  }
}

.tital-hover:hover {
  color: #3B8EFF;
}

.middle {
  margin: 12px 0;

  .middle-left {
    width: 100%;
    height: 300px;
    background-color: #fff;
    padding: 20px;

    .footer-left {
      width: 100%;
      height: 100%;
    }
  }
}

.middle-title {
  font-size: 14px;
  font-weight: bold;
  color: #454545;
  display: flex;
  align-items: center;

  .middle-icon {
    width: 4px;
    height: 14px;
    background: #3B8EFF;
    border-radius: 2px;
    margin-right: 6px;
  }
}

.bottom {
  width: 100%;
  background: #fff;
  padding: 20px 27px;

  .config {
    width: 67px;
    height: 25px;
    color: #909399;
    background: #F4F4F5;
    text-align: center;
    line-height: 25px;
    border-radius: 2px;
  }

  .success {
    width: 67px;
    height: 25px;
    color: #63D188;
    background: #CCF3E0;
    text-align: center;
    line-height: 25px;
    border-radius: 2px;
  }

  .fail {
    width: 67px;
    height: 25px;
    color: #F35551;
    background: #FFDFDE;
    text-align: center;
    line-height: 25px;
    border-radius: 2px;
  }
}

:deep(.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th) {
  background: #fff !important;
  color: #454545 !important;
}
</style>