<template>
  <el-card class="config-card">
    <template #header>
      <div class="card-header">
        <span>基础配置</span>
        <el-tooltip content="所有连接器的通用配置项" placement="top">
          <el-icon><QuestionFilled /></el-icon>
        </el-tooltip>
      </div>
    </template>
    
    <el-form :model="config" :rules="configRules" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="连接器ID">
            <el-input v-model="config.id" placeholder="自动生成" readonly />
            <div class="field-hint">连接器的唯一标识符（自动生成）</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="日志级别" prop="logLevel">
            <el-select v-model="config.logLevel" placeholder="请选择日志级别">
              <el-option label="DEBUG" value="DEBUG" />
              <el-option label="INFO" value="INFO" />
              <el-option label="WARNING" value="WARNING" />
              <el-option label="ERROR" value="ERROR" />
              <el-option label="CRITICAL" value="CRITICAL" />
            </el-select>
            <div class="field-hint">设置连接器的日志输出级别</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="启用远程日志">
            <el-switch v-model="config.enableRemoteLogging" />
            <div class="field-hint">是否将日志发送到IoTCloud平台</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 报告策略配置 -->
      <div class="report-strategy-section">
        <el-row :gutter="20" class="strategy-header-row">
          <el-col :span="12">
            <div class="strategy-label">
              <span class="strategy-text">报告策略</span>
              <el-switch v-model="reportStrategyEnabled" @change="toggleReportStrategy" />
            </div>
            <div class="field-hint">配置数据上报的策略和频率</div>
          </el-col>
        </el-row>
        
        <div v-if="reportStrategyEnabled" class="strategy-config">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="策略类型">
                <el-select v-model="config.reportStrategy.type" :disabled="!reportStrategyEnabled">
                  <el-option label="收到数据时上报" value="ON_RECEIVED" />
                  <el-option label="数据变化时上报" value="ON_CHANGE" />
                  <el-option label="定期上报" value="ON_REPORT_PERIOD" />
                  <el-option label="变化或定期上报" value="ON_CHANGE_OR_REPORT_PERIOD" />
                </el-select>
                <div class="field-hint">选择数据上报的触发条件</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item 
                label="报告周期 (ms)" 
                v-if="config.reportStrategy.type === 'ON_REPORT_PERIOD' || config.reportStrategy.type === 'ON_CHANGE_OR_REPORT_PERIOD'"
              >
                <el-input-number 
                  v-model="config.reportStrategy.reportPeriod" 
                  :min="1000" 
                  :step="1000"
                  :disabled="!reportStrategyEnabled"
                  placeholder="设置报告周期"
                  style="width: 100%"
                />
                <div class="field-hint">定期上报的时间间隔（毫秒）</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue'])

// 配置数据
const config = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 报告策略启用状态
const reportStrategyEnabled = ref(false)

// 表单验证规则
const configRules = {
  logLevel: [
    { required: true, message: '请选择日志级别', trigger: 'change' }
  ]
}

// 切换报告策略
const toggleReportStrategy = (enabled) => {
  if (!enabled) {
    // 禁用时重置为默认值
    config.value.reportStrategy = {
      type: 'ON_RECEIVED',
      reportPeriod: 60000
    }
  }
}

// 监听配置变化
watch(
  () => config.value,
  () => {
    // 检查报告策略状态
    reportStrategyEnabled.value = !!(config.value.reportStrategy && 
      config.value.reportStrategy.type !== 'ON_RECEIVED')
    
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.config-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #303133;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.report-strategy-section {
  margin-top: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f8f9fa;
  
  .strategy-header-row {
    margin-bottom: 0;
  }
  
  .strategy-label {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .strategy-text {
      font-weight: 500;
      color: #303133;
    }
  }
  
  .strategy-config {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  
  .el-input-number {
    width: 100%;
  }
}
</style> 