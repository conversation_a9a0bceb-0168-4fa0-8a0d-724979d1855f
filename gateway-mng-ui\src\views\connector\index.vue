<template>
  <div class="base_box">
    <el-button type="primary" @click="handleAdd()">新增</el-button>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="type" label="类型" align="center">
        <template #default="scope">
          {{ scope.row.type ? getConnectorTypeName(scope.row.type) : '未设置' }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" align="center" />
      <el-table-column prop="configuration" label="文件名" align="center" />
      <el-table-column label="本地连接器" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.isLocalOnly ? 'warning' : 'success'">
            {{ scope.row.isLocalOnly ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.enabled"
            style="--el-switch-on-color: #33C758;"
            :disabled="!scope.row.type"
            @change="handleStatusChange(scope.row)"
          />
          <el-tooltip
            v-if="!scope.row.type"
            content="请先设置连接器类型"
            placement="top"
          >
            <el-icon class="warning-icon"><Warning /></el-icon>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button size="small" link type="primary" @click="handleAdd(scope.row)">修改</el-button>
          <el-button size="small" link type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :hide-on-single-page="!allConnector.length" v-model:current-page="page" v-model:page-size="size"
      :page-sizes="[5, 10, 15]" layout="total, sizes,->, prev, pager, next" :total="allConnector.length" background
      @size-change="getList" @current-change="getList" />

    <!-- 添加类型选择对话框 -->
    <el-dialog v-model="typeDialogVisible" title="设置连接器类型" width="30%">
      <el-form :model="typeForm" label-width="120px">
        <el-form-item label="连接器类型">
          <el-select v-model="typeForm.type" placeholder="请选择连接器类型">
            <el-option 
              v-for="typeInfo in getSupportedConnectorTypes()" 
              :key="typeInfo.type" 
              :label="typeInfo.name" 
              :value="typeInfo.type" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="本地连接器">
          <el-switch 
            v-model="typeForm.isLocalOnly"
            active-text="是"
            inactive-text="否"
            disabled
          />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            （本地连接器可编辑，非本地连接器会被远程覆盖）
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="typeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleTypeConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router';
import { Warning } from '@element-plus/icons-vue'

import { getAllFile, deletaFile } from '@/api/file.js'
import { readJson, writeJson } from '@/api/gateway.js'
import { 
  getConnectorTypes, 
  SUPPORTED_TYPES, 
  getSupportedConnectorTypes,
  getConnectorTypeName,
  getConnectorMetadataByConfigFile,
  updateConnectorFullMetadata,
  deleteConnectorMetadata
} from '@/api/connector.js'

const router = useRouter();

const allConnector = ref([])
const tbGatewayConfig = ref({})
const connectorTypes = ref({})
const typeDialogVisible = ref(false)
const typeForm = ref({
  type: '',
  name: '',
  isLocalOnly: false
})

// 辅助函数：从配置文件名获取文件名（去掉.json后缀）
const getFileNameFromConfig = (configFile) => {
  return configFile.replace('.json', '')
}

onMounted(async () => {
  await getTbGatewayConfig()
  await getConnectorTypesInpage()
  getData()
})

const tableData = ref([])
const page = ref(1)
const size = ref(10)

const getTbGatewayConfig = async () => {
  try {
    const res = await readJson('tb_gateway.json')
    tbGatewayConfig.value = res.data
  } catch (error) {
    console.error('获取网关配置失败:', error)
  }
}

const getConnectorTypesInpage = async () => {
  try {
    const res = await getConnectorTypes()
    connectorTypes.value = res.data || { connector_instances: {} }
  } catch (error) {
    console.error('获取连接器类型配置失败:', error)
    connectorTypes.value = { connector_instances: {} }
  }
}

const handleStatusChange = async (row) => {
  try {
    // 更新tb_gateway.json中的连接器状态
    const connectors = tbGatewayConfig.value.connectors || []
    const connectorIndex = connectors.findIndex(c => c.configuration === row.configuration && c.type === row.type)
    
    if (row.enabled) {
      // 启用连接器时，添加完整的连接器信息
      // 使用配置文件中的实际name字段
      const newConnector = {
        type: row.type,
        name: row.name, // 使用配置文件中的实际名称
        configuration: row.configuration
      }
      // 如果是 GPIO 连接器，添加 class 配置
      if (row.type === 'gpio') {
        newConnector.class = 'CustomGpioConnector'
      }
      
      if (connectorIndex === -1) {
        connectors.push(newConnector)
      } else {
        connectors[connectorIndex] = newConnector
      }
    } else {
      // 禁用连接器时，从配置中删除该连接器
      if (connectorIndex !== -1) {
        connectors.splice(connectorIndex, 1)
      }
    }
    
    tbGatewayConfig.value.connectors = connectors
    
    // 保存更新后的配置
    await writeJson({
      file_name: 'tb_gateway.json',
      file_text: JSON.stringify(tbGatewayConfig.value, null, 2),
      file_type: 'string'
    })
    
    ElMessage({
      type: 'success',
      message: `连接器${row.enabled ? '启用' : '禁用'}成功`
    })
  } catch (error) {
    console.error('更新连接器状态失败:', error)
    ElMessage({
      type: 'error',
      message: '更新连接器状态失败'
    })
    // 恢复开关状态
    row.enabled = !row.enabled
  }
}

const getData = async () => {
  allConnector.value = []
  try {
    // 1. 获取配置文件列表（只需要文件名，不读取内容）
    const filesRes = await getAllFile()
    const configFiles = filesRes.data.filter(item => 
      item !== 'tb_gateway.json' && 
      item !== 'logs.json' && 
      item !== 'connected_devices.json' &&
      item !== 'list.json' && 
      item !== 'statistics.json' && 
      item !== 'connector_types.json' &&
      item.endsWith('.json')
    )
    
    // 2. 基于配置文件列表和统一元数据构建连接器列表（不读取配置文件内容）
    allConnector.value = configFiles.map(configFile => {
      const connectorMetadata = getConnectorMetadataByConfigFile(configFile, connectorTypes.value)
      
      // 构建连接器对象，所有信息都来自connector_types.json
      const connector = {
        type: connectorMetadata?.type || '', // 类型信息
        name: connectorMetadata?.name || getFileNameFromConfig(configFile), // 显示名称
        configuration: configFile, // 配置文件名
        isLocalOnly: connectorMetadata?.isLocalOnly || false, // 本地连接器标识
        enabled: false // 启用状态（后续从tb_gateway.json获取）
      }
      
      // 设置特殊连接器的class属性
      if (connector.type === 'gpio') {
        connector.class = 'CustomGpioConnector'
      }
      
      return connector
    })
    
    // 3. 从tb_gateway.json中获取连接器启用状态
    if (tbGatewayConfig.value.connectors) {
      allConnector.value.forEach(connector => {
        connector.enabled = tbGatewayConfig.value.connectors.some(
          c => c.configuration === connector.configuration && c.type === connector.type
        )
      })
    }
    
    getList()
  } catch (error) {
    console.error('获取连接器列表失败:', error)
  }
}
const getList = () => {
  tableData.value = allConnector.value.slice(
    (page.value - 1) * size.value,
    page.value * size.value
  );
}
const handleAdd = (row) => {
  if (!row) {
    // 新增连接器，直接跳转到添加页面
    router.push({ path: '/connector/addConnector' })
  } else if (!row.type) {
    // 如果没有类型，显示类型选择对话框
    typeForm.value = {
      type: '',
      name: row.name, // 直接使用元数据中的名称
      isLocalOnly: row.isLocalOnly || false // 保持现有状态或默认为非本地连接器
    }
    typeDialogVisible.value = true
  } else {
    // 编辑连接器，传递元数据信息到编辑页面
    const queryData = { ...row }
    router.push({ path: '/connector/addConnector', query: queryData })
  }
}
const handleDelete = (row) => {
  ElMessageBox.confirm(
    '此操作将删除名称为"' + row.name + '"的连接器配置信息，是否继续？',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      // 删除连接器配置文件
      await deletaFile(row.configuration)
      
      // 从 tb_gateway.json 中删除连接器配置
      if (tbGatewayConfig.value.connectors) {
        const connectorIndex = tbGatewayConfig.value.connectors.findIndex(
          c => c.configuration === row.configuration && c.type === row.type
        )
        if (connectorIndex !== -1) {
          tbGatewayConfig.value.connectors.splice(connectorIndex, 1)
          // 保存更新后的 tb_gateway.json
          await writeJson({
            file_name: 'tb_gateway.json',
            file_text: JSON.stringify(tbGatewayConfig.value, null, 2),
            file_type: 'string'
          })
        }
      }
      
      // 从 connector_types.json 中删除连接器元数据
      const fileNameKey = getFileNameFromConfig(row.configuration)
      try {
        await deleteConnectorMetadata(fileNameKey)
      } catch (error) {
        console.warn('删除连接器元数据失败:', error)
      }
      
      ElMessage({ type: 'success', message: '删除成功！' })
      getData()
    } catch (error) {
      console.error('删除连接器失败:', error)
      ElMessage({ type: 'error', message: '删除失败，请重试！' })
    }
  }).catch(() => { })
}

const handleTypeConfirm = async () => {
  if (!typeForm.value.type) {
    ElMessage.warning('请选择连接器类型')
    return
  }

  try {
    // 使用文件名作为键来更新连接器完整元数据
    const fileNameKey = typeForm.value.name
    await updateConnectorFullMetadata(fileNameKey, typeForm.value.type, typeForm.value.name, typeForm.value.isLocalOnly)
    
    // 更新本地数据
    const connector = allConnector.value.find(c => getFileNameFromConfig(c.configuration) === fileNameKey)
    if (connector) {
      connector.type = typeForm.value.type
      connector.name = typeForm.value.name
      connector.isLocalOnly = typeForm.value.isLocalOnly
      if (connector.type === 'gpio') {
        connector.class = 'CustomGpioConnector'
      }
    }
    
    ElMessage.success('设置类型成功')
    typeDialogVisible.value = false
    getList()
  } catch (error) {
    console.error('设置连接器类型失败:', error)
    ElMessage.error('设置类型失败，请重试')
  }
}
</script>

<style lang="scss" scoped>
.base_box{
  padding: 20px;
}
.el-table {
  margin: 12px 0;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.warning-icon {
  margin-left: 8px;
  color: #E6A23C;
  font-size: 16px;
}
</style>