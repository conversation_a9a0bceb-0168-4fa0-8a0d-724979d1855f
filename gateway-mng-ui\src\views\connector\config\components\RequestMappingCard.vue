<template>
  <el-card class="mapping-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="card-title">
          <span>映射配置 {{ index + 1 }}</span>
          <el-tag 
            :type="mappingData.httpMethod === 'GET' ? 'success' : 'info'" 
            size="small"
          >
            {{ mappingData.httpMethod || 'GET' }}
          </el-tag>
        </div>
        <el-button type="danger" size="small" @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>

    <el-form :model="mappingData" label-width="120px">
      <!-- 基础请求配置 -->
      <div class="section">
        <div class="section-title">请求配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="URL路径" required>
              <el-input 
                v-model="mappingData.url" 
                placeholder="getdata"
              />
              <div class="field-hint">相对于服务器地址的URL路径</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="HTTP方法" required>
              <el-select v-model="mappingData.httpMethod" style="width: 100%">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
                <el-option label="HEAD" value="HEAD" />
                <el-option label="OPTIONS" value="OPTIONS" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="超时(秒)">
              <el-input-number 
                v-model="mappingData.timeout" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="扫描周期(秒)">
              <el-input-number 
                v-model="mappingData.scanPeriod" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="允许重定向">
              <el-switch v-model="mappingData.allowRedirects" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        <RequestHttpHeaders v-model="mappingData.httpHeaders" />
      </div>

      <!-- 请求内容配置 -->
      <div class="section" v-if="mappingData.httpMethod !== 'GET'">
        <div class="section-title">请求内容</div>
        <RequestContentConfig v-model="mappingData.content" />
      </div>

      <!-- 数据转换器配置 -->
      <div class="section">
        <div class="section-title">数据转换器</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="转换器类型" required>
              <el-select v-model="mappingData.converter.type" style="width: 100%">
                <el-option label="JSON转换器" value="json" />
                <el-option label="自定义转换器" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名表达式" required>
              <el-input 
                v-model="mappingData.converter.deviceNameJsonExpression" 
                placeholder="SD8500"
              />
              <div class="field-hint">设备名称的JSON表达式或常量</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型表达式">
              <el-input 
                v-model="mappingData.converter.deviceTypeJsonExpression" 
                placeholder="SD"
              />
              <div class="field-hint">设备类型的JSON表达式或常量</div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- JSON转换器配置 -->
        <template v-if="mappingData.converter.type === 'json'">
          <!-- 属性配置 -->
          <div class="converter-section">
            <div class="converter-title">
              <span>属性配置</span>
              <el-button type="primary" size="small" @click="addAttribute">
                <el-icon><Plus /></el-icon>
                添加属性
              </el-button>
            </div>
            
            <div v-if="!mappingData.converter.attributes?.length" class="empty-data">
              <el-empty description="暂无属性配置" :image-size="60">
                <el-button type="primary" size="small" @click="addAttribute">添加第一个属性</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-keys-list">
              <RequestDataKey
                v-for="(attr, attrIndex) in mappingData.converter.attributes"
                :key="`attr-${attrIndex}`"
                v-model="mappingData.converter.attributes[attrIndex]"
                data-type="attribute"
                @delete="removeAttribute(attrIndex)"
              />
            </div>
          </div>

          <!-- 遥测配置 -->
          <div class="converter-section">
            <div class="converter-title">
              <span>遥测配置</span>
              <el-button type="primary" size="small" @click="addTelemetry">
                <el-icon><Plus /></el-icon>
                添加遥测
              </el-button>
            </div>
            
            <div v-if="!mappingData.converter.telemetry?.length" class="empty-data">
              <el-empty description="暂无遥测配置" :image-size="60">
                <el-button type="primary" size="small" @click="addTelemetry">添加第一个遥测</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-keys-list">
              <RequestDataKey
                v-for="(tel, telIndex) in mappingData.converter.telemetry"
                :key="`tel-${telIndex}`"
                v-model="mappingData.converter.telemetry[telIndex]"
                data-type="telemetry"
                @delete="removeTelemetry(telIndex)"
              />
            </div>
          </div>
        </template>

        <!-- 自定义转换器配置 -->
        <template v-if="mappingData.converter.type === 'custom'">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="扩展类名" required>
                <el-input 
                  v-model="mappingData.converter.extension" 
                  placeholder="CustomRequestUplinkConverter"
                />
                <div class="field-hint">自定义转换器的类名</div>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="converter-section">
            <div class="converter-title">
              <span>自定义解析配置</span>
              <el-button type="primary" size="small" @click="addExtensionConfig">
                <el-icon><Plus /></el-icon>
                添加配置
              </el-button>
            </div>
            
            <div v-if="!mappingData.converter['extension-config']?.length" class="empty-data">
              <el-empty description="暂无解析配置" :image-size="60">
                <el-button type="primary" size="small" @click="addExtensionConfig">添加第一个配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="extension-configs-list">
              <RequestExtensionConfig
                v-for="(config, configIndex) in mappingData.converter['extension-config']"
                :key="`ext-${configIndex}`"
                v-model="mappingData.converter['extension-config'][configIndex]"
                @delete="removeExtensionConfig(configIndex)"
              />
            </div>
          </div>
        </template>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import RequestHttpHeaders from './RequestHttpHeaders.vue'
import RequestContentConfig from './RequestContentConfig.vue'
import RequestDataKey from './RequestDataKey.vue'
import RequestExtensionConfig from './RequestExtensionConfig.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

// 映射数据
const mappingData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 删除映射
const handleDelete = () => {
  emit('delete')
}

// 添加属性
const addAttribute = () => {
  if (!mappingData.value.converter.attributes) {
    mappingData.value.converter.attributes = []
  }
  mappingData.value.converter.attributes.push({
    key: '',
    type: 'string',
    value: ''
  })
}

// 删除属性
const removeAttribute = (index) => {
  mappingData.value.converter.attributes.splice(index, 1)
}

// 添加遥测
const addTelemetry = () => {
  if (!mappingData.value.converter.telemetry) {
    mappingData.value.converter.telemetry = []
  }
  mappingData.value.converter.telemetry.push({
    key: '',
    type: 'string',
    value: ''
  })
}

// 删除遥测
const removeTelemetry = (index) => {
  mappingData.value.converter.telemetry.splice(index, 1)
}

// 添加扩展配置
const addExtensionConfig = () => {
  if (!mappingData.value.converter['extension-config']) {
    mappingData.value.converter['extension-config'] = []
  }
  mappingData.value.converter['extension-config'].push({
    key: '',
    type: 'float',
    fromByte: 0,
    toByte: 4,
    byteorder: 'big',
    signed: true,
    multiplier: 1
  })
}

// 删除扩展配置
const removeExtensionConfig = (index) => {
  mappingData.value.converter['extension-config'].splice(index, 1)
}
</script>

<style lang="scss" scoped>
.mapping-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
  
  .converter-section {
    margin-bottom: 20px;
    
    .converter-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      font-weight: 600;
      color: #606266;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .empty-data {
    text-align: center;
    padding: 20px 0;
  }
  
  .data-keys-list,
  .extension-configs-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style> 