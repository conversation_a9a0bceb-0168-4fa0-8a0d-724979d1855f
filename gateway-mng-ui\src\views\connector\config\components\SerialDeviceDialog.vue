<template>
  <div class="serial-device-dialog">
    <el-form :model="deviceConfig" :rules="deviceRules" ref="deviceFormRef" label-width="140px">
      <!-- 基础连接配置 -->
      <div class="config-section">
        <div class="section-title">设备连接配置</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名称" prop="name" required>
              <el-input v-model="deviceConfig.name" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="type">
              <el-input v-model="deviceConfig.type" placeholder="default" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="串口号" prop="port" required>
              <el-input v-model="deviceConfig.port" placeholder="/dev/ttyUSB0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="波特率" prop="baudrate" required>
              <el-select v-model="deviceConfig.baudrate" placeholder="选择波特率">
                <el-option v-for="rate in baudrateOptions" :key="rate" :label="rate" :value="rate" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 转换器配置 -->
      <div class="config-section">
        <div class="section-title">转换器配置</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上行转换器" prop="converter">
              <el-input v-model="deviceConfig.converter" placeholder="SerialUplinkConverter" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下行转换器" prop="downlink_converter">
              <el-input v-model="deviceConfig.downlink_converter" placeholder="SerialDownlinkConverter" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 数据点配置 -->
      <div class="config-section">
        <div class="section-title">数据点配置</div>
        <SerialDataPoints 
          v-model="deviceDataPoints" 
          @update:modelValue="handleDataPointsUpdate" />
      </div>
    </el-form>

    <!-- 对话框底部按钮 -->
    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        {{ isEdit ? '更新' : '添加' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import SerialDataPoints from './SerialDataPoints.vue'

// Props
const props = defineProps({
  deviceConfig: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['save', 'cancel'])

// 响应式数据
const deviceFormRef = ref(null)
const saving = ref(false)

// 数据点配置
const deviceDataPoints = reactive({
  telemetry: [],
  attributes: [],
  attributeUpdates: [],
  serverSideRpc: []
})

const deviceConfig = reactive({
  name: '',
  type: 'default',
  port: '/dev/ttyUSB0',
  baudrate: 9600,
  converter: 'SerialUplinkConverter',
  downlink_converter: 'SerialDownlinkConverter',
  telemetry: [],
  attributes: [],
  attributeUpdates: [],
  serverSideRpc: [],
  ...props.deviceConfig
})

// 波特率选项
const baudrateOptions = [4800, 9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]

// 表单验证规则
const deviceRules = reactive({
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入串口号', trigger: 'blur' }
  ],
  baudrate: [
    { required: true, message: '请选择波特率', trigger: 'change' }
  ]
})

// 方法
const handleDataPointsUpdate = (updatedDataPoints) => {
  // 检查是否真的有变化，避免不必要的更新
  let hasChanges = false
  
  Object.keys(updatedDataPoints).forEach(key => {
    if (Array.isArray(updatedDataPoints[key])) {
      // 只在数据真正变化时才更新
      if (JSON.stringify(updatedDataPoints[key]) !== JSON.stringify(deviceDataPoints[key])) {
        deviceConfig[key] = [...updatedDataPoints[key]]
        deviceDataPoints[key] = [...updatedDataPoints[key]]
        hasChanges = true
      }
    }
  })
  
  // 只在有变化时输出调试信息
  if (hasChanges) {
    console.log('串口设备数据点配置已更新:', {
      telemetry: deviceDataPoints.telemetry?.length || 0,
      attributes: deviceDataPoints.attributes?.length || 0,
      attributeUpdates: deviceDataPoints.attributeUpdates?.length || 0,
      serverSideRpc: deviceDataPoints.serverSideRpc?.length || 0
    })
  }
}

const handleSave = async () => {
  try {
    await deviceFormRef.value.validate()
    saving.value = true
    
    // 确保数据点配置是最新的
    const configToSave = { ...deviceConfig }
    configToSave.telemetry = deviceDataPoints.telemetry
    configToSave.attributes = deviceDataPoints.attributes
    configToSave.attributeUpdates = deviceDataPoints.attributeUpdates
    configToSave.serverSideRpc = deviceDataPoints.serverSideRpc
    
    emit('save', configToSave)
    ElMessage.success(props.isEdit ? '设备配置已更新' : '设备配置已添加')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单配置')
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听外部配置变化（避免深度监听导致循环更新）
watch(() => props.deviceConfig, (newConfig) => {
  if (!newConfig) return
  
  Object.assign(deviceConfig, newConfig)
  
  // 初始化数据点配置，使用条件检查避免不必要的更新
  if (newConfig.telemetry && JSON.stringify(newConfig.telemetry) !== JSON.stringify(deviceDataPoints.telemetry)) {
    deviceDataPoints.telemetry = [...newConfig.telemetry]
  }
  if (newConfig.attributes && JSON.stringify(newConfig.attributes) !== JSON.stringify(deviceDataPoints.attributes)) {
    deviceDataPoints.attributes = [...newConfig.attributes]
  }
  if (newConfig.attributeUpdates && JSON.stringify(newConfig.attributeUpdates) !== JSON.stringify(deviceDataPoints.attributeUpdates)) {
    deviceDataPoints.attributeUpdates = [...newConfig.attributeUpdates]
  }
  if (newConfig.serverSideRpc && JSON.stringify(newConfig.serverSideRpc) !== JSON.stringify(deviceDataPoints.serverSideRpc)) {
    deviceDataPoints.serverSideRpc = [...newConfig.serverSideRpc]
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.serial-device-dialog {
  .config-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input), :deep(.el-select) {
    width: 100%;
  }
}
</style> 