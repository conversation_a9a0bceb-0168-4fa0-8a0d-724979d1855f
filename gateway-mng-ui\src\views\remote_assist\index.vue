<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <span>远程协助配置</span>
      </div>

      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="远程协助密钥: " required>
          <el-input 
            v-model="form.vkey" 
            placeholder="请联系技术支持人员获取远程协助密钥"
            :disabled="form.configured"
            style="width: 400px">
          </el-input>
          <el-button 
            v-if="!form.configured" 
            type="primary" 
            style="margin-left: 10px"
            @click="saveVKey">
            保存
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card style="margin-top: 20px">
      <div slot="header">
        <span>远程协助控制</span>
      </div>

      <div class="control-panel">
        <div class="status">
          当前状态: 
          <el-tag :type="status === 'running' ? 'success' : 'info'">
            {{ status === 'running' ? '运行中' : '已停止' }}
          </el-tag>
        </div>

        <div class="actions">
          <el-button 
            type="primary"
            :disabled="!form.configured || status === 'running'"
            @click="startAssist">
            启动远程协助
          </el-button>
          <el-button 
            type="danger"
            :disabled="status !== 'running'"
            @click="stopAssist">
            停止远程协助
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getConfig, setConfig, startAssist, stopAssist, getStatus } from '@/api/remote_assist'

export default {
  name: 'RemoteAssist',
  
  data() {
    return {
      form: {
        vkey: '',
        configured: false
      },
      status: 'stopped',
      timer: null
    }
  },

  created() {
    this.getConfigInfo()
    this.startStatusPolling()
  },

  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },

  methods: {
    async getConfigInfo() {
      try {
        const res = await getConfig()
        if (res.msg === 'success') {
          this.form.vkey = res.data.vkey
          this.form.configured = res.data.configured
        }
      } catch (error) {
        console.error('获取配置失败:', error)
      }
    },

    async saveVKey() {
      if (!this.form.vkey) {
        this.$message.warning('请输入VKey')
        return
      }

      try {
        const res = await setConfig(this.form.vkey)
        if (res.msg === 'success') {
          this.$message.success('VKey配置成功')
          await this.getConfigInfo()
        } else {
          this.$message.error(res.data || '配置失败')
        }
      } catch (error) {
        this.$message.error('配置失败: ' + error)
      }
    },

    startStatusPolling() {
      this.getStatus()
      this.timer = setInterval(() => {
        this.getStatus()
      }, 5000)
    },

    async getStatus() {
      try {
        const res = await getStatus()
        if (res.msg === 'success') {
          this.status = res.data.status
        }
      } catch (error) {
        console.error('获取状态失败:', error)
      }
    },

    async startAssist() {
      try {
        const res = await startAssist()
        if (res.msg === 'success') {
          this.$message.success('远程协助已启动')
          await this.getStatus()
        } else {
          this.$message.error(res.data || '启动失败')
        }
      } catch (error) {
        this.$message.error('启动失败: ' + error)
      }
    },

    async stopAssist() {
      try {
        const res = await stopAssist()
        if (res.msg === 'success') {
          this.$message.success('远程协助已停止')
          await this.getStatus()
        } else {
          this.$message.error(res.data || '停止失败')
        }
      } catch (error) {
        this.$message.error('停止失败: ' + error)
      }
    }
  }
}
</script>

<style scoped>
.control-panel {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0;
}

.status {
  font-size: 16px;
}

.actions {
  display: flex;
  gap: 20px;
}
</style>