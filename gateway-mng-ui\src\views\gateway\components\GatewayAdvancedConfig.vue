<template>
  <div class="gateway-advanced-config">
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>高级配置 - JSON编辑器</span>
          <div class="header-actions">
            <el-button size="small" @click="formatJson">格式化</el-button>
            <el-button size="small" @click="validateJson">验证</el-button>
            <el-button size="small" @click="resetConfig" type="warning">重置</el-button>
          </div>
        </div>
      </template>
      
      <div class="editor-container">
        <el-input
          v-model="configText"
          type="textarea"
          :rows="30"
          placeholder="请输入JSON配置..."
          @input="handleInput"
          class="json-editor"
        />
      </div>
      
      <div class="editor-footer">
        <div class="status-info">
          <el-tag v-if="isValidJson" type="success">
            <el-icon><Check /></el-icon>
            JSON格式正确
          </el-tag>
          <el-tag v-else type="danger">
            <el-icon><Close /></el-icon>
            JSON格式错误
          </el-tag>
          <span class="line-count">行数: {{ lineCount }}</span>
        </div>
        
        <div class="editor-tips">
          <el-alert
            title="提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>在高级模式下，您可以直接编辑完整的网关配置JSON。请确保JSON格式正确，否则可能导致网关无法正常工作。</p>
              <p>建议在修改前备份当前配置，并在测试环境中验证配置的正确性。</p>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, watch, computed, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { cleanEmptyValues } from '@/utils/configUtils.js'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const configText = ref('')
const isValidJson = ref(true)

// 计算行数
const lineCount = computed(() => {
  return configText.value.split('\n').length
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    try {
      configText.value = JSON.stringify(newValue, null, 2)
      isValidJson.value = true
    } catch (error) {
      console.error('配置对象转换为JSON失败:', error)
      isValidJson.value = false
    }
  }
}, { deep: true, immediate: true })



// 处理输入变化
const handleInput = () => {
  try {
    const parsedConfig = JSON.parse(configText.value)
    isValidJson.value = true
    
    // 在高级模式下也应用清理逻辑
    const cleanedConfig = cleanEmptyValues(parsedConfig)
    
    emit('update:modelValue', cleanedConfig)
    emit('change', cleanedConfig)
  } catch (error) {
    isValidJson.value = false
    // 不发送无效的配置
  }
}

// 格式化JSON
const formatJson = () => {
  try {
    const parsedConfig = JSON.parse(configText.value)
    const cleanedConfig = cleanEmptyValues(parsedConfig)
    configText.value = JSON.stringify(cleanedConfig, null, 2)
    isValidJson.value = true
    
    // 更新配置
    emit('update:modelValue', cleanedConfig)
    emit('change', cleanedConfig)
    
    ElMessage.success('JSON格式化并清理空值成功')
  } catch (error) {
    ElMessage.error('JSON格式错误，无法格式化')
  }
}

// 验证JSON
const validateJson = () => {
  try {
    JSON.parse(configText.value)
    isValidJson.value = true
    ElMessage.success('JSON格式验证通过')
  } catch (error) {
    isValidJson.value = false
    ElMessage.error(`JSON格式错误: ${error.message}`)
  }
}

// 重置配置
const resetConfig = () => {
  const defaultConfig = {
    thingsboard: {
      host: 'localhost',
      port: 1883,
      remoteShell: true,
      remoteConfiguration: true,
      security: {
        type: 'accessToken',
        accessToken: ''
      },
      reportStrategy: {
        type: 'ON_RECEIVED',
        reportPeriod: 60000
      },
      statistics: {
        enable: true,
        statsSendPeriodInSeconds: 60,
        customStatsSendPeriodInSeconds: 3600,
        commands: []
      },
      checkingDeviceActivity: {
        checkDeviceInactivity: true,
        inactivityTimeoutSeconds: 300,
        inactivityCheckPeriodSeconds: 10
      },
      maxPayloadSizeBytes: 8196,
      minPackSendDelayMS: 50,
      minPackSizeToSend: 500,
      checkConnectorsConfigurationInSeconds: 60,
      handleDeviceRenaming: true,
      qos: 1
    },
    storage: {
      type: 'memory',
      read_records_count: 100,
      max_records_count: 100000
    },
    grpc: {
      enabled: false,
      serverPort: 9595,
      keepAliveTimeMs: 10001
    },
    logs: {
      dateFormat: '%Y-%m-%d %H:%M:%S',
      logFormat: '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
      logLevel: 'INFO',
      local: {}
    }
  }
  
  configText.value = JSON.stringify(defaultConfig, null, 2)
  isValidJson.value = true
  emit('update:modelValue', defaultConfig)
  emit('change', defaultConfig)
  ElMessage.success('配置已重置为默认值')
}

// 暴露验证方法
defineExpose({
  validate: () => {
    return new Promise((resolve, reject) => {
      if (isValidJson.value) {
        resolve(true)
      } else {
        reject(new Error('JSON格式错误'))
      }
    })
  },
  resetFields: () => {
    resetConfig()
  }
})
</script>

<style lang="scss" scoped>
.gateway-advanced-config {
  .config-card {
    border: 1px solid #e4e7ed;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
      font-size: 16px;
      
      .header-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .editor-container {
    margin-bottom: 20px;
    
    .json-editor {
      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
      font-size: 14px;
      line-height: 1.5;
      
      :deep(.el-textarea__inner) {
        font-family: inherit;
        font-size: inherit;
        line-height: inherit;
        border-radius: 6px;
        border: 2px solid #e4e7ed;
        transition: border-color 0.3s ease;
        
        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }
  }
  
  .editor-footer {
    .status-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 6px;
      
      .line-count {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }
    }
    
    .editor-tips {
      :deep(.el-alert) {
        border-radius: 6px;
        
        .el-alert__content {
          p {
            margin: 4px 0;
            line-height: 1.6;
            
            &:first-child {
              margin-top: 0;
            }
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
  
  :deep(.el-card__body) {
    padding: 24px;
  }
}
</style> 