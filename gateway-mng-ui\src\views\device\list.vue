<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="clearfix">
          <span>设备列表</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="refreshList">刷新</el-button>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="deviceList"
        style="width: 100%">
        <el-table-column
          prop="device_name"
          label="设备名称"
          width="360">
        </el-table-column>
        <el-table-column
          prop="connector_type"
          label="连接器类型"
          width="180">
        </el-table-column>
        <el-table-column
          prop="last_active"
          label="最后活跃时间">
        </el-table-column>
        <el-table-column
          label="操作"
          width="150">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="viewDeviceData(scope.row)">查看数据</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'DeviceList',
  data() {
    return {
      loading: false,
      deviceList: []
    }
  },
  created() {
    this.getDeviceList()
  },
  methods: {
    async getDeviceList() {
      this.loading = true
      try {
        const response = await request({
          url: '/gateway/v1/devices/active',
          method: 'get'
        })
        if (response.msg === 'success') {
          this.deviceList = response.data
        } else {
          this.$message.error(response.data)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
        console.error(error)
      }
      this.loading = false
    },
    refreshList() {
      this.getDeviceList()
    },
    viewDeviceData(device) {
      this.$router.push({
        path: '/device/data',
        query: {
          deviceName: device.device_name
        }
      })
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style> 