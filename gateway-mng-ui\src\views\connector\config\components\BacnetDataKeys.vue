<template>
  <div class="bacnet-data-keys">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <span class="title">{{ title }}</span>
            <span class="description">{{ description }}</span>
          </div>
          <el-button type="primary" size="small" @click="handleAddKey">
            <el-icon><Plus /></el-icon>
            添加{{ getKeyTypeName() }}
          </el-button>
        </div>
      </template>
      
      <!-- 数据键列表 -->
      <div v-if="dataKeys.length === 0" class="empty-state">
        <el-empty :description="`暂无${getKeyTypeName()}配置`">
          <el-button type="primary" @click="handleAddKey">添加第一个{{ getKeyTypeName() }}</el-button>
        </el-empty>
      </div>
      
      <div v-else class="keys-list">
        <div 
          v-for="(key, index) in dataKeys" 
          :key="index" 
          class="key-item"
        >
          <el-card shadow="hover">
            <template #header>
              <div class="key-header">
                <div class="key-info">
                  <h4 class="key-name">{{ getKeyDisplayName(key) }}</h4>
                  <span class="key-type">{{ getObjectTypeDisplay(key.objectType) }}</span>
                </div>
                <div class="key-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    link 
                    @click="handleEditKey(key, index)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    link 
                    @click="handleDeleteKey(index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="key-details">
              <div class="detail-item">
                <span class="label">对象类型:</span>
                <span class="value">{{ getObjectTypeDisplay(key.objectType) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">对象ID:</span>
                <span class="value">{{ key.objectId || key.identifier }}</span>
              </div>
              <div class="detail-item">
                <span class="label">属性ID:</span>
                <span class="value">{{ key.propertyId }}</span>
              </div>
              <div v-if="dataType === 'serverSideRpc'" class="detail-item">
                <span class="label">请求类型:</span>
                <span class="value">{{ getRequestTypeDisplay(key.requestType) }}</span>
              </div>
              <div v-if="dataType === 'serverSideRpc'" class="detail-item">
                <span class="label">超时时间:</span>
                <span class="value">{{ key.requestTimeout }}ms</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 数据键配置对话框 -->
    <BacnetDataKeyDialog
      v-model="dialogVisible"
      :data-key="currentKey"
      :data-type="dataType"
      :is-edit="isEdit"
      @save="handleSaveKey"
      @cancel="handleCancelKey"
    />
  </div>
</template>

<script setup>
import { ref, watch, nextTick, defineEmits, defineProps } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BacnetDataKeyDialog from './BacnetDataKeyDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries', 'attributeUpdates', 'serverSideRpc'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const dataKeys = ref([])
const dialogVisible = ref(false)
const currentKey = ref(null)
const currentIndex = ref(-1)
const isEdit = ref(false)

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// BACnet对象类型映射
const objectTypeMap = {
  analogInput: '模拟输入',
  analogOutput: '模拟输出',
  analogValue: '模拟值',
  binaryInput: '二进制输入',
  binaryOutput: '二进制输出',
  binaryValue: '二进制值',
  multiStateInput: '多状态输入',
  multiStateOutput: '多状态输出',
  multiStateValue: '多状态值'
}

// BACnet请求类型映射
const requestTypeMap = {
  writeProperty: '写属性',
  readProperty: '读属性'
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value) {
    dataKeys.value = [...(newValue || [])]
  }
}, { deep: true, immediate: true })

// 监听数据键列表变化
watch(dataKeys, (newKeys) => {
  isInternalUpdate.value = true
  emit('update:modelValue', [...newKeys])
  nextTick(() => {
    isInternalUpdate.value = false
  })
}, { deep: true })

// 获取键类型名称
const getKeyTypeName = () => {
  const typeNames = {
    attributes: '属性',
    timeseries: '遥测',
    attributeUpdates: '属性更新',
    serverSideRpc: 'RPC方法'
  }
  return typeNames[props.dataType] || '数据键'
}

// 获取键显示名称
const getKeyDisplayName = (key) => {
  if (props.dataType === 'serverSideRpc') {
    return key.method || 'Unknown Method'
  }
  return key.key || 'Unknown Key'
}

// 获取对象类型显示名称
const getObjectTypeDisplay = (objectType) => {
  return objectTypeMap[objectType] || objectType || 'Unknown'
}

// 获取请求类型显示名称
const getRequestTypeDisplay = (requestType) => {
  return requestTypeMap[requestType] || requestType || 'Unknown'
}

// 添加数据键
const handleAddKey = () => {
  currentKey.value = createDefaultKey()
  currentIndex.value = -1
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑数据键
const handleEditKey = (key, index) => {
  currentKey.value = { ...key }
  currentIndex.value = index
  isEdit.value = true
  dialogVisible.value = true
}

// 删除数据键
const handleDeleteKey = async (index) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这个${getKeyTypeName()}配置吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    dataKeys.value.splice(index, 1)
    ElMessage.success(`${getKeyTypeName()}删除成功`)
  } catch {
    // 用户取消删除
  }
}

// 保存数据键
const handleSaveKey = (keyData) => {
  if (isEdit.value) {
    // 编辑模式
    dataKeys.value[currentIndex.value] = { ...keyData }
    ElMessage.success(`${getKeyTypeName()}配置更新成功`)
  } else {
    // 添加模式
    dataKeys.value.push({ ...keyData })
    ElMessage.success(`${getKeyTypeName()}添加成功`)
  }
  
  dialogVisible.value = false
  currentKey.value = null
  currentIndex.value = -1
}

// 取消数据键配置
const handleCancelKey = () => {
  dialogVisible.value = false
  currentKey.value = null
  currentIndex.value = -1
}

// 创建默认数据键配置
const createDefaultKey = () => {
  const baseKey = {
    objectType: 'analogOutput',
    objectId: '1',
    propertyId: 'presentValue'
  }
  
  switch (props.dataType) {
    case 'attributes':
    case 'timeseries':
      return {
        key: '',
        ...baseKey
      }
    case 'attributeUpdates':
      return {
        key: '',
        requestType: 'writeProperty',
        ...baseKey
      }
    case 'serverSideRpc':
      return {
        method: '',
        requestType: 'writeProperty',
        requestTimeout: 10000,
        ...baseKey
      }
    default:
      return baseKey
  }
}
</script>

<style lang="scss" scoped>
.bacnet-data-keys {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .header-info {
        flex: 1;
        
        .title {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .description {
          font-size: 12px;
          color: #909399;
          line-height: 1.4;
        }
      }
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
  
  .keys-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 16px;
    
    .key-item {
      .key-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .key-info {
          flex: 1;
          
          .key-name {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            word-break: break-word;
          }
          
          .key-type {
            font-size: 11px;
            color: #909399;
            background: #f5f7fa;
            padding: 2px 6px;
            border-radius: 10px;
          }
        }
        
        .key-actions {
          display: flex;
          gap: 8px;
          margin-left: 12px;
        }
      }
      
      .key-details {
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 0;
          border-bottom: 1px solid #f5f7fa;
          
          &:last-child {
            border-bottom: none;
          }
          
          .label {
            font-size: 12px;
            color: #606266;
            font-weight: 500;
          }
          
          .value {
            font-size: 12px;
            color: #303133;
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            word-break: break-word;
          }
        }
      }
    }
  }
  
  :deep(.el-card) {
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  :deep(.el-card__header) {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f2f5;
  }
  
  :deep(.el-card__body) {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .keys-list {
    grid-template-columns: 1fr;
  }
}
</style> 