# MQTT连接器数据映射优化

## 概述

本次优化将MQTT连接器的数据映射配置从单一页面展示改为列表+对话框的模式，参考了modbus连接器从机设备列表的实现方式，提供了更好的用户体验和更清晰的配置管理。

## 主要变更

### 1. 新增MqttDataMappingDialog组件
- **文件**: `src/views/connector/config/components/MqttDataMappingDialog.vue`
- **功能**: 专门用于编辑单个MQTT数据映射配置的对话框
- **特点**:
  - 分区域展示配置项（基础配置、转换器配置、设备信息配置等）
  - 智能提示和验证
  - 支持添加和编辑两种模式
  - 响应式表单验证

### 2. 重构MqttDataMappingConfig组件
- **文件**: `src/views/connector/config/components/MqttDataMappingConfig.vue`
- **主要变更**:
  - 从详细表单改为简洁的卡片列表展示
  - 每个映射显示关键信息概览
  - 添加编辑、删除按钮
  - 集成对话框编辑功能
  - 应用了防止递归更新的修复

### 3. 优化数据同步机制
- 采用与modbus连接器相同的优化策略
- 使用JSON字符串比较避免不必要的更新
- 防止Vue递归更新警告
- 优化性能，减少不必要的渲染

## 用户体验改进

### 之前的问题
- 所有映射配置在一个页面展示，内容繁杂
- 多个映射时页面过长，难以管理
- 配置项混乱，不便于快速定位和修改

### 优化后的体验
- **清晰的列表视图**: 每个映射以卡片形式展示关键信息
- **快速概览**: 可以快速查看映射名称、主题过滤器、转换器类型等
- **便捷操作**: 每个映射都有独立的编辑和删除按钮
- **专注的编辑**: 对话框中专门编辑单个映射，避免干扰
- **状态标识**: 用标签显示启用状态、数据变化发送等设置

## 关键特性

### 1. 映射列表展示
```vue
<div class="mapping-config-card">
  <div class="card-header">
    <div class="card-title">
      <el-icon><DataBoard /></el-icon>
      映射 1: 温湿度传感器映射
    </div>
    <div class="card-actions">
      <el-button type="primary" size="small" @click="editMapping(index)">
        编辑
      </el-button>
      <el-button type="danger" size="small" @click="deleteMapping(index)">
        删除
      </el-button>
    </div>
  </div>
  <!-- 关键信息展示 -->
</div>
```

### 2. 对话框编辑
- 分区域配置管理
- 实时表单验证
- 智能提示文本
- 优化的用户交互

### 3. 数据同步优化
```javascript
// 优化的数据更新机制
watch(() => props.modelValue, (newValue) => {
  const currentStringified = JSON.stringify(mappings.value)
  const newStringified = JSON.stringify(newValue || [])
  
  if (currentStringified === newStringified) {
    return
  }
  // 只在真正需要时更新
}, { immediate: true })
```

## 技术实现

### 组件结构
```
MqttDataMappingConfig (主组件)
├── 映射列表展示
├── 添加映射按钮
├── 编辑/删除操作
└── MqttDataMappingDialog (对话框)
    ├── 基础配置区域
    ├── 转换器配置区域
    ├── 设备信息配置区域
    ├── MqttAttributeConfig (属性配置)
    └── MqttTimeseriesConfig (时序数据配置)
```

### 数据流
1. 主组件维护映射列表
2. 用户点击编辑/添加打开对话框
3. 对话框提交后更新主组件数据
4. 主组件同步数据到父组件

## 样式优化

### 设计原则
- 参考modbus连接器风格，保持界面一致性
- 卡片式布局，清晰分区
- 悬停效果，提升交互体验
- 响应式设计，适配不同屏幕

### 关键样式
- 卡片悬停效果
- 状态标签颜色区分
- 信息项对齐展示
- 按钮组合布局

## 兼容性

- 向后兼容现有MQTT配置数据结构
- 自动补充缺失的配置项
- 保持与其他组件的接口一致
- 支持现有的数据验证和保存机制

## 使用说明

### 添加映射
1. 点击"添加数据映射"按钮
2. 在对话框中填写配置信息
3. 点击"添加"保存

### 编辑映射
1. 点击映射卡片上的"编辑"按钮
2. 在对话框中修改配置
3. 点击"更新"保存

### 删除映射
1. 点击映射卡片上的"删除"按钮
2. 确认删除操作

## 后续优化建议

1. **批量操作**: 支持批量启用/禁用映射
2. **模板功能**: 提供常用映射配置模板
3. **导入导出**: 支持映射配置的导入导出
4. **测试功能**: 集成映射配置测试工具
5. **拖拽排序**: 支持映射优先级拖拽调整

## 测试要点

- [ ] 映射列表正确显示
- [ ] 添加映射功能正常
- [ ] 编辑映射功能正常
- [ ] 删除映射功能正常
- [ ] 数据同步无误
- [ ] 无递归更新警告
- [ ] 表单验证生效
- [ ] 配置保存正确 