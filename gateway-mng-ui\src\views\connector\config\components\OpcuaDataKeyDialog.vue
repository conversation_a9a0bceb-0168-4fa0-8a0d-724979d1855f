<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="keyForm" :rules="rules" ref="formRef" label-width="140px">
      <!-- 属性和遥测数据键 -->
      <template v-if="dataType === 'attributes' || dataType === 'timeseries'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="键名称" prop="key" required>
              <el-input 
                v-model="keyForm.key" 
                placeholder="temperature"
              />
              <div class="field-hint">数据键的名称标识</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据源类型" prop="type" required>
              <el-select 
                v-model="keyForm.type" 
                placeholder="请选择数据源类型"
              >
                <el-option label="路径" value="path" />
                <el-option label="标识符" value="identifier" />
                <el-option label="常量" value="constant" />
              </el-select>
              <div class="field-hint">OPC-UA节点引用类型</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="值表达式" prop="value" required>
              <el-input 
                v-model="keyForm.value" 
                placeholder="${ns=2;i=5} 或 ${Root\.Objects\.Device1\.Temperature}"
                type="textarea"
                :rows="2"
              />
              <div class="field-hint">
                OPC-UA节点的值表达式，支持路径表达式或节点标识符
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- RPC方法数据键 -->
      <template v-if="dataType === 'rpc_methods'">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="方法名称" prop="method" required>
              <el-input 
                v-model="keyForm.method" 
                placeholder="multiply"
              />
              <div class="field-hint">RPC方法的名称</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="方法参数">
              <div class="arguments-config">
                <div class="arguments-header">
                  <span>参数列表</span>
                  <el-button type="primary" size="small" @click="addArgument">
                    <el-icon><Plus /></el-icon>
                    添加参数
                  </el-button>
                </div>
                <div v-if="keyForm.arguments.length === 0" class="empty-arguments">
                  <el-empty description="暂无参数配置" :image-size="60">
                    <el-button type="primary" size="small" @click="addArgument">添加第一个参数</el-button>
                  </el-empty>
                </div>
                <div v-else class="arguments-list">
                  <div 
                    v-for="(arg, index) in keyForm.arguments" 
                    :key="index" 
                    class="argument-item"
                  >
                    <el-card shadow="never">
                      <div class="argument-content">
                        <div class="argument-fields">
                          <el-form-item label="类型" :prop="`arguments.${index}.type`">
                            <el-select v-model="arg.type" placeholder="请选择参数类型">
                              <el-option label="整数" value="integer" />
                              <el-option label="浮点数" value="double" />
                              <el-option label="字符串" value="string" />
                              <el-option label="布尔值" value="boolean" />
                            </el-select>
                          </el-form-item>
                          <el-form-item label="值" :prop="`arguments.${index}.value`">
                            <el-input 
                              v-if="arg.type === 'string'"
                              v-model="arg.value" 
                              placeholder="请输入字符串值"
                            />
                            <el-input-number 
                              v-else-if="arg.type === 'integer'"
                              v-model.number="arg.value" 
                              :precision="0"
                              controls-position="right"
                              placeholder="请输入整数值"
                            />
                            <el-input-number 
                              v-else-if="arg.type === 'double'"
                              v-model.number="arg.value" 
                              :precision="2"
                              controls-position="right"
                              placeholder="请输入浮点数值"
                            />
                            <el-switch 
                              v-else-if="arg.type === 'boolean'"
                              v-model="arg.value"
                            />
                            <el-input 
                              v-else
                              v-model="arg.value" 
                              placeholder="请输入参数值"
                            />
                          </el-form-item>
                        </div>
                        <div class="argument-actions">
                          <el-button 
                            type="danger" 
                            size="small" 
                            link 
                            @click="removeArgument(index)"
                          >
                            删除
                          </el-button>
                        </div>
                      </div>
                    </el-card>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 属性更新数据键 -->
      <template v-if="dataType === 'attributes_updates'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="键名称" prop="key" required>
              <el-input 
                v-model="keyForm.key" 
                placeholder="deviceName"
              />
              <div class="field-hint">属性更新的键名称标识</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据源类型" prop="type" required>
              <el-select 
                v-model="keyForm.type" 
                placeholder="请选择数据源类型"
              >
                <el-option label="路径" value="path" />
                <el-option label="标识符" value="identifier" />
                <el-option label="常量" value="constant" />
              </el-select>
              <div class="field-hint">OPC-UA节点引用类型</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="值表达式" prop="value" required>
              <el-input 
                v-model="keyForm.value" 
                placeholder="Root\.Objects\.Device1\.serialNumber"
                type="textarea"
                :rows="2"
              />
              <div class="field-hint">
                OPC-UA设备上的属性路径或标识符表达式
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dataKey: {
    type: Object,
    default: null
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries', 'rpc_methods', 'attributes_updates'].includes(value)
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 数据键表单
const keyForm = reactive({
  key: '',
  type: 'path',
  value: '',
  method: '',
  arguments: []
})

// 表单验证规则
const rules = reactive({
  key: [
    { required: true, message: '请输入键名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据源类型', trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入值表达式', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  method: [
    { required: true, message: '请输入方法名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.dataKey) {
    // 加载数据键数据
    Object.assign(keyForm, {
      key: '',
      type: 'path',
      value: '',
      method: '',
      arguments: [],
      ...props.dataKey
    })
    
    // 确保arguments是数组
    if (!Array.isArray(keyForm.arguments)) {
      keyForm.arguments = []
    }
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 获取对话框标题
const getDialogTitle = () => {
  const typeNames = {
    attributes: '属性',
    timeseries: '遥测',
    rpc_methods: 'RPC方法',
    attributes_updates: '属性更新'
  }
  const typeName = typeNames[props.dataType] || '数据键'
  return `${props.isEdit ? '编辑' : '添加'}${typeName}`
}

// 添加参数
const addArgument = () => {
  keyForm.arguments.push({
    type: 'integer',
    value: 0
  })
}

// 删除参数
const removeArgument = (index) => {
  keyForm.arguments.splice(index, 1)
}

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...keyForm }
    
    // 根据数据类型清理不需要的字段
    if (props.dataType === 'attributes' || props.dataType === 'timeseries') {
      delete saveData.method
      delete saveData.arguments
    } else if (props.dataType === 'rpc_methods') {
      delete saveData.key
      delete saveData.type
      delete saveData.value
    } else if (props.dataType === 'attributes_updates') {
      delete saveData.method
      delete saveData.arguments
    }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '数据键更新成功' : '数据键添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.arguments-config {
  .arguments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
    
    span {
      font-weight: 600;
      color: #303133;
    }
  }
  
  .empty-arguments {
    text-align: center;
    padding: 20px 0;
  }
  
  .arguments-list {
    .argument-item {
      margin-bottom: 12px;
      
      .argument-content {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        
        .argument-fields {
          flex: 1;
          display: grid;
          grid-template-columns: 1fr 2fr;
          gap: 16px;
          align-items: start;
        }
        
        .argument-actions {
          display: flex;
          align-items: center;
          padding-top: 8px;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  
  .el-input-number {
    width: 100%;
  }
}

:deep(.el-card__body) {
  padding: 12px;
}
</style> 