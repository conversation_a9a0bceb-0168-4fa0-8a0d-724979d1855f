<template>
  <el-card class="config-card">
    <template #header>
      <div class="card-header">
        <span>MQTT代理服务器配置</span>
      </div>
    </template>
    
    <el-form :model="brokerConfig" :rules="brokerRules" label-width="140px">
      <!-- 基础连接配置 -->
      <div class="config-section">
        <h4 class="section-title">连接配置</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="服务器地址" prop="host" required>
              <el-input v-model="brokerConfig.host" placeholder="127.0.0.1" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="端口" prop="port" required>
              <el-input-number 
                v-model="brokerConfig.port" 
                :min="1" 
                :max="65535" 
                controls-position="right" 
                style="width: 100%" 
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户端ID" prop="clientId">
              <el-input v-model="brokerConfig.clientId" placeholder="ThingsBoard_gateway">
                <template #append>
                  <el-button @click="generateClientId" :icon="Refresh">生成</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="MQTT版本">
              <el-select v-model="brokerConfig.version" style="width: 100%">
                <el-option label="MQTT 3.1" :value="3" />
                <el-option label="MQTT 3.1.1" :value="4" />
                <el-option label="MQTT 5.0" :value="5" />
              </el-select>
              <div class="field-hint">MQTT协议版本</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="只在数据变化时发送">
              <el-switch v-model="brokerConfig.sendDataOnlyOnChange" />
              <div class="field-hint">仅当数据发生变化时才发送到平台</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 工作线程配置 -->
      <div class="config-section">
        <h4 class="section-title">性能配置</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="最大工作线程数">
              <el-input-number 
                v-model="brokerConfig.maxNumberOfWorkers" 
                :min="1" 
                :max="1000" 
                controls-position="right" 
                style="width: 100%" 
              />
              <div class="field-hint">处理MQTT消息的最大工作线程数</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每线程最大消息数">
              <el-input-number 
                v-model="brokerConfig.maxMessageNumberPerWorker" 
                :min="1" 
                :max="1000" 
                controls-position="right" 
                style="width: 100%" 
              />
              <div class="field-hint">每个工作线程处理的最大消息数</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 安全配置 -->
      <div class="config-section">
        <h4 class="section-title">安全配置</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认证方式" prop="security.type">
              <el-select v-model="brokerConfig.security.type" style="width: 100%">
                <el-option label="匿名访问" value="anonymous" />
                <el-option label="用户名密码" value="basic" />
                <el-option label="证书认证" value="certificates" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="brokerConfig.security.type === 'basic'">
            <el-form-item label="用户名" prop="security.username">
              <el-input v-model="brokerConfig.security.username" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="brokerConfig.security.type === 'basic'">
            <el-form-item label="密码" prop="security.password">
              <el-input 
                v-model="brokerConfig.security.password" 
                type="password" 
                show-password 
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 证书配置 -->
        <el-row :gutter="20" v-if="brokerConfig.security.type === 'certificates'">
          <el-col :span="8">
            <el-form-item label="CA证书路径" prop="security.caCert">
              <el-input v-model="brokerConfig.security.caCert" placeholder="/path/to/ca.crt" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户端证书路径" prop="security.cert">
              <el-input v-model="brokerConfig.security.cert" placeholder="/path/to/client.crt" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="私钥路径" prop="security.privateKey">
              <el-input v-model="brokerConfig.security.privateKey" placeholder="/path/to/client.key" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { reactive, watch, defineEmits, defineProps, nextTick, ref } from 'vue'
import { Refresh } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 标准的isInternalUpdate模式
const isInternalUpdate = ref(false)

// 代理配置对象
const brokerConfig = reactive({
  host: '127.0.0.1',
  port: 1883,
  clientId: 'ThingsBoard_gateway',
  version: 5,
  maxMessageNumberPerWorker: 10,
  maxNumberOfWorkers: 100,
  sendDataOnlyOnChange: false,
  security: {
    type: 'anonymous'
  }
})

// 表单验证规则
const brokerRules = reactive({
  host: [
    { required: true, message: '请输入MQTT服务器地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
  ]
})

// 生成随机客户端ID
const generateClientId = () => {
  const randomStr = Math.random().toString(36).substring(2, 8)
  brokerConfig.clientId = `tb_gw_${randomStr}`
}

// 监听props变化 - 标准模式
watch(() => props.modelValue, (newVal) => {
  if (isInternalUpdate.value) return
  
  if (newVal) {
    Object.assign(brokerConfig, {
      host: '127.0.0.1',
      port: 1883,
      clientId: 'ThingsBoard_gateway',
      version: 5,
      maxMessageNumberPerWorker: 10,
      maxNumberOfWorkers: 100,
      sendDataOnlyOnChange: false,
      security: {
        type: 'anonymous'
      },
      ...newVal
    })
  }
}, { immediate: true, deep: true })

// 监听配置变化 - 标准模式
watch(brokerConfig, (newVal) => {
  isInternalUpdate.value = true
  nextTick(() => {
    isInternalUpdate.value = false
  })
  
  emit('update:modelValue', { ...newVal })
}, { deep: true })
</script>

<style lang="scss" scoped>
.config-card {
  margin-bottom: 20px;
  
  .card-header {
    font-weight: 600;
    color: #303133;
  }
}

.config-section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #409eff;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #409eff;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}
</style> 