[Unit]
Description=IOT Gateway Managerment API
After=network.target
StartLimitIntervalSec=0

[Service]
WorkingDirectory=/home/<USER>/thingsboard_gateway/
Environment="PATH=/home/<USER>/venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
Environment="PYTHONPATH=/home/<USER>/"
Environment="PYTHONUNBUFFERED=1"
ExecStart=/home/<USER>/venv/bin/python3 /home/<USER>/thingsboard_gateway/front_interface.py
Restart=always
RestartSec=0
Type=simple

[Install]
WantedBy=multi-user.target