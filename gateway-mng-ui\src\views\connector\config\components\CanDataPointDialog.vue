<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${isEdit ? '编辑' : '添加'}${getDataTypeLabel()}`"
    width="70%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="dataPointForm" :rules="rules" ref="formRef" label-width="140px">
      <!-- 属性和遥测数据配置 -->
      <div v-if="dataType === 'attributes' || dataType === 'timeseries'">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数据键名" prop="key" required>
              <el-input v-model="dataPointForm.key" placeholder="rpm" />
              <div class="field-hint">数据的键名标识</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="节点ID" prop="nodeId" required>
              <el-input-number 
                v-model="dataPointForm.nodeId" 
                :min="0" 
                :max="0x1FFFFFFF"
                controls-position="right"
                style="width: 100%"
              />
              <div class="field-hint">CAN节点标识符</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="扩展ID">
              <el-switch v-model="dataPointForm.isExtendedId" />
              <div class="field-hint">使用29位扩展ID</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="命令格式" prop="command">
              <el-input v-model="dataPointForm.command" placeholder="2:2:big:8717" />
              <div class="field-hint">格式: 起始位:长度:字节序:数据类型</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="值格式" prop="value">
              <el-input v-model="dataPointForm.value" placeholder="4:2:big:int" />
              <div class="field-hint">格式: 起始位:长度:字节序:数据类型</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="表达式" prop="expression">
              <el-input v-model="dataPointForm.expression" placeholder="value / 4" />
              <div class="field-hint">数据处理表达式，如 value / 4 或 bool(value & 0b00000100)</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 轮询配置 -->
        <el-card class="polling-config">
          <template #header>
            <span>轮询配置</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="轮询类型">
                <el-select v-model="dataPointForm.polling.type" style="width: 100%">
                  <el-option label="仅一次" value="once" />
                  <el-option label="始终轮询" value="always" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="dataPointForm.polling.type === 'always'">
              <el-form-item label="轮询周期">
                <el-input-number 
                  v-model="dataPointForm.polling.period" 
                  :min="1" 
                  controls-position="right"
                  style="width: 100%"
                />
                <span class="unit-suffix">秒</span>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="数据(Hex)">
                <el-input v-model="dataPointForm.polling.dataInHex" placeholder="AA BB CC DD EE FF" />
                <div class="field-hint">发送的十六进制数据，用空格分隔</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </div>
      
      <!-- 属性更新配置 -->
      <div v-else-if="dataType === 'attributeUpdates'">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="IoTCloud属性" prop="attributeOnThingsBoard" required>
              <el-input v-model="dataPointForm.attributeOnThingsBoard" placeholder="softwareVersion" />
              <div class="field-hint">IoTCloud平台上的属性名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="节点ID" prop="nodeId" required>
              <el-input-number 
                v-model="dataPointForm.nodeId" 
                :min="0" 
                :max="0x1FFFFFFF"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="扩展ID">
              <el-switch v-model="dataPointForm.isExtendedId" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数据长度">
              <el-input-number 
                v-model="dataPointForm.dataLength" 
                :min="1" 
                :max="64"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="字节序">
              <el-select v-model="dataPointForm.dataByteorder" style="width: 100%">
                <el-option label="大端" value="big" />
                <el-option label="小端" value="little" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据表达式">
              <el-input v-model="dataPointForm.dataExpression" placeholder="value + 5" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      
      <!-- RPC方法配置 -->
      <div v-else-if="dataType === 'serverSideRpc'">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="方法名" prop="method" required>
              <el-input v-model="dataPointForm.method" placeholder="setLightLevel" />
              <div class="field-hint">RPC方法的名称标识</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="节点ID" prop="nodeId" required>
              <el-input-number 
                v-model="dataPointForm.nodeId" 
                :min="0" 
                :max="0x1FFFFFFF"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="扩展ID">
              <el-switch v-model="dataPointForm.isExtendedId" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="CAN FD">
              <el-switch v-model="dataPointForm.isFd" />
              <div class="field-hint">使用CAN FD协议</div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="比特率切换">
              <el-switch v-model="dataPointForm.bitrateSwitch" />
              <div class="field-hint">启用比特率切换</div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数据长度">
              <el-input-number 
                v-model="dataPointForm.dataLength" 
                :min="1" 
                :max="64"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="字节序">
              <el-select v-model="dataPointForm.dataByteorder" style="width: 100%">
                <el-option label="大端" value="big" />
                <el-option label="小端" value="little" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数据(Hex)">
              <el-input v-model="dataPointForm.dataInHex" placeholder="AA BB CC DD" />
              <div class="field-hint">发送的十六进制数据</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="前置数据">
              <el-input v-model="dataPointForm.dataBefore" placeholder="00AA" />
              <div class="field-hint">数据前缀</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="后置数据">
              <el-input v-model="dataPointForm.dataAfter" placeholder="0102" />
              <div class="field-hint">数据后缀</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="数据表达式">
              <el-input v-model="dataPointForm.dataExpression" placeholder="userSpeed if maxAllowedSpeed > userSpeed else maxAllowedSpeed" />
              <div class="field-hint">数据处理表达式，用于动态计算发送数据</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dataPoint: {
    type: Object,
    default: null
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries', 'attributeUpdates', 'serverSideRpc'].includes(value)
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 数据点表单数据
const dataPointForm = reactive({})

// 获取数据类型标签
const getDataTypeLabel = () => {
  const labels = {
    attributes: '属性',
    timeseries: '遥测',
    attributeUpdates: '属性更新',
    serverSideRpc: 'RPC方法'
  }
  return labels[props.dataType] || '数据点'
}

// 创建默认数据点
const createDefaultDataPoint = () => {
  if (props.dataType === 'attributes' || props.dataType === 'timeseries') {
    return {
      key: '',
      nodeId: 0,
      isExtendedId: false,
      command: '',
      value: '',
      expression: '',
      polling: {
        type: 'always',
        period: 5,
        dataInHex: ''
      }
    }
  } else if (props.dataType === 'attributeUpdates') {
    return {
      attributeOnThingsBoard: '',
      nodeId: 0,
      isExtendedId: false,
      dataLength: 4,
      dataExpression: '',
      dataByteorder: 'little'
    }
  } else if (props.dataType === 'serverSideRpc') {
    return {
      method: '',
      nodeId: 0,
      isExtendedId: false,
      isFd: false,
      bitrateSwitch: false,
      dataLength: 8,
      dataByteorder: 'big',
      dataInHex: '',
      dataBefore: '',
      dataAfter: '',
      dataExpression: ''
    }
  }
  return {}
}

// 表单验证规则
const rules = reactive({
  key: [{ required: true, message: '请输入数据键名', trigger: 'blur' }],
  nodeId: [{ required: true, message: '请输入节点ID', trigger: 'blur' }],
  method: [{ required: true, message: '请输入方法名', trigger: 'blur' }],
  attributeOnThingsBoard: [{ required: true, message: '请输入IoTCloud属性名', trigger: 'blur' }]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue) {
    if (props.dataPoint) {
      Object.assign(dataPointForm, props.dataPoint)
    } else {
      Object.assign(dataPointForm, createDefaultDataPoint())
    }
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    
    saving.value = true
    
    emit('save', { ...dataPointForm })
    
    ElMessage.success(props.isEdit ? `${getDataTypeLabel()}更新成功` : `${getDataTypeLabel()}添加成功`)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.unit-suffix {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.polling-config {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-card__body) {
  padding: 16px;
}
</style> 