{"broker": {"host": "127.0.0.1", "port": 1883, "clientId": "IoTCloud_gateway", "version": 5, "maxMessageNumberPerWorker": 10, "maxNumberOfWorkers": 100, "sendDataOnlyOnChange": false, "cleanSession": true, "cleanStart": true, "sessionExpiryInterval": 0, "security": {"type": "anonymous"}}, "mapping": [{"topicFilter": "sensor/data", "subscriptionQos": 1, "converter": {"type": "json", "deviceInfo": {"deviceNameExpressionSource": "message", "deviceNameExpression": "${serialNumber}", "deviceProfileExpressionSource": "message", "deviceProfileExpression": "${sensorType}"}, "sendDataOnlyOnChange": false, "timeout": 60000, "attributes": [{"type": "string", "key": "model", "value": "${sensorModel}"}, {"type": "string", "key": "${sensorModel}", "value": "on"}], "timeseries": [{"type": "double", "key": "temperature", "value": "${temp}"}, {"type": "double", "key": "humidity", "value": "${hum}"}, {"type": "string", "key": "combine", "value": "${hum}:${temp}"}]}}, {"topicFilter": "sensor/+/data", "subscriptionQos": 1, "converter": {"type": "json", "deviceInfo": {"deviceNameExpressionSource": "topic", "deviceNameExpression": "(?<=sensor/)(.*?)(?=/data)", "deviceProfileExpressionSource": "constant", "deviceProfileExpression": "Thermometer"}, "sendDataOnlyOnChange": false, "timeout": 60000, "attributes": [{"type": "string", "key": "model", "value": "${sensorModel}"}], "timeseries": [{"type": "double", "key": "temperature", "value": "${temp}"}, {"type": "string", "key": "humidity", "value": "${hum}"}]}}, {"topicFilter": "sensor/raw_data", "subscriptionQos": 1, "converter": {"type": "bytes", "deviceInfo": {"deviceNameExpressionSource": "message", "deviceNameExpression": "[0:4]", "deviceProfileExpressionSource": "constant", "deviceProfileExpression": "default"}, "sendDataOnlyOnChange": false, "timeout": 60000, "attributes": [{"type": "raw", "key": "rawData", "value": "[:]"}], "timeseries": [{"type": "raw", "key": "temp", "value": "[4:]"}]}}, {"topicFilter": "custom/sensors/+", "subscriptionQos": 1, "converter": {"type": "custom", "extension": "CustomMqttUplinkConverter", "cached": true, "extensionConfig": {"temperature": 2, "humidity": 2, "batteryLevel": 1}}}], "requestsMapping": {"connectRequests": [{"topicFilter": "sensor/connect", "deviceInfo": {"deviceNameExpressionSource": "message", "deviceNameExpression": "${serialNumber}", "deviceProfileExpressionSource": "constant", "deviceProfileExpression": "Thermometer"}}, {"topicFilter": "sensor/+/connect", "deviceInfo": {"deviceNameExpressionSource": "topic", "deviceNameExpression": "(?<=sensor/)(.*?)(?=/connect)", "deviceProfileExpressionSource": "constant", "deviceProfileExpression": "Thermometer"}}], "disconnectRequests": [{"topicFilter": "sensor/disconnect", "deviceInfo": {"deviceNameExpressionSource": "message", "deviceNameExpression": "${serialNumber}"}}, {"topicFilter": "sensor/+/disconnect", "deviceInfo": {"deviceNameExpressionSource": "topic", "deviceNameExpression": "(?<=sensor/)(.*?)(?=/connect)"}}], "attributeRequests": [{"retain": false, "topicFilter": "v1/devices/me/attributes/request", "deviceInfo": {"deviceNameExpressionSource": "message", "deviceNameExpression": "${serialNumber}"}, "attributeNameExpressionSource": "message", "attributeNameExpression": "${versionAttribute}, ${pduAttribute}", "topicExpression": "devices/${deviceName}/attrs", "valueExpression": "${attributeKey}: ${attributeValue}"}], "attributeUpdates": [{"retain": true, "deviceNameFilter": ".*", "attributeFilter": "firmwareVersion", "topicExpression": "sensor/${deviceName}/${attributeKey}", "valueExpression": "{\"${attributeKey}\":\"${attributeValue}\"}"}], "serverSideRpc": [{"type": "twoWay", "deviceNameFilter": ".*", "methodFilter": "echo", "requestTopicExpression": "sensor/${deviceName}/request/${methodName}/${requestId}", "responseTopicExpression": "sensor/${deviceName}/response/${methodName}/${requestId}", "responseTopicQoS": 1, "responseTimeout": 10000, "valueExpression": "${params}"}, {"type": "oneWay", "deviceNameFilter": ".*", "methodFilter": "no-reply", "requestTopicExpression": "sensor/${deviceName}/request/${methodName}/${requestId}", "valueExpression": "${params}"}]}}