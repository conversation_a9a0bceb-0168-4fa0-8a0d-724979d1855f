<template>
  <div class="gpio-device-config">
    <el-form :model="deviceConfig" :rules="rules" ref="formRef" label-width="140px">
      <el-tabs v-model="activeTab" class="device-tabs">
        <!-- 基础配置标签页 -->
        <el-tab-pane label="基础配置" name="basic">
          <div class="config-section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备名称" prop="name" required>
                  <el-input v-model="deviceConfig.name" placeholder="请输入设备名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备类型" prop="deviceType">
                  <el-input v-model="deviceConfig.deviceType" placeholder="default" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上行转换器" prop="converter">
                  <el-select v-model="deviceConfig.converter" placeholder="选择转换器">
                    <el-option label="GpioUplinkConverter" value="GpioUplinkConverter" />
                    <el-option label="CustomGpioUplinkConverter" value="CustomGpioUplinkConverter" />
                    <el-option label="CustomGpioUplinkCoverter" value="CustomGpioUplinkCoverter" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="下行转换器" prop="downlink_converter">
                  <el-select v-model="deviceConfig.downlink_converter" placeholder="选择下行转换器" clearable>
                    <el-option label="GpioDownlinkConverter" value="GpioDownlinkConverter" />
                    <el-option label="CustomGpioDownlinkConverter" value="CustomGpioDownlinkConverter" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- GPIO配置标签页 -->
        <el-tab-pane label="GPIO配置" name="gpio">
          <div class="config-section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="GPIO引脚" prop="gpioPins" required>
                  <el-select 
                    v-model="deviceConfig.gpioPins" 
                    multiple 
                    placeholder="选择GPIO引脚"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="pin in availablePins" 
                      :key="pin" 
                      :label="`GPIO ${pin}`" 
                      :value="pin" 
                    />
                  </el-select>
                  <div class="field-hint">选择要使用的GPIO引脚号</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="引脚模式" prop="pinMode">
                  <el-select v-model="deviceConfig.pinMode" placeholder="选择引脚模式">
                    <el-option label="输出模式 (out)" value="out" />
                    <el-option label="输入模式 (in)" value="in" />
                  </el-select>
                  <div class="field-hint">设置GPIO引脚的工作模式</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上拉下拉电阻" prop="pullUpDown">
                  <el-select v-model="deviceConfig.pullUpDown" placeholder="选择上拉下拉配置">
                    <el-option label="关闭 (off)" value="off" />
                    <el-option label="上拉 (up)" value="up" />
                    <el-option label="下拉 (down)" value="down" />
                  </el-select>
                  <div class="field-hint">仅在输入模式下有效</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="轮询周期(ms)" prop="pollingPeriod">
                  <el-input-number 
                    v-model="deviceConfig.pollingPeriod" 
                    :min="100" 
                    :max="60000"
                    :step="100"
                    controls-position="right"
                    style="width: 100%"
                  />
                  <div class="field-hint">GPIO状态读取间隔时间</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- 遥测配置标签页 -->
        <el-tab-pane label="遥测配置" name="telemetry">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">遥测数据配置</div>
              <el-button type="primary" size="small" @click="addTelemetry">
                <el-icon><Plus /></el-icon>添加遥测
              </el-button>
            </div>
            
            <div v-if="deviceConfig.telemetry.length === 0" class="empty-state">
              <el-empty description="暂无遥测配置">
                <el-button type="primary" @click="addTelemetry">添加第一个遥测配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-point-list">
              <el-card 
                v-for="(item, index) in deviceConfig.telemetry" 
                :key="index" 
                class="data-point-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span>遥测 {{ index + 1 }}</span>
                    <el-button 
                      type="danger" 
                      size="small" 
                      link 
                      @click="deleteTelemetry(index)"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="键名" required>
                      <el-input v-model="item.key" placeholder="telemetry_key" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="数据类型">
                      <el-select v-model="item.type" placeholder="选择数据类型">
                        <el-option label="整数 (int)" value="int" />
                        <el-option label="浮点数 (float)" value="float" />
                        <el-option label="布尔值 (bool)" value="bool" />
                        <el-option label="字符串 (string)" value="string" />
                        <el-option label="十六进制 (hex)" value="hex" />
                        <el-option label="二进制 (binary)" value="binary" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="GPIO引脚">
                      <el-select v-model="item.pin" placeholder="选择GPIO引脚" clearable>
                        <el-option 
                          v-for="pin in deviceConfig.gpioPins" 
                          :key="pin" 
                          :label="`GPIO ${pin}`" 
                          :value="pin" 
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 属性配置标签页 -->
        <el-tab-pane label="属性配置" name="attributes">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">属性数据配置</div>
              <el-button type="primary" size="small" @click="addAttribute">
                <el-icon><Plus /></el-icon>添加属性
              </el-button>
            </div>
            
            <div v-if="deviceConfig.attributes.length === 0" class="empty-state">
              <el-empty description="暂无属性配置">
                <el-button type="primary" @click="addAttribute">添加第一个属性配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-point-list">
              <el-card 
                v-for="(item, index) in deviceConfig.attributes" 
                :key="index" 
                class="data-point-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span>属性 {{ index + 1 }}</span>
                    <el-button 
                      type="danger" 
                      size="small" 
                      link 
                      @click="deleteAttribute(index)"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="键名" required>
                      <el-input v-model="item.key" placeholder="attribute_key" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="数据类型">
                      <el-select v-model="item.type" placeholder="选择数据类型">
                        <el-option label="整数 (int)" value="int" />
                        <el-option label="浮点数 (float)" value="float" />
                        <el-option label="布尔值 (bool)" value="bool" />
                        <el-option label="字符串 (string)" value="string" />
                        <el-option label="十六进制 (hex)" value="hex" />
                        <el-option label="二进制 (binary)" value="binary" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="GPIO引脚">
                      <el-select v-model="item.pin" placeholder="选择GPIO引脚" clearable>
                        <el-option 
                          v-for="pin in deviceConfig.gpioPins" 
                          :key="pin" 
                          :label="`GPIO ${pin}`" 
                          :value="pin" 
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 属性更新配置标签页 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">属性更新配置</div>
              <el-button type="primary" size="small" @click="addAttributeUpdate">
                <el-icon><Plus /></el-icon>添加属性更新
              </el-button>
            </div>
            
            <div v-if="deviceConfig.attributeUpdates.length === 0" class="empty-state">
              <el-empty description="暂无属性更新配置">
                <el-button type="primary" @click="addAttributeUpdate">添加第一个属性更新配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-point-list">
              <el-card 
                v-for="(item, index) in deviceConfig.attributeUpdates" 
                :key="index" 
                class="data-point-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span>属性更新 {{ index + 1 }}</span>
                    <el-button 
                      type="danger" 
                      size="small" 
                      link 
                      @click="deleteAttributeUpdate(index)"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="平台属性名" required>
                      <el-input v-model="item.attributeOnPlatform" placeholder="平台上的属性名" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="GPIO引脚" required>
                      <el-select v-model="item.pin" placeholder="选择要控制的GPIO引脚">
                        <el-option 
                          v-for="pin in deviceConfig.gpioPins" 
                          :key="pin" 
                          :label="`GPIO ${pin}`" 
                          :value="pin" 
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="值映射">
                      <el-input v-model="item.valueMapping" placeholder="如: 0->LOW, 1->HIGH" />
                      <div class="field-hint">可选，用于值转换</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- RPC配置标签页 -->
        <el-tab-pane label="RPC配置" name="rpc">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">服务端RPC配置</div>
              <el-button type="primary" size="small" @click="addServerSideRpc">
                <el-icon><Plus /></el-icon>添加RPC方法
              </el-button>
            </div>
            
            <div v-if="deviceConfig.serverSideRpc.length === 0" class="empty-state">
              <el-empty description="暂无RPC配置">
                <el-button type="primary" @click="addServerSideRpc">添加第一个RPC方法</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-point-list">
              <el-card 
                v-for="(item, index) in deviceConfig.serverSideRpc" 
                :key="index" 
                class="data-point-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span>RPC方法 {{ index + 1 }}</span>
                    <el-button 
                      type="danger" 
                      size="small" 
                      link 
                      @click="deleteServerSideRpc(index)"
                    >
                      删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="方法名" required>
                      <el-input v-model="item.method" placeholder="如: setGpioPin" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="GPIO引脚">
                      <el-select v-model="item.pin" placeholder="选择要控制的GPIO引脚" clearable>
                        <el-option 
                          v-for="pin in deviceConfig.gpioPins" 
                          :key="pin" 
                          :label="`GPIO ${pin}`" 
                          :value="pin" 
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="参数类型">
                      <el-select v-model="item.type" placeholder="选择参数类型">
                        <el-option label="整数 (int)" value="int" />
                        <el-option label="布尔值 (bool)" value="bool" />
                        <el-option label="字符串 (string)" value="string" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否有响应">
                      <el-switch v-model="item.withResponse" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="item.withResponse">
                    <el-form-item label="响应超时(秒)">
                      <el-input-number 
                        v-model="item.responseTimeoutSec" 
                        :min="1" 
                        :max="60"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// Props and emits
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// Reactive data
const activeTab = ref('basic')
const formRef = ref()

// 可用的GPIO引脚
const availablePins = ref([
  2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29
])

// 设备配置
const deviceConfig = reactive({
  name: '',
  deviceName: '',
  deviceType: 'default',
  type: 'gpio',
  converter: 'GpioUplinkConverter',
  downlink_converter: 'GpioDownlinkConverter',
  gpioPins: [],
  pinMode: 'out',
  pullUpDown: 'off',
  pollingPeriod: 1000,
  telemetry: [],
  attributes: [],
  attributeUpdates: [],
  serverSideRpc: []
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  gpioPins: [
    { required: true, message: '请选择GPIO引脚', trigger: 'change' }
  ],
  converter: [
    { required: true, message: '请选择上行转换器', trigger: 'change' }
  ]
})

// 初始化设备配置
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    Object.assign(deviceConfig, {
      name: '',
      deviceName: '',
      deviceType: 'default',
      type: 'gpio',
      converter: 'GpioUplinkConverter',
      downlink_converter: 'GpioDownlinkConverter',
      gpioPins: [],
      pinMode: 'out',
      pullUpDown: 'off',
      pollingPeriod: 1000,
      telemetry: [],
      attributes: [],
      attributeUpdates: [],
      serverSideRpc: [],
      ...newVal
    })
    
    // 确保deviceName与name同步
    if (deviceConfig.name && !deviceConfig.deviceName) {
      deviceConfig.deviceName = deviceConfig.name
    }
  }
}, { immediate: true, deep: true })

// 监听设备配置变化
watch(deviceConfig, (newVal) => {
  // 同步deviceName
  if (newVal.name) {
    newVal.deviceName = newVal.name
  }
  emit('update:modelValue', { ...newVal })
}, { deep: true })

// 遥测相关方法
const addTelemetry = () => {
  deviceConfig.telemetry.push({
    key: '',
    type: 'int',
    pin: null
  })
}

const deleteTelemetry = (index) => {
  deviceConfig.telemetry.splice(index, 1)
  ElMessage.success('遥测配置删除成功')
}

// 属性相关方法
const addAttribute = () => {
  deviceConfig.attributes.push({
    key: '',
    type: 'int',
    pin: null
  })
}

const deleteAttribute = (index) => {
  deviceConfig.attributes.splice(index, 1)
  ElMessage.success('属性配置删除成功')
}

// 属性更新相关方法
const addAttributeUpdate = () => {
  deviceConfig.attributeUpdates.push({
    attributeOnPlatform: '',
    pin: null,
    valueMapping: ''
  })
}

const deleteAttributeUpdate = (index) => {
  deviceConfig.attributeUpdates.splice(index, 1)
  ElMessage.success('属性更新配置删除成功')
}

// RPC相关方法
const addServerSideRpc = () => {
  deviceConfig.serverSideRpc.push({
    method: '',
    type: 'int',
    pin: null,
    withResponse: false,
    responseTimeoutSec: 5
  })
}

const deleteServerSideRpc = (index) => {
  deviceConfig.serverSideRpc.splice(index, 1)
  ElMessage.success('RPC配置删除成功')
}

// 验证方法
const validate = () => {
  return formRef.value?.validate()
}

// 暴露验证方法
defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.gpio-device-config {
  .device-tabs {
    :deep(.el-tabs__content) {
      padding: 20px 0;
    }
  }

  .config-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }

  .data-point-list {
    .data-point-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        color: #303133;
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}

:deep(.el-input-number) {
  width: 100%;
}
</style> 