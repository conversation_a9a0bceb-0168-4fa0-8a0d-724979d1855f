<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基础请求配置 -->
      <div class="section">
        <div class="section-title">请求配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="URL路径" prop="url" required>
              <el-input 
                v-model="form.url" 
                placeholder="getdata"
              />
              <div class="field-hint">相对于服务器地址的URL路径</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="HTTP方法" prop="httpMethod" required>
              <el-select v-model="form.httpMethod" style="width: 100%">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
                <el-option label="HEAD" value="HEAD" />
                <el-option label="OPTIONS" value="OPTIONS" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="超时(秒)" prop="timeout">
              <el-input-number 
                v-model="form.timeout" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="扫描周期(秒)" prop="scanPeriod">
              <el-input-number 
                v-model="form.scanPeriod" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="允许重定向">
              <el-switch v-model="form.allowRedirects" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        <RequestHttpHeaders v-model="form.httpHeaders" />
      </div>

      <!-- 请求内容配置 -->
      <div class="section" v-if="form.httpMethod !== 'GET'">
        <div class="section-title">请求内容</div>
        <RequestContentConfig v-model="form.content" />
      </div>

      <!-- 数据转换器配置 -->
      <div class="section">
        <div class="section-title">数据转换器</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="转换器类型" prop="converter.type" required>
              <el-select v-model="form.converter.type" style="width: 100%">
                <el-option label="JSON转换器" value="json" />
                <el-option label="自定义转换器" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名表达式" prop="converter.deviceNameJsonExpression" required>
              <el-input 
                v-model="form.converter.deviceNameJsonExpression" 
                placeholder="SD8500"
              />
              <div class="field-hint">设备名称的JSON表达式或常量</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型表达式" prop="converter.deviceTypeJsonExpression">
              <el-input 
                v-model="form.converter.deviceTypeJsonExpression" 
                placeholder="SD"
              />
              <div class="field-hint">设备类型的JSON表达式或常量</div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- JSON转换器配置 -->
        <template v-if="form.converter.type === 'json'">
          <!-- 属性配置 -->
          <div class="converter-section">
            <div class="converter-title">
              <span>属性配置</span>
              <el-button type="primary" size="small" @click="addAttribute">
                <el-icon><Plus /></el-icon>
                添加属性
              </el-button>
            </div>
            
            <div v-if="!form.converter.attributes?.length" class="empty-data">
              <el-empty description="暂无属性配置" :image-size="60">
                <el-button type="primary" size="small" @click="addAttribute">添加第一个属性</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-keys-list">
              <RequestDataKey
                v-for="(attr, attrIndex) in form.converter.attributes"
                :key="`attr-${attrIndex}`"
                v-model="form.converter.attributes[attrIndex]"
                data-type="attribute"
                @delete="removeAttribute(attrIndex)"
              />
            </div>
          </div>

          <!-- 遥测配置 -->
          <div class="converter-section">
            <div class="converter-title">
              <span>遥测配置</span>
              <el-button type="primary" size="small" @click="addTelemetry">
                <el-icon><Plus /></el-icon>
                添加遥测
              </el-button>
            </div>
            
            <div v-if="!form.converter.telemetry?.length" class="empty-data">
              <el-empty description="暂无遥测配置" :image-size="60">
                <el-button type="primary" size="small" @click="addTelemetry">添加第一个遥测</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-keys-list">
              <RequestDataKey
                v-for="(tel, telIndex) in form.converter.telemetry"
                :key="`tel-${telIndex}`"
                v-model="form.converter.telemetry[telIndex]"
                data-type="telemetry"
                @delete="removeTelemetry(telIndex)"
              />
            </div>
          </div>
        </template>

        <!-- 自定义转换器配置 -->
        <template v-if="form.converter.type === 'custom'">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="扩展类名" prop="converter.extension" required>
                <el-input 
                  v-model="form.converter.extension" 
                  placeholder="CustomRequestUplinkConverter"
                />
                <div class="field-hint">自定义转换器的类名</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import RequestHttpHeaders from './RequestHttpHeaders.vue'
import RequestContentConfig from './RequestContentConfig.vue'
import RequestDataKey from './RequestDataKey.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mapping: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const formRef = ref()
const saving = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑映射配置' : '添加映射配置'
})

// 默认表单结构
const defaultForm = {
  url: '',
  httpMethod: 'GET',
  httpHeaders: {
    'ACCEPT': 'application/json'
  },
  content: {},
  allowRedirects: true,
  timeout: 0.5,
  scanPeriod: 5,
  converter: {
    type: 'json',
    deviceNameJsonExpression: '',
    deviceTypeJsonExpression: '',
    attributes: [],
    telemetry: []
  }
}

const form = reactive({ ...defaultForm })

// 表单验证规则
const rules = reactive({
  url: [
    { required: true, message: '请输入URL路径', trigger: 'blur' }
  ],
  httpMethod: [
    { required: true, message: '请选择HTTP方法', trigger: 'change' }
  ],
  'converter.type': [
    { required: true, message: '请选择转换器类型', trigger: 'change' }
  ],
  'converter.deviceNameJsonExpression': [
    { required: true, message: '请输入设备名表达式', trigger: 'blur' }
  ],
  'converter.extension': [
    {
      required: true,
      message: '请输入扩展类名',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.converter.type === 'custom' && !value) {
          callback(new Error('请输入扩展类名'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 监听映射数据变化
watch(() => props.mapping, (newMapping) => {
  if (newMapping && props.modelValue) {
    Object.assign(form, JSON.parse(JSON.stringify(newMapping)))
  }
}, { deep: true, immediate: true })

// 监听对话框状态
watch(() => props.modelValue, (newVal) => {
  if (newVal && !props.mapping) {
    // 新增时重置表单
    Object.assign(form, JSON.parse(JSON.stringify(defaultForm)))
  }
})

// 添加属性
const addAttribute = () => {
  if (!form.converter.attributes) {
    form.converter.attributes = []
  }
  form.converter.attributes.push({
    key: '',
    type: 'string',
    value: ''
  })
}

// 删除属性
const removeAttribute = (index) => {
  form.converter.attributes.splice(index, 1)
}

// 添加遥测
const addTelemetry = () => {
  if (!form.converter.telemetry) {
    form.converter.telemetry = []
  }
  form.converter.telemetry.push({
    key: '',
    type: 'double',
    value: ''
  })
}

// 删除遥测
const removeTelemetry = (index) => {
  form.converter.telemetry.splice(index, 1)
}

// 保存配置
const handleSave = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    saving.value = true

    // 深拷贝表单数据
    const mappingData = JSON.parse(JSON.stringify(form))
    
    emit('save', mappingData)
    
    ElMessage.success(props.isEdit ? '映射配置更新成功' : '映射配置添加成功')
    handleClose()
  } catch (error) {
    console.error('保存映射配置失败:', error)
    ElMessage.error('保存映射配置失败')
  } finally {
    saving.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.converter-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  
  .converter-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #606266;
    margin-bottom: 12px;
  }
}

.empty-data {
  text-align: center;
  padding: 20px;
}

.data-keys-list {
  .data-key-item {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}
</style> 