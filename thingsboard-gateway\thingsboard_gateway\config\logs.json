{"version": 1, "disable_existing_loggers": false, "formatters": {"LogFormatter": {"class": "logging.Formatter", "format": "%(asctime)s.%(msecs)03d - |%(levelname)s| - [%(filename)s] - %(module)s - %(funcName)s - %(lineno)d - %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}}, "handlers": {"consoleHandler": {"class": "logging.StreamHandler", "formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "level": 0, "stream": "ext://sys.stdout"}, "databaseHandler": {"class": "thingsboard_gateway.tb_utility.tb_rotating_file_handler.TimedRotatingFileHandler", "formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "filename": "./logs/database.log", "backupCount": 1, "encoding": "utf-8"}, "serviceHandler": {"class": "thingsboard_gateway.tb_utility.tb_rotating_file_handler.TimedRotatingFileHandler", "formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "filename": "./logs/service.log", "backupCount": 7, "interval": 3, "when": "D", "maxBytes": 0, "encoding": "utf-8"}, "connectorHandler": {"class": "thingsboard_gateway.tb_utility.tb_rotating_file_handler.TimedRotatingFileHandler", "formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "filename": "./logs/connector.log", "backupCount": 7, "interval": 3, "when": "D", "maxBytes": 0, "encoding": "utf-8"}, "converterHandler": {"class": "thingsboard_gateway.tb_utility.tb_rotating_file_handler.TimedRotatingFileHandler", "formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "filename": "./logs/converter.log", "backupCount": 7, "interval": 3, "when": "D", "maxBytes": 0, "encoding": "utf-8"}, "tb_connectionHandler": {"class": "thingsboard_gateway.tb_utility.tb_rotating_file_handler.TimedRotatingFileHandler", "formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "filename": "./logs/tb_connection.log", "backupCount": 7, "interval": 3, "when": "D", "maxBytes": 0, "encoding": "utf-8"}, "storageHandler": {"class": "thingsboard_gateway.tb_utility.tb_rotating_file_handler.TimedRotatingFileHandler", "formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "filename": "./logs/storage.log", "backupCount": 7, "interval": 3, "when": "D", "maxBytes": 0, "encoding": "utf-8"}, "extensionHandler": {"class": "thingsboard_gateway.tb_utility.tb_rotating_file_handler.TimedRotatingFileHandler", "formatter": "Log<PERSON><PERSON><PERSON><PERSON>", "filename": "./logs/extension.log", "backupCount": 7, "interval": 3, "when": "D", "maxBytes": 0, "encoding": "utf-8"}}, "loggers": {"database": {"handlers": ["database<PERSON><PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>"], "level": "INFO", "propagate": false}, "service": {"handlers": ["serviceHandler", "<PERSON><PERSON><PERSON><PERSON>"], "level": "INFO", "propagate": false}, "connector": {"handlers": ["connectorHandler", "<PERSON><PERSON><PERSON><PERSON>"], "level": "INFO", "propagate": false}, "converter": {"handlers": ["converterHandler", "<PERSON><PERSON><PERSON><PERSON>"], "level": "INFO", "propagate": false}, "tb_connection": {"handlers": ["tb_<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "level": "INFO", "propagate": false}, "storage": {"handlers": ["storageHandler", "<PERSON><PERSON><PERSON><PERSON>"], "level": "INFO", "propagate": false}, "extension": {"handlers": ["extension<PERSON><PERSON>ler", "<PERSON><PERSON><PERSON><PERSON>"], "level": "INFO", "propagate": false}}, "root": {"level": "ERROR", "handlers": ["<PERSON><PERSON><PERSON><PERSON>"]}}