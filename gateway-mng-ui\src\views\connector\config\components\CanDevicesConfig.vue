<template>
  <div class="can-devices-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>CAN设备配置管理</span>
          <el-button type="primary" size="small" @click="handleAddDevice">
            <el-icon><Plus /></el-icon>添加设备
          </el-button>
        </div>
      </template>
      
      <!-- 设备列表 -->
      <div v-if="devices.length === 0" class="empty-state">
        <el-empty description="暂无设备配置">
          <el-button type="primary" @click="handleAddDevice">添加第一个设备</el-button>
        </el-empty>
      </div>
      
      <div v-else class="devices-grid">
        <div 
          v-for="(device, index) in devices" 
          :key="index" 
          class="device-card"
        >
          <el-card shadow="hover">
            <template #header>
              <div class="device-header">
                <div class="device-info">
                  <h4 class="device-name">{{ device.name || 'Unknown Device' }}</h4>
                  <div class="device-badges">
                    <el-tag v-if="device.sendDataOnlyOnChange" size="small" type="info">仅变化发送</el-tag>
                    <el-tag v-if="device.enableUnknownRpc" size="small" type="success">允许未知RPC</el-tag>
                    <el-tag v-if="device.strictEval" size="small" type="warning">严格模式</el-tag>
                  </div>
                </div>
                <div class="device-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    link 
                    @click="handleEditDevice(device, index)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    link 
                    @click="handleDeleteDevice(index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="device-details">
              <div class="detail-item">
                <span class="label">设备名称:</span>
                <span class="value">{{ device.name || 'N/A' }}</span>
              </div>
              <div class="detail-item">
                <span class="label">属性数量:</span>
                <span class="value">{{ (device.attributes || []).length }}</span>
              </div>
              <div class="detail-item">
                <span class="label">遥测数量:</span>
                <span class="value">{{ (device.timeseries || []).length }}</span>
              </div>
              <div class="detail-item">
                <span class="label">属性更新数量:</span>
                <span class="value">{{ (device.attributeUpdates || []).length }}</span>
              </div>
              <div class="detail-item">
                <span class="label">RPC数量:</span>
                <span class="value">{{ (device.serverSideRpc || []).length }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- CAN设备配置对话框 -->
    <CanDeviceDialog
      v-model="deviceDialogVisible"
      :device="editingDevice"
      :is-edit="editingDeviceIndex !== -1"
      @save="handleSaveDevice"
      @cancel="handleCancelDevice"
    />
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import CanDeviceDialog from './CanDeviceDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const devices = ref([...props.modelValue])
const deviceDialogVisible = ref(false)
const editingDevice = ref({})
const editingDeviceIndex = ref(-1)

// 监听外部设备数据变化
watch(() => props.modelValue, (newDevices) => {
  devices.value = [...newDevices]
}, { deep: true, immediate: true })

// 监听内部设备数据变化
watch(devices, (newDevices) => {
  emit('update:modelValue', newDevices)
}, { deep: true })

// 设备操作
const handleAddDevice = () => {
  editingDeviceIndex.value = -1
  editingDevice.value = createDefaultDevice()
  deviceDialogVisible.value = true
}

const handleEditDevice = (device, index) => {
  editingDeviceIndex.value = index
  editingDevice.value = JSON.parse(JSON.stringify(device))
  deviceDialogVisible.value = true
}

const handleDeleteDevice = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个CAN设备配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    devices.value.splice(index, 1)
    ElMessage.success('设备删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleSaveDevice = (deviceData) => {
  if (editingDeviceIndex.value === -1) {
    // 添加模式
    devices.value.push(JSON.parse(JSON.stringify(deviceData)))
  } else {
    // 编辑模式
    devices.value[editingDeviceIndex.value] = JSON.parse(JSON.stringify(deviceData))
  }
  
  deviceDialogVisible.value = false
  editingDevice.value = {}
  editingDeviceIndex.value = -1
}

const handleCancelDevice = () => {
  deviceDialogVisible.value = false
  editingDevice.value = {}
  editingDeviceIndex.value = -1
}

// 创建默认设备配置
const createDefaultDevice = () => {
  return {
    name: 'Car',
    sendDataOnlyOnChange: false,
    enableUnknownRpc: true,
    strictEval: false,
    attributes: [],
    timeseries: [],
    attributeUpdates: [],
    serverSideRpc: []
  }
}
</script>

<style lang="scss" scoped>
.can-devices-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
  
  .devices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    
    .device-card {
      .device-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .device-info {
          flex: 1;
          
          .device-name {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            word-break: break-word;
          }
          
          .device-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
          }
        }
        
        .device-actions {
          display: flex;
          gap: 8px;
          margin-left: 12px;
        }
      }
      
      .device-details {
        margin-top: 16px;
        
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 0;
          border-bottom: 1px solid #f5f7fa;
          
          &:last-child {
            border-bottom: none;
          }
          
          .label {
            font-size: 13px;
            color: #909399;
            font-weight: 500;
          }
          
          .value {
            font-size: 13px;
            color: #303133;
            font-weight: 500;
          }
        }
      }
    }
  }
}
</style> 