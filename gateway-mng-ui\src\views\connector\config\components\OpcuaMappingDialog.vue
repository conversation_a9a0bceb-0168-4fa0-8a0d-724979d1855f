<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑设备映射' : '添加设备映射'"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="mappingForm" :rules="rules" ref="formRef" label-width="160px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备节点模式" prop="deviceNodePattern" required>
                <el-input 
                  v-model="mappingForm.deviceNodePattern" 
                  placeholder="Root\\.Objects\\.Device1"
                />
                <div class="field-hint">设备节点的匹配模式，支持正则表达式</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="节点源类型" prop="deviceNodeSource" required>
                <el-select 
                  v-model="mappingForm.deviceNodeSource" 
                  placeholder="请选择节点源类型"
                >
                  <el-option label="路径" value="path" />
                  <el-option label="标识符" value="identifier" />
                </el-select>
                <div class="field-hint">节点引用的类型：路径或标识符</div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 设备信息配置 -->
          <div class="config-section">
            <h4 class="section-title">设备信息配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备名称表达式" prop="deviceInfo.deviceNameExpression" required>
                  <el-input 
                    v-model="mappingForm.deviceInfo.deviceNameExpression" 
                    placeholder="Device ${Root\\.Objects\\.Device1\\.serialNumber}"
                  />
                  <div class="field-hint">设备名称表达式，支持变量替换</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="名称表达式源" prop="deviceInfo.deviceNameExpressionSource">
                  <el-select 
                    v-model="mappingForm.deviceInfo.deviceNameExpressionSource" 
                    placeholder="请选择表达式源类型"
                  >
                    <el-option label="路径" value="path" />
                    <el-option label="标识符" value="identifier" />
                    <el-option label="常量" value="constant" />
                  </el-select>
                  <div class="field-hint">设备名称表达式的数据源类型</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备配置文件" prop="deviceInfo.deviceProfileExpression">
                  <el-input 
                    v-model="mappingForm.deviceInfo.deviceProfileExpression" 
                    placeholder="Device"
                  />
                  <div class="field-hint">设备配置文件名称</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="配置文件源" prop="deviceInfo.deviceProfileExpressionSource">
                  <el-select 
                    v-model="mappingForm.deviceInfo.deviceProfileExpressionSource" 
                    placeholder="请选择配置文件源类型"
                  >
                    <el-option label="路径" value="path" />
                    <el-option label="标识符" value="identifier" />
                    <el-option label="常量" value="constant" />
                  </el-select>
                  <div class="field-hint">设备配置文件的数据源类型</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <OpcuaDataKeys
            v-model="mappingForm.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从OPC-UA服务器读取的属性数据"
          />
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="timeseries">
          <OpcuaDataKeys
            v-model="mappingForm.timeseries"
            data-type="timeseries"
            title="遥测数据配置"
            description="配置从OPC-UA服务器读取的遥测数据"
          />
        </el-tab-pane>

        <!-- RPC配置 -->
        <el-tab-pane label="RPC配置" name="rpc_methods">
          <OpcuaDataKeys
            v-model="mappingForm.rpc_methods"
            data-type="rpc_methods"
            title="RPC方法配置"
            description="配置OPC-UA服务器的远程过程调用方法"
          />
        </el-tab-pane>

        <!-- 属性更新配置 -->
        <el-tab-pane label="属性更新" name="attributes_updates">
          <OpcuaDataKeys
            v-model="mappingForm.attributes_updates"
            data-type="attributes_updates"
            title="属性更新配置"
            description="配置向OPC-UA服务器写入属性的操作"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import OpcuaDataKeys from './OpcuaDataKeys.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mapping: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const activeTab = ref('basic')
const saving = ref(false)

// 映射表单数据
const mappingForm = reactive({
  deviceNodePattern: 'Root\\.Objects\\.Device1',
  deviceNodeSource: 'path',
  deviceInfo: {
    deviceNameExpression: 'Device ${Root\\.Objects\\.Device1\\.serialNumber}',
    deviceNameExpressionSource: 'path',
    deviceProfileExpression: 'Device',
    deviceProfileExpressionSource: 'constant'
  },
  attributes: [],
  timeseries: [],
  rpc_methods: [],
  attributes_updates: []
})

// 表单验证规则
const rules = reactive({
  deviceNodePattern: [
    { required: true, message: '请输入设备节点模式', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  deviceNodeSource: [
    { required: true, message: '请选择节点源类型', trigger: 'change' }
  ],
  'deviceInfo.deviceNameExpression': [
    { required: true, message: '请输入设备名称表达式', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.mapping) {
    // 加载映射数据
    Object.assign(mappingForm, {
      deviceNodePattern: 'Root\\.Objects\\.Device1',
      deviceNodeSource: 'path',
      deviceInfo: {
        deviceNameExpression: 'Device ${Root\\.Objects\\.Device1\\.serialNumber}',
        deviceNameExpressionSource: 'path',
        deviceProfileExpression: 'Device',
        deviceProfileExpressionSource: 'constant'
      },
      attributes: [],
      timeseries: [],
      rpc_methods: [],
      attributes_updates: [],
      ...props.mapping
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    // 重置表单
    activeTab.value = 'basic'
  }
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...mappingForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '映射更新成功' : '映射添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.config-section {
  margin-bottom: 30px;
  
  .section-title {
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 