<template>
  <div class="rest-connector">
    <el-form :model="restConfig" :rules="rules" ref="formRef" label-width="120px">
      <el-tabs v-model="activeTab" class="rest-tabs">
        <!-- 通用配置标签页 -->
        <el-tab-pane label="通用配置" name="general">
          <CommonGeneralConfig 
            v-model="restConfig" 
          />
        </el-tab-pane>

        <!-- 服务器配置标签页 -->
        <el-tab-pane label="服务器配置" name="server">
          <div class="config-section">
            <div class="section-title">连接配置</div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="服务器地址" prop="server.host" required>
                  <el-input 
                    v-model="restConfig.server.host" 
                    placeholder="127.0.0.1"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="端口号" prop="server.port" required>
                  <el-input-number 
                    v-model="restConfig.server.port" 
                    :min="1" 
                    :max="65535"
                    controls-position="right"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="启用SSL">
                  <el-switch v-model="restConfig.server.SSL" @change="toggleSSL" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- SSL证书配置 -->
            <div v-if="restConfig.server.SSL" class="ssl-config-section">
              <div class="section-title">SSL证书配置</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="证书文件路径" prop="server.security.cert">
                    <el-input 
                      v-model="restConfig.server.security.cert" 
                      placeholder="~/ssl/cert.pem"
                    />
                    <div class="field-hint">SSL证书文件的完整路径</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="私钥文件路径" prop="server.security.key">
                    <el-input 
                      v-model="restConfig.server.security.key" 
                      placeholder="~/ssl/key.pem"
                    />
                    <div class="field-hint">SSL私钥文件的完整路径</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>

        <!-- 映射配置标签页 -->
        <el-tab-pane label="端点映射" name="mapping">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">REST端点映射</div>
              <el-button type="primary" :icon="Plus" @click="showMappingDialog()">
                添加端点映射
              </el-button>
            </div>
            
            <div v-if="restConfig.mapping.length === 0" class="empty-state">
              <el-empty description="暂无端点映射配置">
                <el-button type="primary" @click="showMappingDialog()">添加第一个端点映射</el-button>
              </el-empty>
            </div>
            
            <div v-else class="mapping-cards">
              <el-card 
                v-for="(mapping, index) in restConfig.mapping" 
                :key="index" 
                class="mapping-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <div class="card-title">
                      <span class="mapping-name">端点映射 {{ index + 1 }}</span>
                      <div class="mapping-tags">
                        <el-tag 
                          v-for="method in mapping.HTTPMethods" 
                          :key="method" 
                          :type="method === 'GET' ? 'success' : 'info'" 
                          size="small"
                        >
                          {{ method }}
                        </el-tag>
                        <el-tag 
                          :type="mapping.security?.type === 'anonymous' ? 'info' : 'warning'" 
                          size="small"
                        >
                          {{ mapping.security?.type === 'anonymous' ? '匿名访问' : '基本认证' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="card-actions">
                      <el-button size="small" @click="editMapping(index, mapping)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button size="small" type="danger" @click="deleteMapping(index)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div class="mapping-summary">
                  <div class="summary-item">
                    <span class="label">端点路径:</span>
                    <span class="value">{{ mapping.endpoint || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">转换器类型:</span>
                    <span class="value">{{ mapping.converter?.type === 'json' ? 'JSON转换器' : '自定义转换器' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">设备名表达式:</span>
                    <span class="value">{{ mapping.converter?.deviceInfo?.deviceNameExpression || '未设置' }}</span>
                  </div>
                  <div class="summary-item" v-if="mapping.converter?.type === 'json'">
                    <span class="label">数据点:</span>
                    <span class="value">
                      属性{{ mapping.converter?.attributes?.length || 0 }}个，
                      遥测{{ mapping.converter?.timeseries?.length || 0 }}个
                    </span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 属性请求标签页 -->
        <el-tab-pane label="属性请求" name="attributeRequests">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">属性请求配置</div>
              <el-button type="primary" :icon="Plus" @click="showAttributeRequestDialog()">
                添加属性请求
              </el-button>
            </div>
            
            <div v-if="restConfig.requestsMapping.attributeRequests.length === 0" class="empty-state">
              <el-empty description="暂无属性请求配置">
                <el-button type="primary" @click="showAttributeRequestDialog()">添加第一个属性请求</el-button>
              </el-empty>
            </div>
            
            <div v-else class="attribute-request-cards">
              <el-card 
                v-for="(request, index) in restConfig.requestsMapping.attributeRequests" 
                :key="index" 
                class="attribute-request-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <div class="card-title">
                      <span class="request-name">属性请求 {{ index + 1 }}</span>
                      <div class="request-tags">
                        <el-tag 
                          v-for="method in request.HTTPMethods" 
                          :key="method" 
                          :type="method === 'GET' ? 'success' : 'info'" 
                          size="small"
                        >
                          {{ method }}
                        </el-tag>
                        <el-tag 
                          :type="request.type === 'shared' ? 'info' : 'warning'" 
                          size="small"
                        >
                          {{ request.type === 'shared' ? '共享属性' : '客户端属性' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="card-actions">
                      <el-button size="small" @click="editAttributeRequest(index, request)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button size="small" type="danger" @click="deleteAttributeRequest(index)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div class="request-summary">
                  <div class="summary-item">
                    <span class="label">端点路径:</span>
                    <span class="value">{{ request.endpoint || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">设备名表达式:</span>
                    <span class="value">{{ request.deviceNameExpression || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">属性名表达式:</span>
                    <span class="value">{{ request.attributeNameExpression || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">超时时间:</span>
                    <span class="value">{{ request.timeout || 10 }}秒</span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 属性更新标签页 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">属性更新配置</div>
              <el-button type="primary" :icon="Plus" @click="showAttributeUpdateDialog()">
                添加属性更新
              </el-button>
            </div>
            
            <div v-if="restConfig.requestsMapping.attributeUpdates.length === 0" class="empty-state">
              <el-empty description="暂无属性更新配置">
                <el-button type="primary" @click="showAttributeUpdateDialog()">添加第一个属性更新配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="attribute-update-cards">
              <el-card 
                v-for="(update, index) in restConfig.requestsMapping.attributeUpdates" 
                :key="index" 
                class="attribute-update-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <div class="card-title">
                      <span class="update-name">属性更新 {{ index + 1 }}</span>
                      <div class="update-tags">
                        <el-tag 
                          v-for="method in update.HTTPMethods" 
                          :key="method" 
                          :type="method === 'POST' ? 'info' : 'warning'" 
                          size="small"
                        >
                          {{ method }}
                        </el-tag>
                        <el-tag 
                          :type="update.security?.type === 'anonymous' ? 'info' : 'warning'" 
                          size="small"
                        >
                          {{ update.security?.type === 'anonymous' ? '匿名访问' : '基本认证' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="card-actions">
                      <el-button size="small" @click="editAttributeUpdate(index, update)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button size="small" type="danger" @click="deleteAttributeUpdate(index)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div class="update-summary">
                  <div class="summary-item">
                    <span class="label">端点路径:</span>
                    <span class="value">{{ update.endpoint || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">设备筛选:</span>
                    <span class="value">{{ update.deviceNameFilter || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">属性筛选:</span>
                    <span class="value">{{ update.attributeFilter || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">超时设置:</span>
                    <span class="value">{{ update.timeout || 10 }}秒</span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- RPC配置标签页 -->
        <el-tab-pane label="RPC配置" name="rpc">
          <div class="config-section">
            <div class="section-header">
              <div class="section-title">服务端RPC配置</div>
              <el-button type="primary" :icon="Plus" @click="showServerRpcDialog()">
                添加RPC配置
              </el-button>
            </div>
            
            <div v-if="restConfig.requestsMapping.serverSideRpc.length === 0" class="empty-state">
              <el-empty description="暂无RPC配置">
                <el-button type="primary" @click="showServerRpcDialog()">添加第一个RPC配置</el-button>
              </el-empty>
            </div>
            
            <div v-else class="server-rpc-cards">
              <el-card 
                v-for="(rpc, index) in restConfig.requestsMapping.serverSideRpc" 
                :key="index" 
                class="server-rpc-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <div class="card-title">
                      <span class="rpc-name">RPC配置 {{ index + 1 }}</span>
                      <div class="rpc-tags">
                        <el-tag 
                          v-for="method in rpc.HTTPMethods" 
                          :key="method" 
                          :type="method === 'GET' ? 'success' : 'info'" 
                          size="small"
                        >
                          {{ method }}
                        </el-tag>
                        <el-tag 
                          :type="rpc.security?.type === 'anonymous' ? 'info' : 'warning'" 
                          size="small"
                        >
                          {{ rpc.security?.type === 'anonymous' ? '匿名访问' : '基本认证' }}
                        </el-tag>
                      </div>
                    </div>
                    <div class="card-actions">
                      <el-button size="small" @click="editServerRpc(index, rpc)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                      <el-button size="small" type="danger" @click="deleteServerSideRpc(index)">
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div class="rpc-summary">
                  <div class="summary-item">
                    <span class="label">端点路径:</span>
                    <span class="value">{{ rpc.endpoint || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">设备筛选:</span>
                    <span class="value">{{ rpc.deviceNameFilter || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">方法筛选:</span>
                    <span class="value">{{ rpc.methodFilter || '未设置' }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">响应超时:</span>
                    <span class="value">{{ rpc.responseTimeout || 1 }}秒</span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <!-- 端点映射对话框 -->
    <RestEndpointMappingDialog
      v-model="mappingDialogVisible"
      :mapping="currentMapping"
      :is-edit="currentMappingIndex >= 0"
      @save="saveMappingConfig"
    />

    <!-- 属性请求对话框 -->
    <RestAttributeRequestDialog
      v-model="attributeRequestDialogVisible"
      :attribute-request="currentAttributeRequest"
      :is-edit="currentAttributeRequestIndex >= 0"
      @save="saveAttributeRequestConfig"
    />

    <!-- 属性更新对话框 -->
    <RestAttributeUpdateDialog
      v-model="attributeUpdateDialogVisible"
      :attribute-update="currentAttributeUpdate"
      :is-edit="currentAttributeUpdateIndex >= 0"
      @save="saveAttributeUpdateConfig"
    />

    <!-- RPC配置对话框 -->
    <RestServerRpcDialog
      v-model="serverRpcDialogVisible"
      :server-rpc="currentServerRpc"
      :is-edit="currentServerRpcIndex >= 0"
      @save="saveServerRpcConfig"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import { useRoute } from 'vue-router'
import RestEndpointMappingDialog from './components/RestEndpointMappingDialog.vue'
import RestAttributeRequestDialog from './components/RestAttributeRequestDialog.vue'
import RestAttributeUpdateDialog from './components/RestAttributeUpdateDialog.vue'
import RestServerRpcDialog from './components/RestServerRpcDialog.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const formRef = ref()
const activeTab = ref('general')

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  server: {
    type: 'HTTPs',
    host: '0.0.0.0',
    port: 5000,
    SSL: false,
    HTTPSCertificateFile: 'server.pub',
    HTTPSPrivateKeyFile: 'server.pem',
    security: {
      cert: '',
      key: ''
    }
  },
  mapping: [],
  requestsMapping: {
    attributeRequests: [],
    attributeUpdates: [],
    serverSideRpc: []
  }
}

// REST连接器配置
const restConfig = reactive({ ...defaultConfigStructure })

// 表单验证规则
const rules = reactive({
  'server.host': [
    { required: true, message: '请输入服务器地址', trigger: 'blur' }
  ],
  'server.port': [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号范围1-65535', trigger: 'change' }
  ],
  'server.security.cert': [
    { 
      required: true, 
      message: '请输入证书文件路径', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (restConfig.server.SSL && !value) {
          callback(new Error('启用SSL时必须提供证书文件路径'))
        } else {
          callback()
        }
      }
    }
  ],
  'server.security.key': [
    { 
      required: true, 
      message: '请输入私钥文件路径', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (restConfig.server.SSL && !value) {
          callback(new Error('启用SSL时必须提供私钥文件路径'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 切换SSL
const toggleSSL = (enabled) => {
  if (!enabled) {
    restConfig.server.security = {
      cert: '',
      key: ''
    }
  }
}

// 对话框状态管理
const mappingDialogVisible = ref(false)
const attributeRequestDialogVisible = ref(false)
const attributeUpdateDialogVisible = ref(false)
const serverRpcDialogVisible = ref(false)

// 编辑状态管理
const currentMappingIndex = ref(-1)
const currentAttributeRequestIndex = ref(-1)
const currentAttributeUpdateIndex = ref(-1)
const currentServerRpcIndex = ref(-1)
const currentMapping = ref(null)
const currentAttributeRequest = ref(null)
const currentAttributeUpdate = ref(null)
const currentServerRpc = ref(null)

// 显示映射对话框
const showMappingDialog = (index = -1, mapping = null) => {
  currentMappingIndex.value = index
  currentMapping.value = mapping
  mappingDialogVisible.value = true
}

// 编辑映射配置
const editMapping = (index, mapping) => {
  showMappingDialog(index, mapping)
}

// 保存映射配置
const saveMappingConfig = (mappingData) => {
  try {
    if (currentMappingIndex.value >= 0) {
      // 编辑模式
      restConfig.mapping[currentMappingIndex.value] = mappingData
      console.log('端点映射已更新:', mappingData)
    } else {
      // 新增模式
      restConfig.mapping.push(mappingData)
      console.log('端点映射已添加:', mappingData)
    }
    mappingDialogVisible.value = false
  } catch (error) {
    console.error('保存端点映射失败:', error)
    ElMessage.error('保存端点映射失败')
  }
}

// 删除映射配置
const deleteMapping = (index) => {
  restConfig.mapping.splice(index, 1)
  ElMessage.success('端点映射删除成功')
}

// 显示属性请求对话框
const showAttributeRequestDialog = (index = -1, request = null) => {
  currentAttributeRequestIndex.value = index
  currentAttributeRequest.value = request
  attributeRequestDialogVisible.value = true
}

// 编辑属性请求配置
const editAttributeRequest = (index, request) => {
  showAttributeRequestDialog(index, request)
}

// 保存属性请求配置
const saveAttributeRequestConfig = (requestData) => {
  try {
    if (currentAttributeRequestIndex.value >= 0) {
      // 编辑模式
      restConfig.requestsMapping.attributeRequests[currentAttributeRequestIndex.value] = requestData
      console.log('属性请求配置已更新:', requestData)
    } else {
      // 新增模式
      restConfig.requestsMapping.attributeRequests.push(requestData)
      console.log('属性请求配置已添加:', requestData)
    }
    attributeRequestDialogVisible.value = false
  } catch (error) {
    console.error('保存属性请求配置失败:', error)
    ElMessage.error('保存属性请求配置失败')
  }
}

// 删除属性请求配置
const deleteAttributeRequest = (index) => {
  restConfig.requestsMapping.attributeRequests.splice(index, 1)
  ElMessage.success('属性请求配置删除成功')
}

// 显示属性更新对话框
const showAttributeUpdateDialog = (index = -1, update = null) => {
  currentAttributeUpdateIndex.value = index
  currentAttributeUpdate.value = update
  attributeUpdateDialogVisible.value = true
}

// 编辑属性更新配置
const editAttributeUpdate = (index, update) => {
  showAttributeUpdateDialog(index, update)
}

// 保存属性更新配置
const saveAttributeUpdateConfig = (updateData) => {
  try {
    if (currentAttributeUpdateIndex.value >= 0) {
      // 编辑模式
      restConfig.requestsMapping.attributeUpdates[currentAttributeUpdateIndex.value] = updateData
      console.log('属性更新配置已更新:', updateData)
    } else {
      // 新增模式
      restConfig.requestsMapping.attributeUpdates.push(updateData)
      console.log('属性更新配置已添加:', updateData)
    }
    attributeUpdateDialogVisible.value = false
  } catch (error) {
    console.error('保存属性更新配置失败:', error)
    ElMessage.error('保存属性更新配置失败')
  }
}

// 删除属性更新配置
const deleteAttributeUpdate = (index) => {
  restConfig.requestsMapping.attributeUpdates.splice(index, 1)
  ElMessage.success('属性更新配置删除成功')
}

// 显示服务端RPC对话框
const showServerRpcDialog = (index = -1, rpc = null) => {
  currentServerRpcIndex.value = index
  currentServerRpc.value = rpc
  serverRpcDialogVisible.value = true
}

// 编辑服务端RPC配置
const editServerRpc = (index, rpc) => {
  showServerRpcDialog(index, rpc)
}

// 保存服务端RPC配置
const saveServerRpcConfig = (rpcData) => {
  try {
    if (currentServerRpcIndex.value >= 0) {
      // 编辑模式
      restConfig.requestsMapping.serverSideRpc[currentServerRpcIndex.value] = rpcData
      console.log('RPC配置已更新:', rpcData)
    } else {
      // 新增模式
      restConfig.requestsMapping.serverSideRpc.push(rpcData)
      console.log('RPC配置已添加:', rpcData)
    }
    serverRpcDialogVisible.value = false
  } catch (error) {
    console.error('保存RPC配置失败:', error)
    ElMessage.error('保存RPC配置失败')
  }
}

// 删除服务端RPC配置
const deleteServerSideRpc = (index) => {
  restConfig.requestsMapping.serverSideRpc.splice(index, 1)
  ElMessage.success('RPC配置删除成功')
}

// 组件挂载时加载配置
onMounted(async () => {
  try {
    const success = await initializeConnectorConfig(restConfig, 'rest', route, defaultConfigStructure)
    if (success) {
      ensureConfigId(restConfig)
      console.log('REST 连接器配置初始化成功')
    } else {
      ElMessage.warning('REST 连接器配置初始化失败')
    }
  } catch (error) {
    console.error('REST 连接器初始化失败:', error)
    ElMessage.error('REST 连接器初始化失败')
  }
})

// 暴露给父组件
defineExpose({
  rest: restConfig,
  validate: () => formRef.value?.validate()
})
</script>

<style lang="scss" scoped>
.rest-connector {
  .rest-tabs {
    :deep(.el-tabs__content) {
      padding: 20px 0;
    }
  }

  .config-section {
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
  }

  .ssl-config-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .mapping-cards,
  .attribute-request-cards,
  .attribute-update-cards,
  .server-rpc-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 16px;
    
    .mapping-card,
    .attribute-request-card,
    .attribute-update-card,
    .server-rpc-card {
      border: 1px solid #e4e7ed;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #409eff;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .card-title {
          flex: 1;
          
          .mapping-name,
          .request-name,
          .update-name,
          .rpc-name {
            font-weight: 600;
            color: #303133;
            display: block;
            margin-bottom: 8px;
          }
          
          .mapping-tags,
          .request-tags,
          .update-tags,
          .rpc-tags {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
          }
        }
        
        .card-actions {
          display: flex;
          gap: 8px;
          flex-shrink: 0;
        }
      }
      
      .mapping-summary,
      .request-summary,
      .update-summary,
      .rpc-summary {
        .summary-item {
          display: flex;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .label {
            font-weight: 500;
            color: #606266;
            min-width: 100px;
            flex-shrink: 0;
          }
          
          .value {
            color: #303133;
            word-break: break-all;
            flex: 1;
          }
        }
      }
    }
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}

:deep(.el-input-number) {
  width: 100%;
}
</style>