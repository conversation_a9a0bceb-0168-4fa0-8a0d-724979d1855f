<template>
  <div class="modbus-data-values">
    <el-tabs v-model="activeDataType" type="border-card">
      <el-tab-pane 
        v-for="(dataType, key) in dataTypes" 
        :key="key"
        :label="dataType.label" 
        :name="key">
        <div class="data-type-config">
          <div class="data-header">
            <div class="data-title">{{ dataType.label }}配置</div>
            <el-button type="primary" size="small" :icon="CirclePlus" @click="addDataItem(key)">
              添加{{ dataType.label }}
            </el-button>
          </div>

          <div v-if="dataValues[key].length === 0" class="empty-data">
            <el-empty :description="`暂无${dataType.label}配置`" :image-size="60">
              <el-button type="primary" size="small" @click="addDataItem(key)">
                添加{{ dataType.label }}
              </el-button>
            </el-empty>
          </div>

          <div v-else class="data-items">
            <div 
              v-for="(item, index) in dataValues[key]" 
              :key="index" 
              class="data-item">
              <div class="item-header">
                <span class="item-title">{{ dataType.label }} {{ index + 1 }}: {{ item.tag || '未命名' }}</span>
                <el-button type="danger" size="small" :icon="Delete" @click="removeDataItem(key, index)">
                  删除
                </el-button>
              </div>
              
              <div class="item-content">
                <el-form :model="item" label-width="120px" size="small">
                  <el-row :gutter="16">
                    <el-col :span="8">
                      <el-form-item label="标签名称" required>
                        <el-input v-model="item.tag" placeholder="数据标签" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="数据类型" required>
                        <el-select v-model="item.type" placeholder="选择数据类型">
                          <el-option 
                            v-for="type in modbusDataTypes" 
                            :key="type" 
                            :label="type" 
                            :value="type" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="对象数量" required>
                        <el-input-number 
                          v-model="item.objectsCount" 
                          :min="1" 
                          :max="100" 
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="16">
                    <el-col :span="8">
                      <el-form-item label="起始地址" required>
                        <el-input-number 
                          v-model="item.address" 
                          :min="0" 
                          :max="65535" 
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="needsFunctionCode(key)">
                      <el-form-item label="功能码">
                        <el-select v-model="item.functionCode" placeholder="选择功能码">
                          <el-option 
                            v-for="code in getFunctionCodes(key)" 
                            :key="code.value" 
                            :label="`${code.value} - ${code.label}`" 
                            :value="code.value" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="needsValue(key)">
                      <el-form-item label="初始值">
                        <el-input v-model="item.value" placeholder="初始值" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <!-- 值修饰符配置 -->
                  <el-collapse v-if="supportsModifier(item.type)" class="modifier-config">
                    <el-collapse-item title="值修饰符配置" name="modifier">
                      <el-row :gutter="16">
                        <el-col :span="8">
                          <el-form-item label="修饰符类型">
                            <el-select v-model="item.modifierType" placeholder="选择修饰符">
                              <el-option label="乘数" value="multiplier" />
                              <el-option label="除数" value="divider" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="item.modifierType === 'multiplier'">
                          <el-form-item label="乘数值">
                            <el-input-number 
                              v-model="item.multiplier" 
                              :precision="2" 
                              :step="0.1" 
                              controls-position="right" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="8" v-if="item.modifierType === 'divider'">
                          <el-form-item label="除数值">
                            <el-input-number 
                              v-model="item.divider" 
                              :precision="2" 
                              :step="0.1" 
                              :min="0.01" 
                              controls-position="right" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                  </el-collapse>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { CirclePlus, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      rpc: []
    })
  },
  registerType: {
    type: String,
    default: 'holding_registers'
  },
  isSlave: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const activeDataType = ref('attributes')

const dataValues = reactive({
  attributes: [],
  timeseries: [],
  attributeUpdates: [],
  rpc: [],
  ...props.modelValue
})

// 数据类型配置
const dataTypes = {
  attributes: {
    label: '属性数据',
    description: '设备属性信息'
  },
  timeseries: {
    label: '遥测数据',
    description: '时序数据'
  },
  attributeUpdates: {
    label: '属性更新',
    description: '属性写入操作'
  },
  rpc: {
    label: 'RPC请求',
    description: '远程过程调用'
  }
}

// MODBUS数据类型
const modbusDataTypes = [
  'string', 'bytes', 'bits', 
  '8int', '8uint', '8float', 
  '16int', '16uint', '16float', 
  '32int', '32uint', '32float', 
  '64int', '64uint', '64float'
]

// 功能码配置
const functionCodes = {
  read: [
    { value: 1, label: '读取线圈' },
    { value: 2, label: '读取离散输入' },
    { value: 3, label: '读取保持寄存器' },
    { value: 4, label: '读取输入寄存器' }
  ],
  write: [
    { value: 5, label: '写单个线圈' },
    { value: 6, label: '写单个寄存器' },
    { value: 15, label: '写多个线圈' },
    { value: 16, label: '写多个寄存器' }
  ]
}

// 方法
const addDataItem = (dataType) => {
  const newItem = createDefaultDataItem(dataType)
  dataValues[dataType].push(newItem)
  ElMessage.success(`已添加${dataTypes[dataType].label}`)
}

const removeDataItem = async (dataType, index) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这个${dataTypes[dataType].label}吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 删除数据项
    dataValues[dataType].splice(index, 1)
    
    // 调试信息
    console.log(`已删除${dataTypes[dataType].label}，当前数量:`, dataValues[dataType].length)
    
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const createDefaultDataItem = (dataType) => {
  const baseItem = {
    tag: `${dataType}_${Date.now()}`,
    type: '16int',
    objectsCount: 1,
    address: 1
  }

  // 为所有数据类型设置默认功能码
  if (dataType === 'attributeUpdates' || dataType === 'rpc') {
    baseItem.functionCode = 6
    baseItem.value = 0
  } else if (dataType === 'attributes' || dataType === 'timeseries') {
    baseItem.functionCode = 4  // 读取输入寄存器作为默认值
  }

  if (props.registerType === 'coils_initializer' || props.registerType === 'discrete_inputs') {
    baseItem.type = 'bits'
    baseItem.value = false
    // 线圈类型使用读线圈功能码
    if (dataType === 'attributes' || dataType === 'timeseries') {
      baseItem.functionCode = 1
    }
  }

  return baseItem
}

const needsFunctionCode = (dataType) => {
  // 所有数据类型都应该有功能码配置项
  return true
}

const needsValue = (dataType) => {
  return props.isSlave && (dataType === 'attributes' || dataType === 'timeseries' || dataType === 'attributeUpdates' || dataType === 'rpc')
}

const getFunctionCodes = (dataType) => {
  if (dataType === 'attributeUpdates') {
    return functionCodes.write;
  }
  if (dataType === 'rpc') {
    return [...functionCodes.read, ...functionCodes.write]; // Combine both arrays
  }
  return functionCodes.read;
};

const supportsModifier = (dataType) => {
  return !['string', 'bytes', 'bits'].includes(dataType)
}

// 监听配置变化
watch(dataValues, (newValue) => {
  // 处理输出数据，清理修饰符字段并确保正确设置
  const processedValue = {}
  Object.keys(newValue).forEach(key => {
    if (Array.isArray(newValue[key])) {
      processedValue[key] = newValue[key].map(item => {
        const outputItem = { ...item }
        
        // 处理修饰符配置
        if (outputItem.modifierType === 'multiplier') {
          delete outputItem.modifierType
          delete outputItem.divider  // 清理不需要的除数字段
          if (outputItem.multiplier === undefined || outputItem.multiplier === null) {
            delete outputItem.multiplier
          }
        } else if (outputItem.modifierType === 'divider') {
          delete outputItem.modifierType
          delete outputItem.multiplier  // 清理不需要的乘数字段
          if (outputItem.divider === undefined || outputItem.divider === null) {
            delete outputItem.divider
          }
        } else {
          // 如果没有选择修饰符类型，清理所有修饰符字段
          delete outputItem.modifierType
          delete outputItem.multiplier
          delete outputItem.divider
        }
        
        return outputItem
      })
    }
  })
  
  emit('update:modelValue', processedValue)
}, { deep: true })

// 监听外部值变化（优化避免循环更新）
watch(() => props.modelValue, (newValue) => {
  if (!newValue) return
  
  // 深度合并，确保修饰符配置正确设置，但避免不必要的更新
  Object.keys(dataValues).forEach(key => {
    if (newValue[key] && Array.isArray(newValue[key])) {
      // 只在数据真正变化时才更新
      const newData = newValue[key].map(item => {
        const processedItem = { ...item }
        
        // 处理修饰符配置
        if (item.multiplier !== undefined && item.multiplier !== null) {
          processedItem.modifierType = 'multiplier'
          processedItem.multiplier = item.multiplier
        } else if (item.divider !== undefined && item.divider !== null) {
          processedItem.modifierType = 'divider'
          processedItem.divider = item.divider
        }
        
        return processedItem
      })
      
      // 只在数据确实不同时才更新
      if (JSON.stringify(newData) !== JSON.stringify(dataValues[key])) {
        dataValues[key] = newData
      }
    } else if (dataValues[key].length > 0) {
      // 如果外部值中没有该key的数据，且当前不为空数组，才设为空数组
      dataValues[key] = []
    }
  })
}, { immediate: true })
</script>

<style lang="scss" scoped>
.modbus-data-values {
  .data-type-config {
    .data-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px;
      background: #f0f2f5;
      border-radius: 6px;

      .data-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
      }
    }

    .empty-data {
      text-align: center;
      padding: 30px 20px;
    }

    .data-items {
      .data-item {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        margin-bottom: 12px;
        overflow: hidden;

        .item-header {
          background: #fafafa;
          padding: 8px 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #e4e7ed;

          .item-title {
            font-size: 13px;
            font-weight: 500;
            color: #303133;
          }
        }

        .item-content {
          padding: 12px;
        }
      }
    }
  }

  .modifier-config {
    margin-top: 12px;
    
    :deep(.el-collapse-item__header) {
      background: #f8f9fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 0 12px;
      font-size: 13px;
    }
    
    :deep(.el-collapse-item__content) {
      padding: 12px 0;
      border: none;
    }
  }

  :deep(.el-tabs__header) {
    margin-bottom: 16px;
  }

  :deep(.el-tabs__item) {
    font-size: 13px;
    padding: 8px 16px;
  }

  :deep(.el-form-item) {
    margin-bottom: 12px;
  }

  :deep(.el-form-item__label) {
    font-size: 12px;
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input), :deep(.el-select) {
    width: 100%;
  }
}
</style> 