{"Opcua": {"name": "Opcua", "type": "opcua_asyncio", "logLevel": "DEBUG", "configuration": "opcua.json", "configurationJson": {"server": {"name": "OPC-UA Demo Server", "url": "opc.tcp://127.0.0.1:4840/freeopcua/server/", "timeoutInMillis": 5000, "scanPeriodInMillis": 1000, "disableSubscriptions": true, "subCheckPeriodInMillis": 100, "showMap": false, "security": "Basic128Rsa15", "identity": {"type": "anonymous"}, "mapping": [{"deviceNodePattern": "${ns=4;s=TestDevice}", "deviceNamePattern": "Temp Sensor", "deviceTypePattern": "default", "attributes": [], "timeseries": [{"key": "path", "path": "${Humidity}"}, {"key": "i", "path": "${ns=4;i=2}"}, {"key": "s", "path": "${ns=4;s=Humidity_S}"}, {"key": "g", "path": "${ns=4;g=018dd02c-fd22-754a-b6d3-5fcae91cd38d}"}, {"key": "b", "path": "${ns=4;b=Status_S}"}], "rpc_methods": [], "attributes_updates": []}]}}}}