<template>
  <div class="http-headers">
    <div v-if="headersList.length === 0" class="empty-headers">
      <el-empty description="暂无HTTP头配置" :image-size="60">
        <el-button type="primary" size="small" @click="addHeader">添加第一个HTTP头</el-button>
      </el-empty>
    </div>
    
    <div v-else class="headers-list">
      <div v-for="(header, index) in headersList" :key="index" class="header-item">
        <el-card shadow="never" class="header-card">
          <el-row :gutter="16" align="middle">
            <el-col :span="10">
              <el-form-item label="Header名称" :label-width="90">
                <el-input 
                  v-model="header.key" 
                  placeholder="Content-Type"
                  @input="updateHeaders"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Header值" :label-width="80">
                <el-input 
                  v-model="header.value" 
                  placeholder="application/json"
                  @input="updateHeaders"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button 
                type="danger" 
                size="small" 
                :icon="Delete" 
                circle 
                @click="removeHeader(index)"
                title="删除HTTP头"
              />
            </el-col>
          </el-row>
        </el-card>
      </div>
    </div>
    
    <div class="add-header-section">
      <el-button type="primary" size="small" @click="addHeader" :icon="Plus">
        添加HTTP头
      </el-button>
      <el-button type="info" size="small" @click="addCommonHeaders" :icon="Setting">
        添加常用头
      </el-button>
    </div>
    
    <!-- 常用HTTP头快速添加 -->
    <el-dialog v-model="commonHeadersVisible" title="选择常用HTTP头" width="500px">
      <div class="common-headers">
        <el-checkbox-group v-model="selectedCommonHeaders">
          <div class="header-options">
            <el-checkbox 
              v-for="commonHeader in commonHeadersOptions" 
              :key="commonHeader.key"
              :label="commonHeader.key"
              class="header-option"
            >
              <div class="header-info">
                <span class="header-name">{{ commonHeader.key }}</span>
                <span class="header-desc">{{ commonHeader.description }}</span>
              </div>
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      
      <template #footer>
        <el-button @click="commonHeadersVisible = false">取消</el-button>
        <el-button type="primary" @click="addSelectedCommonHeaders">添加选中的头</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Plus, Delete, Setting } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const commonHeadersVisible = ref(false)
const selectedCommonHeaders = ref([])

// 常用HTTP头选项
const commonHeadersOptions = [
  { key: 'Accept', value: 'application/json', description: '接受的内容类型' },
  { key: 'Content-Type', value: 'application/json', description: '请求内容类型' },
  { key: 'Authorization', value: 'Bearer token', description: '认证信息' },
  { key: 'User-Agent', value: 'IoTCloud-Gateway', description: '用户代理' },
  { key: 'Accept-Encoding', value: 'gzip, deflate', description: '接受的编码方式' },
  { key: 'Cache-Control', value: 'no-cache', description: '缓存控制' },
  { key: 'Connection', value: 'keep-alive', description: '连接方式' },
  { key: 'Accept-Language', value: 'zh-CN,zh;q=0.9,en;q=0.8', description: '接受的语言' }
]

// 将对象格式的headers转换为数组格式便于编辑
const headersList = computed(() => {
  const headers = props.modelValue || {}
  return Object.entries(headers).map(([key, value]) => ({
    key,
    value: value || ''
  }))
})

// 添加新的HTTP头
const addHeader = () => {
  const newHeaders = { ...props.modelValue }
  let newKey = 'Header'
  let counter = 1
  
  // 确保键名唯一
  while (newHeaders[newKey]) {
    newKey = `Header${counter}`
    counter++
  }
  
  newHeaders[newKey] = ''
  emit('update:modelValue', newHeaders)
}

// 删除HTTP头
const removeHeader = (index) => {
  const headers = headersList.value
  if (index >= 0 && index < headers.length) {
    const newHeaders = { ...props.modelValue }
    delete newHeaders[headers[index].key]
    emit('update:modelValue', newHeaders)
    ElMessage.success('HTTP头删除成功')
  }
}

// 更新headers对象
const updateHeaders = () => {
  const newHeaders = {}
  headersList.value.forEach(header => {
    if (header.key && header.key.trim()) {
      newHeaders[header.key.trim()] = header.value || ''
    }
  })
  emit('update:modelValue', newHeaders)
}

// 显示常用headers对话框
const addCommonHeaders = () => {
  selectedCommonHeaders.value = []
  commonHeadersVisible.value = true
}

// 添加选中的常用headers
const addSelectedCommonHeaders = () => {
  if (selectedCommonHeaders.value.length === 0) {
    ElMessage.warning('请选择要添加的HTTP头')
    return
  }
  
  const newHeaders = { ...props.modelValue }
  selectedCommonHeaders.value.forEach(headerKey => {
    const commonHeader = commonHeadersOptions.find(h => h.key === headerKey)
    if (commonHeader && !newHeaders[headerKey]) {
      newHeaders[headerKey] = commonHeader.value
    }
  })
  
  emit('update:modelValue', newHeaders)
  commonHeadersVisible.value = false
  ElMessage.success(`成功添加 ${selectedCommonHeaders.value.length} 个HTTP头`)
}

// 监听modelValue变化，确保数据同步
watch(() => props.modelValue, (newValue) => {
  if (!newValue || Object.keys(newValue).length === 0) {
    // 如果没有headers，提供默认的Accept头
    emit('update:modelValue', { 'ACCEPT': 'application/json' })
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.http-headers {
  .empty-headers {
    text-align: center;
    padding: 20px 0;
  }
  
  .headers-list {
    .header-item {
      margin-bottom: 12px;
      
      .header-card {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        
        &:hover {
          border-color: #c0c4cc;
        }
      }
    }
  }
  
  .add-header-section {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
    display: flex;
    gap: 12px;
  }
  
  .common-headers {
    .header-options {
      display: flex;
      flex-direction: column;
      gap: 12px;
      
      .header-option {
        .header-info {
          display: flex;
          flex-direction: column;
          margin-left: 8px;
          
          .header-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 2px;
          }
          
          .header-desc {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
}

:deep(.el-card__body) {
  padding: 12px 16px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-checkbox) {
  width: 100%;
  
  .el-checkbox__label {
    width: 100%;
  }
}
</style> 