<template>
  <div class="xmpp-server-config">
    <el-form :model="serverConfig" :rules="rules" label-width="140px" ref="formRef">
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <span>XMPP服务器配置</span>
            <el-tooltip content="XMPP连接和认证配置管理" placement="top">
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
      
        <!-- 连接配置 -->
        <div class="config-section">
          <h4 class="section-title">连接配置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="JID" prop="jid" required>
                <el-input 
                  v-model="serverConfig.jid" 
                  placeholder="gateway@localhost"
                  
                />
                <div class="field-hint">XMPP Jabber ID，格式：用户名@服务器</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="密码" prop="password" required>
                <el-input 
                  v-model="serverConfig.password" 
                  type="password"
                  placeholder="password"
                  show-password
                  
                />
                <div class="field-hint">XMPP服务器认证密码</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="服务器地址" prop="host" required>
                <el-input 
                  v-model="serverConfig.host" 
                  placeholder="localhost"
                  
                />
                <div class="field-hint">XMPP服务器的主机地址</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="端口号" prop="port" required>
                <el-input-number 
                  v-model.number="serverConfig.port" 
                  :min="1" 
                  :max="65535"
                  controls-position="right"
                  placeholder="5222"
                  
                  style="width: 100%"
                />
                <div class="field-hint">XMPP服务器端口 (默认5222)</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="连接超时" prop="timeout">
                <el-input-number 
                  v-model.number="serverConfig.timeout" 
                  :min="1000" 
                  :max="60000"
                  :step="1000"
                  controls-position="right"
                  placeholder="10000"
                  
                  style="width: 100%"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">连接超时时间 (1-60秒)</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- SSL/TLS配置 -->
        <div class="config-section">
          <h4 class="section-title">安全配置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="启用SSL">
                <el-switch 
                  v-model="serverConfig.use_ssl"
                  
                />
                <div class="field-hint">使用SSL加密连接</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="禁用STARTTLS">
                <el-switch 
                  v-model="serverConfig.disable_starttls"
                  
                />
                <div class="field-hint">禁用STARTTLS升级机制</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="强制STARTTLS">
                <el-switch 
                  v-model="serverConfig.force_starttls"
                  
                />
                <div class="field-hint">强制使用STARTTLS加密</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 插件配置 -->
        <div class="config-section">
          <h4 class="section-title">插件配置</h4>
          <el-form-item label="XMPP扩展插件">
            <div class="plugins-config">
              <div v-for="(plugin, index) in serverConfig.plugins" :key="index" class="plugin-item">
                <el-select
                  v-model="serverConfig.plugins[index]"
                  filterable
                  allow-create
                  placeholder="选择或输入插件名称"
                  
                  style="width: 100%"
                >
                  <el-option label="xep_0030 - Service Discovery" value="xep_0030" />
                  <el-option label="xep_0323 - IoT Sensor Data" value="xep_0323" />
                  <el-option label="xep_0325 - IoT Control" value="xep_0325" />
                  <el-option label="xep_0004 - Data Forms" value="xep_0004" />
                  <el-option label="xep_0060 - Publish-Subscribe" value="xep_0060" />
                  <el-option label="xep_0065 - SOCKS5 Bytestreams" value="xep_0065" />
                  <el-option label="xep_0085 - Chat State Notifications" value="xep_0085" />
                  <el-option label="xep_0115 - Entity Capabilities" value="xep_0115" />
                  <el-option label="xep_0199 - XMPP Ping" value="xep_0199" />
                </el-select>
                <el-button 
                  type="danger" 
                  circle 
                  @click="removePlugin(index)"
                  :disabled="serverConfig.plugins.length === 1"
                >
                  <el-icon><Minus /></el-icon>
                </el-button>
              </div>
              <el-button type="primary" text @click="addPlugin">
                <el-icon><Plus /></el-icon>添加插件
              </el-button>
            </div>
            <div class="field-hint">XMPP扩展协议插件，用于支持IoT功能</div>
          </el-form-item>
        </div>
      </el-card>

      <!-- 高级设置 -->
      <el-card class="advanced-settings">
        <template #header>
          <div class="card-header">
            <span>高级设置</span>
            <el-button 
              type="text" 
              @click="showAdvanced = !showAdvanced"
              :icon="showAdvanced ? ArrowUp : ArrowDown"
            >
              {{ showAdvanced ? '收起' : '展开' }}
            </el-button>
          </div>
        </template>
        
        <el-collapse-transition>
          <div v-show="showAdvanced">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="心跳间隔" prop="heartbeatInterval">
                  <el-input-number 
                    v-model.number="serverConfig.heartbeatInterval" 
                    :min="30000" 
                    :max="300000"
                    :step="1000"
                    controls-position="right"
                    placeholder="60000"
                    
                    style="width: 100%"
                  />
                  <span class="unit-suffix">毫秒</span>
                  <div class="field-hint">发送心跳包的间隔时间</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="重连间隔" prop="reconnectInterval">
                  <el-input-number 
                    v-model.number="serverConfig.reconnectInterval" 
                    :min="5000" 
                    :max="300000"
                    :step="1000"
                    controls-position="right"
                    placeholder="30000"
                    
                    style="width: 100%"
                  />
                  <span class="unit-suffix">毫秒</span>
                  <div class="field-hint">连接断开后重连间隔</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最大重连次数" prop="maxReconnectAttempts">
                  <el-input-number 
                    v-model.number="serverConfig.maxReconnectAttempts" 
                    :min="0" 
                    :max="100"
                    controls-position="right"
                    placeholder="5"
                    
                    style="width: 100%"
                  />
                  <div class="field-hint">最大重连尝试次数 (0表示无限重连)</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="启用调试日志" prop="enableDebugLog">
                  <el-switch 
                    v-model="serverConfig.enableDebugLog"
                    
                  />
                  <div class="field-hint">启用详细的XMPP协议调试日志</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="自动重连" prop="autoReconnect">
                  <el-switch 
                    v-model="serverConfig.autoReconnect"
                    
                  />
                  <div class="field-hint">连接断开时自动重连</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { 
  QuestionFilled, 
  ArrowUp, 
  ArrowDown,
  Plus,
  Minus
} from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      jid: 'gateway@localhost',
      password: 'password',
      host: 'localhost',
      port: 5222,
      use_ssl: false,
      disable_starttls: false,
      force_starttls: true,
      timeout: 10000,
      plugins: ['xep_0030', 'xep_0323', 'xep_0325'],
      heartbeatInterval: 60000,
      reconnectInterval: 30000,
      maxReconnectAttempts: 5,
      enableDebugLog: false,
      autoReconnect: true
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const showAdvanced = ref(false)

// 服务器配置
const serverConfig = reactive({
  jid: 'gateway@localhost',
  password: 'password',
  host: 'localhost',
  port: 5222,
  use_ssl: false,
  disable_starttls: false,
  force_starttls: true,
  timeout: 10000,
  plugins: ['xep_0030', 'xep_0323', 'xep_0325'],
  heartbeatInterval: 60000,
  reconnectInterval: 30000,
  maxReconnectAttempts: 5,
  enableDebugLog: false,
  autoReconnect: true,
  ...props.modelValue
})

// 表单验证规则
const rules = {
  jid: [
    { required: true, message: '请输入JID', trigger: 'blur' },
    { pattern: /^[^@]+@[^@]+$/, message: '请输入有效的JID格式 (用户名@服务器)', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 1, max: 128, message: '密码长度在1-128个字符之间', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { min: 1, max: 255, message: '服务器地址长度在1-255个字符之间', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
  ],
  timeout: [
    { type: 'number', min: 1000, max: 60000, message: '超时时间必须在1-60秒之间', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(serverConfig, {
      jid: 'gateway@localhost',
      password: 'password',
      host: 'localhost',
      port: 5222,
      use_ssl: false,
      disable_starttls: false,
      force_starttls: true,
      timeout: 10000,
      plugins: ['xep_0030', 'xep_0323', 'xep_0325'],
      heartbeatInterval: 60000,
      reconnectInterval: 30000,
      maxReconnectAttempts: 5,
      enableDebugLog: false,
      autoReconnect: true,
      ...newValue
    })
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(serverConfig, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
}, { deep: true })

// 处理配置变化
const handleChange = () => {
  // 触发验证
  formRef.value?.validateField(Object.keys(rules))
}

// 插件操作
const addPlugin = () => {
  serverConfig.plugins.push('')
}

const removePlugin = (index) => {
  if (serverConfig.plugins.length > 1) {
    serverConfig.plugins.splice(index, 1)
  }
}

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.xmpp-server-config {
  .config-card {
    border: 1px solid #e4e7ed;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
      font-size: 16px;
      
      .el-icon {
        color: #909399;
        cursor: help;
      }
    }
  }
  
  .config-section {
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      border-left: 3px solid #409eff;
      padding-left: 8px;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .unit-suffix {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
  }
  
  .plugins-config {
    .plugin-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      
      .el-select {
        flex: 1;
      }
    }
  }
  
  .advanced-settings {
    margin-top: 24px;
    border: 1px solid #e4e7ed;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
      font-size: 14px;
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 16px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      font-size: 13px;
    }
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
  
  :deep(.el-select) {
    width: 100%;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .xmpp-server-config {
    :deep(.el-form-item) {
      margin-bottom: 12px;
      
      .el-form-item__label {
        font-size: 12px;
      }
    }
  }
}
</style> 