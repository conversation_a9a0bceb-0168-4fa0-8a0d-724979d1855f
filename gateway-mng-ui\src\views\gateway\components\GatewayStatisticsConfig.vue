<template>
  <div class="gateway-statistics-config">
    <el-card class="config-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>数据统计配置</span>
          <el-switch
            v-model="localConfig.enable"
            @change="handleConfigChange"
            active-text="启用"
            inactive-text="禁用"
          />
        </div>
      </template>

      <div class="config-content">
        <!-- 基础统计配置 -->
        <div class="basic-statistics-section">
          <el-divider content-position="left">
            <span class="divider-text">基础统计配置</span>
          </el-divider>
          
          <el-form :model="localConfig" label-width="160px" label-position="left">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item>
                  <template #label>
                    <div class="form-label-with-switch">
                      <span>基础统计</span>
                      <el-switch
                        v-model="localConfig.enable"
                        @change="handleConfigChange"
                        size="small"
                      />
                    </div>
                  </template>
                  <el-input-number
                    v-model="localConfig.statsSendPeriodInSeconds"
                    :min="60"
                    :precision="0"
                    controls-position="right"
                    style="width: 100%"
                    @change="handleConfigChange"
                    :disabled="!localConfig.enable"
                    placeholder="发送周期(秒)"
                  />
                  <div class="form-item-tip">
                    网关基础运行状态统计数据的上报周期，包括连接状态、设备数量等系统级信息
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item>
                  <template #label>
                    <div class="form-label-with-switch">
                      <span>自定义统计</span>
                      <el-switch
                        v-model="localConfig.enableCustom"
                        @change="handleConfigChange"
                        size="small"
                        :disabled="!localConfig.enable"
                      />
                    </div>
                  </template>
                  <el-input-number
                    v-model="localConfig.customStatsSendPeriodInSeconds"
                    :min="60"
                    :precision="0"
                    controls-position="right"
                    style="width: 100%"
                    @change="handleConfigChange"
                    :disabled="!localConfig.enable || !localConfig.enableCustom"
                    placeholder="发送周期(秒)"
                  />
                  <div class="form-item-tip">
                    自定义Shell命令统计数据的上报周期，如CPU、内存使用率等监控指标（需要先启用总开关）
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="自定义统计配置文件" v-if="localConfig.enable && localConfig.enableCustom">
              <el-input
                v-model="localConfig.configuration"
                placeholder="请输入统计配置文件名"
                @input="handleConfigChange"
                @blur="handleFileNameBlur"
                :disabled="!localConfig.enable || !localConfig.enableCustom"
              >
                <template #suffix>
                  <span class="file-extension">.json</span>
                </template>
              </el-input>
              <div class="form-item-tip">
                指定存储自定义统计命令的配置文件名，文件将保存在网关配置目录中
              </div>
            </el-form-item>
          </el-form>

          <!-- 基础统计信息说明 -->
          <div v-if="localConfig.enable" class="basic-stats-info">
            <el-alert
              title="基础统计包含以下信息"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <ul class="stats-info-list">
                  <li>网关连接状态和连接时长</li>
                  <li>已连接的连接器数量和状态</li>
                  <li>设备总数和在线设备数</li>
                  <li>数据包发送和接收统计</li>
                  <li>错误和异常统计信息</li>
                  <li>网关系统资源使用情况</li>
                </ul>
              </template>
            </el-alert>
          </div>
        </div>

        <!-- 自定义统计命令配置 -->
        <div v-if="localConfig.enable && localConfig.enableCustom" class="statistics-commands">
          <el-divider content-position="left">
            <span class="divider-text">自定义统计命令配置</span>
          </el-divider>

          <div class="commands-container">
            <div class="section-header">
              <div class="header-left">
                <h4>系统统计命令</h4>
                <el-tag v-if="fileLoading" type="info" size="small">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  加载中...
                </el-tag>
                <el-tag v-else-if="fileSaving" type="warning" size="small">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  保存中...
                </el-tag>
                <el-tag v-else-if="localConfig.configuration" type="success" size="small">
                  {{ localConfig.configuration }}
                </el-tag>
              </div>
              <el-button 
                type="primary" 
                size="small" 
                @click="addCommand"
                icon="Plus"
              >
                添加命令
              </el-button>
            </div>
            <div class="section-description">
              配置用于收集系统统计信息的Shell命令，这些命令的输出将作为网关属性上报到IoTCloud
            </div>
            
            <div class="commands-list">
              <div 
                v-for="(command, index) in statisticsCommands" 
                :key="index"
                class="command-card"
              >
                <el-card shadow="hover">
                  <template #header>
                    <div class="command-header">
                      <div class="command-info">
                        <h4 class="command-name">{{ command.attributeOnGateway || 'Unknown Command' }}</h4>
                        <span class="command-timeout">超时: {{ command.timeout }}ms</span>
                      </div>
                      <div class="command-actions">
                        <el-button 
                          type="primary" 
                          size="small" 
                          @click="editCommand(index)"
                          icon="Edit"
                        >
                          编辑
                        </el-button>
                        <el-button 
                          type="danger" 
                          size="small" 
                          @click="removeCommand(index)"
                          icon="Delete"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </template>
                  
                  <div class="command-details">
                    <div class="detail-item">
                      <span class="label">命令:</span>
                      <code class="command-code">{{ formatCommand(command.command) }}</code>
                    </div>
                  </div>
                </el-card>
              </div>

              <!-- 空状态 -->
              <div v-if="statisticsCommands.length === 0" class="empty-state">
                <el-empty description="暂无统计命令">
                  <el-button type="primary" @click="addCommand">添加第一个命令</el-button>
                </el-empty>
              </div>
            </div>
          </div>

          <!-- 配置预览 -->
          <div class="config-preview">
            <el-divider content-position="left">
              <span class="divider-text">配置预览</span>
            </el-divider>
            <el-input
              :model-value="previewConfig"
              type="textarea"
              :rows="12"
              readonly
              class="preview-textarea"
            />
            <div class="preview-actions">
              <el-button @click="manualSaveFile" size="small" icon="Upload" type="primary" :loading="fileSaving">
                保存到文件
              </el-button>
              <el-button @click="manualLoadFile" size="small" icon="Download" :loading="fileLoading">
                从文件加载
              </el-button>
              <el-button @click="copyConfig" size="small" icon="CopyDocument">
                复制配置
              </el-button>
              <el-button @click="downloadConfig" size="small" icon="Download">
                下载配置文件
              </el-button>
              <el-button @click="loadDefaultCommands" size="small" icon="Refresh">
                加载默认命令
              </el-button>
            </div>
          </div>
        </div>

        <!-- 帮助信息 -->
        <div v-if="localConfig.enable && localConfig.enableCustom" class="help-section">
          <el-divider content-position="left">
            <span class="divider-text">使用说明</span>
          </el-divider>
          <el-alert
            title="自定义统计命令配置说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <ul class="help-list">
                <li>每个命令包含超时时间、Shell命令和属性名称</li>
                <li>命令执行结果将作为网关属性上报到IoTCloud</li>
                <li>支持复杂的Shell命令和管道操作</li>
                <li>建议设置合理的超时时间避免命令执行过久</li>
                <li>属性名称将在IoTCloud中显示为设备属性</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>

    <!-- 命令编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑统计命令' : '添加统计命令'"
      width="60%"
      :before-close="handleDialogClose"
    >
      <el-form :model="currentCommand" :rules="commandRules" ref="commandFormRef" label-width="120px">
        <el-form-item label="属性名称" prop="attributeOnGateway" required>
          <el-input
            v-model="currentCommand.attributeOnGateway"
            placeholder="请输入属性名称，如: CPU"
          />
          <div class="form-item-tip">
            该属性名称将在IoTCloud中显示
          </div>
        </el-form-item>
        
        <el-form-item label="超时时间(ms)" prop="timeout" required>
          <el-input-number
            v-model="currentCommand.timeout"
            :min="100"
            :max="30000"
            :step="100"
            controls-position="right"
            style="width: 100%"
          />
          <div class="form-item-tip">
            命令执行的最大等待时间
          </div>
        </el-form-item>
        
        <el-form-item label="Shell命令" prop="command" required>
          <el-input
            v-model="commandString"
            type="textarea"
            :rows="4"
            placeholder="请输入Shell命令，如: ps -A -o cpu,%mem | awk '{cpu += $1}END{print cpu}'"
          />
          <div class="form-item-tip">
            支持复杂的Shell命令和管道操作
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="saveCommand" :loading="saving">
            {{ isEdit ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { getFileFromName, updataFile } from '@/api/file.js'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  remoteConfiguration: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 本地配置数据
const localConfig = ref({
  enable: true,
  enableCustom: true,
  statsSendPeriodInSeconds: 60,
  customStatsSendPeriodInSeconds: 3600,
  configuration: 'statistics.json'
})

// 统计命令数据
const statisticsCommands = ref([])

// 对话框相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentIndex = ref(-1)
const saving = ref(false)
const commandFormRef = ref()

// 当前编辑的命令
const currentCommand = ref({
  timeout: 100,
  command: [],
  attributeOnGateway: ''
})

// 命令字符串（用于编辑）
const commandString = ref('')

// 表单验证规则
const commandRules = {
  attributeOnGateway: [
    { required: true, message: '请输入属性名称', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请输入超时时间', trigger: 'blur' }
  ]
}

// 默认统计命令
const defaultCommands = [
  {
    timeout: 100,
    command: ["/bin/sh", "-c", "ps -A -o cpu,%mem | awk '{cpu += $1}END{print cpu}'"],
    attributeOnGateway: "CPU"
  },
  {
    timeout: 100,
    command: ["/bin/sh", "-c", "free -m | grep Swap | awk '{print ($3/$2)*100}'"],
    attributeOnGateway: "Memory"
  },
  {
    timeout: 100,
    command: ["/bin/sh", "-c", "hostname -I"],
    attributeOnGateway: "IP address"
  },
  {
    timeout: 100,
    command: ["/bin/sh", "-c", "lsb_release -ds"],
    attributeOnGateway: "OS"
  },
  {
    timeout: 100,
    command: ["/bin/sh", "-c", "uptime"],
    attributeOnGateway: "Uptime"
  },
  {
    timeout: 100,
    command: ["/bin/sh", "-c", "lsusb"],
    attributeOnGateway: "USBs"
  }
]

// 配置预览
const previewConfig = computed(() => {
  return JSON.stringify(statisticsCommands.value, null, 2)
})

// 文件加载状态
const fileLoading = ref(false)
const fileSaving = ref(false)

// 加载统计命令文件
const loadStatisticsFile = async () => {
  if (!localConfig.value.configuration) {
    return
  }

  try {
    fileLoading.value = true
    const response = await getFileFromName(localConfig.value.configuration)
    
    if (response.data && Array.isArray(response.data)) {
      statisticsCommands.value = response.data
      // ElMessage.success(`已加载统计配置文件: ${localConfig.value.configuration}`)
    } else {
      // 如果文件不存在或格式不正确，使用默认命令
      ElMessage.warning(`配置文件 ${localConfig.value.configuration} 不存在或格式错误，已加载默认命令`)
      loadDefaultCommands()
    }
  } catch (error) {
    console.error('加载统计配置文件失败:', error)
    // 文件不存在时，使用默认命令
    ElMessage.warning(`配置文件 ${localConfig.value.configuration} 不存在，已加载默认命令`)
    loadDefaultCommands()
  } finally {
    fileLoading.value = false
  }
}

// 保存统计命令到文件
const saveStatisticsFile = async () => {
  if (!localConfig.value.configuration) {
    ElMessage.error('请先设置统计配置文件名')
    return
  }

  try {
    fileSaving.value = true
    
    const fileData = {
      file_name: localConfig.value.configuration,
      file_text: JSON.stringify(statisticsCommands.value, null, 2)
    }

    await updataFile(fileData)
    ElMessage.success(`统计配置已保存到文件: ${localConfig.value.configuration}`)
  } catch (error) {
    console.error('保存统计配置文件失败:', error)
    ElMessage.error('保存统计配置文件失败')
  } finally {
    fileSaving.value = false
  }
}

// 处理配置变化
const handleConfigChange = () => {
  // 当总开关关闭时，自动关闭自定义统计
  if (!localConfig.value.enable) {
    localConfig.value.enableCustom = false
  }
  
  emit('update:modelValue', localConfig.value)
  emit('change')
}

// 添加命令
const addCommand = () => {
  isEdit.value = false
  currentIndex.value = -1
  currentCommand.value = {
    timeout: 100,
    command: [],
    attributeOnGateway: ''
  }
  commandString.value = ''
  dialogVisible.value = true
}

// 编辑命令
const editCommand = (index) => {
  isEdit.value = true
  currentIndex.value = index
  const command = statisticsCommands.value[index]
  currentCommand.value = JSON.parse(JSON.stringify(command))
  commandString.value = formatCommand(command.command)
  dialogVisible.value = true
}

// 删除命令
const removeCommand = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个统计命令吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    statisticsCommands.value.splice(index, 1)
    ElMessage.success('命令删除成功')
    
    // 自动保存到文件
    await saveStatisticsFile()
  } catch {
    // 用户取消删除
  }
}

// 保存命令
const saveCommand = async () => {
  try {
    const valid = await commandFormRef.value?.validate()
    if (!valid) return

    if (!commandString.value.trim()) {
      ElMessage.error('请输入Shell命令')
      return
    }

    saving.value = true

    // 解析命令字符串为数组格式
    const command = ["/bin/sh", "-c", commandString.value.trim()]
    
    const commandData = {
      ...currentCommand.value,
      command
    }

    if (isEdit.value) {
      statisticsCommands.value[currentIndex.value] = commandData
      ElMessage.success('命令更新成功')
    } else {
      statisticsCommands.value.push(commandData)
      ElMessage.success('命令添加成功')
    }

    dialogVisible.value = false
    
    // 自动保存到文件
    await saveStatisticsFile()
  } catch (error) {
    console.error('保存命令失败:', error)
    ElMessage.error('保存命令失败')
  } finally {
    saving.value = false
  }
}

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false
}

// 复制配置
const copyConfig = async () => {
  try {
    await navigator.clipboard.writeText(previewConfig.value)
    ElMessage.success('配置已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下载配置文件
const downloadConfig = () => {
  const blob = new Blob([previewConfig.value], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = localConfig.value.configuration || 'statistics.json'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  ElMessage.success('配置文件下载成功')
}

// 初始化默认命令
const loadDefaultCommands = () => {
  statisticsCommands.value = JSON.parse(JSON.stringify(defaultCommands))
  ElMessage.success('已加载默认统计命令')
}

// 格式化命令显示
const formatCommand = (command) => {
  if (Array.isArray(command)) {
    return command.join(' ')
  }
  return command
}

// 手动保存配置文件
const manualSaveFile = async () => {
  await saveStatisticsFile()
}

// 手动加载配置文件
const manualLoadFile = async () => {
  await loadStatisticsFile()
}

// 处理文件名失焦事件
const handleFileNameBlur = async () => {
  if (localConfig.value.enable && localConfig.value.enableCustom && localConfig.value.configuration && statisticsCommands.value.length === 0) {
    try {
      await ElMessageBox.confirm(
        `是否要加载配置文件 ${localConfig.value.configuration} 中的统计命令？`,
        '加载配置文件',
        {
          confirmButtonText: '加载',
          cancelButtonText: '取消',
          type: 'info'
        }
      )
      await loadStatisticsFile()
    } catch {
      // 用户取消加载
    }
  }
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(localConfig.value, newValue)
  }
}, { immediate: true, deep: true })

// 监听配置文件名变化
watch(() => localConfig.value.configuration, (newFileName, oldFileName) => {
  if (newFileName && newFileName !== oldFileName && localConfig.value.enable && localConfig.value.enableCustom) {
    loadStatisticsFile()
  }
}, { immediate: true })

// 监听自定义统计启用状态变化
watch(() => localConfig.value.enableCustom, (newValue) => {
  if (newValue && localConfig.value.enable && localConfig.value.configuration && statisticsCommands.value.length === 0) {
    loadStatisticsFile()
  }
}, { immediate: true })

// 初始化时加载默认命令
nextTick(() => {
  if (statisticsCommands.value.length === 0 && !localConfig.value.configuration && localConfig.value.enable && localConfig.value.enableCustom) {
    loadDefaultCommands()
  }
})
</script>

<style lang="scss" scoped>
.gateway-statistics-config {
  .config-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    :deep(.el-card__header) {
      background-color: #fafafa;
      border-bottom: 1px solid #e4e7ed;
      padding: 16px 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }
    }

    :deep(.el-card__body) {
      padding: 20px;
    }
  }

  .config-content {
    .basic-statistics-section {
      margin-bottom: 20px;

      .form-label-with-switch {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        
        span {
          font-weight: 500;
          color: #606266;
        }
      }

      .basic-stats-info {
        margin-top: 16px;

        .stats-info-list {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 4px;
            color: #606266;
            font-size: 13px;
            line-height: 1.5;
          }
        }
      }
    }

    .form-item-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
      line-height: 1.4;
    }

    .file-extension {
      color: #909399;
      font-size: 12px;
    }

    .divider-text {
      font-weight: 600;
      color: #606266;
    }

    .statistics-commands {
      margin-top: 20px;

      .commands-container {
                 .section-header {
           display: flex;
           justify-content: space-between;
           align-items: center;
           margin-bottom: 12px;

           .header-left {
             display: flex;
             align-items: center;
             gap: 12px;

             h4 {
               margin: 0;
               font-size: 14px;
               font-weight: 600;
               color: #303133;
             }
           }
         }

        .section-description {
          color: #606266;
          font-size: 13px;
          margin-bottom: 16px;
          line-height: 1.5;
        }

        .commands-list {
          .command-card {
            margin-bottom: 12px;

            :deep(.el-card) {
              border: 1px solid #e4e7ed;
              transition: all 0.3s ease;

              &:hover {
                border-color: #409eff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
              }

              .el-card__header {
                background-color: #f8f9fa;
                padding: 12px 16px;

                .command-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;

                  .command-info {
                    flex: 1;

                    .command-name {
                      margin: 0 0 4px 0;
                      font-size: 14px;
                      font-weight: 600;
                      color: #303133;
                    }

                    .command-timeout {
                      font-size: 12px;
                      color: #909399;
                      background-color: #f0f9ff;
                      padding: 2px 6px;
                      border-radius: 10px;
                    }
                  }

                  .command-actions {
                    display: flex;
                    gap: 8px;
                  }
                }
              }

              .el-card__body {
                padding: 16px;
              }
            }
          }

          .command-details {
            .detail-item {
              display: flex;
              align-items: flex-start;
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .label {
                font-weight: 500;
                color: #606266;
                font-size: 13px;
                min-width: 60px;
                flex-shrink: 0;
              }

              .command-code {
                background-color: #f5f5f5;
                padding: 4px 8px;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                color: #e53e3e;
                word-break: break-all;
                flex: 1;
                margin-left: 8px;
              }
            }
          }

          .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #909399;
          }
        }
      }
    }

    .config-preview {
      margin-top: 20px;

      .preview-textarea {
        margin-bottom: 12px;

        :deep(.el-textarea__inner) {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          background-color: #f8f9fa;
        }
      }

      .preview-actions {
        display: flex;
        gap: 8px;
      }
    }

    .help-section {
      margin-top: 20px;

      .help-list {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 4px;
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-button--small) {
  padding: 4px 8px;
  font-size: 12px;
}

:deep(.el-divider__text) {
  background-color: #fff;
  padding: 0 16px;
}
</style> 