<template>
  <div class="gateway-storage-config">
    <el-form :model="config" :rules="rules" ref="formRef" label-width="140px">
      <!-- 存储类型选择 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>存储类型</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="存储类型" prop="type" required>
              <el-select 
                v-model="config.type" 
                placeholder="请选择存储类型"
                @change="handleTypeChange"
              >
                <el-option label="内存存储" value="memory" />
                <el-option label="文件存储" value="file" />
                <el-option label="SQLite数据库" value="sqlite" />
              </el-select>
              <div class="field-hint">选择网关数据的存储方式</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 内存存储配置 -->
      <el-card v-if="config.type === 'memory'" class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>内存存储配置</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="读取记录数量" prop="read_records_count" required>
              <el-input-number 
                v-model="config.read_records_count" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">每次从内存中读取的记录数量</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大记录数量" prop="max_records_count" required>
              <el-input-number 
                v-model="config.max_records_count" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">内存中最多保存的记录数量</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 文件存储配置 -->
      <el-card v-if="config.type === 'file'" class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>文件存储配置</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="数据文件夹路径" prop="data_folder_path" required>
              <el-input 
                v-model="config.data_folder_path" 
                placeholder="./data/"
                @change="handleChange"
              />
              <div class="field-hint">存储数据文件的文件夹路径</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="最大文件数量" prop="max_file_count" required>
              <el-input-number 
                v-model="config.max_file_count" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">最多保存的数据文件数量</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大读取记录数" prop="max_read_records_count" required>
              <el-input-number 
                v-model="config.max_read_records_count" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">每次最多读取的记录数量</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="每文件最大记录数" prop="max_records_per_file" required>
              <el-input-number 
                v-model="config.max_records_per_file" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">单个文件最多保存的记录数量</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- SQLite存储配置 -->
      <el-card v-if="config.type === 'sqlite'" class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>SQLite数据库配置</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="数据库文件路径" prop="data_file_path" required>
              <el-input 
                v-model="config.data_file_path" 
                placeholder="./data/data.db"
                @change="handleChange"
              />
              <div class="field-hint">SQLite数据库文件的完整路径</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="TTL检查周期(小时)" prop="messages_ttl_check_in_hours" required>
              <el-input-number 
                v-model="config.messages_ttl_check_in_hours" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">检查过期记录的时间间隔</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="记录保存天数" prop="messages_ttl_in_days" required>
              <el-input-number 
                v-model="config.messages_ttl_in_days" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">记录在数据库中的最长保存时间</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="每文件最大记录数" prop="max_records_per_file">
              <el-input-number 
                v-model="config.max_records_per_file" 
                :min="1"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">数据库文件的记录数量限制</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 存储类型说明 -->
      <el-alert 
        :title="getStorageTypeDescription()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const config = ref({
  type: 'memory',
  read_records_count: 100,
  max_records_count: 100000,
  data_folder_path: './data/',
  max_file_count: 10,
  max_read_records_count: 10,
  max_records_per_file: 10000,
  data_file_path: './data/data.db',
  messages_ttl_check_in_hours: 1,
  messages_ttl_in_days: 7
})

// 表单验证规则
const rules = ref({
  type: [
    { required: true, message: '请选择存储类型', trigger: 'change' }
  ],
  read_records_count: [
    { required: true, message: '请输入读取记录数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '读取记录数量不能小于1', trigger: 'blur' }
  ],
  max_records_count: [
    { required: true, message: '请输入最大记录数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '最大记录数量不能小于1', trigger: 'blur' }
  ],
  data_folder_path: [
    { required: true, message: '请输入数据文件夹路径', trigger: 'blur' }
  ],
  max_file_count: [
    { required: true, message: '请输入最大文件数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '最大文件数量不能小于1', trigger: 'blur' }
  ],
  max_read_records_count: [
    { required: true, message: '请输入最大读取记录数', trigger: 'blur' },
    { type: 'number', min: 1, message: '最大读取记录数不能小于1', trigger: 'blur' }
  ],
  max_records_per_file: [
    { required: true, message: '请输入每文件最大记录数', trigger: 'blur' },
    { type: 'number', min: 1, message: '每文件最大记录数不能小于1', trigger: 'blur' }
  ],
  data_file_path: [
    { required: true, message: '请输入数据库文件路径', trigger: 'blur' }
  ],
  messages_ttl_check_in_hours: [
    { required: true, message: '请输入TTL检查周期', trigger: 'blur' },
    { type: 'number', min: 1, message: 'TTL检查周期不能小于1小时', trigger: 'blur' }
  ],
  messages_ttl_in_days: [
    { required: true, message: '请输入记录保存天数', trigger: 'blur' },
    { type: 'number', min: 1, message: '记录保存天数不能小于1天', trigger: 'blur' }
  ]
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(config.value, newValue)
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
  emit('change', { ...newConfig })
}, { deep: true })

// 处理存储类型变化
const handleTypeChange = () => {
  // 清除验证错误
  formRef.value?.clearValidate()
  handleChange()
}

// 处理配置变化
const handleChange = () => {
  // 触发验证
  const fieldsToValidate = getValidationFields()
  formRef.value?.validateField(fieldsToValidate)
}

// 获取需要验证的字段
const getValidationFields = () => {
  const baseFields = ['type']
  
  switch (config.value.type) {
    case 'memory':
      return [...baseFields, 'read_records_count', 'max_records_count']
    case 'file':
      return [...baseFields, 'data_folder_path', 'max_file_count', 'max_read_records_count', 'max_records_per_file']
    case 'sqlite':
      return [...baseFields, 'data_file_path', 'messages_ttl_check_in_hours', 'messages_ttl_in_days']
    default:
      return baseFields
  }
}

// 获取存储类型说明
const getStorageTypeDescription = () => {
  const descriptions = {
    memory: '内存存储：数据保存在内存中，读写速度最快，但重启后数据会丢失。适用于对性能要求高且可以接受数据丢失的场景。',
    file: '文件存储：数据保存在本地文件中，具有持久性，重启后数据不会丢失。适用于需要数据持久化但对性能要求不是特别高的场景。',
    sqlite: 'SQLite数据库：数据保存在SQLite数据库中，提供完整的数据库功能，支持复杂查询和事务。适用于需要复杂数据操作和高可靠性的场景。'
  }
  return descriptions[config.value.type] || ''
}

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.gateway-storage-config {
  .config-card {
    margin-bottom: 24px;
    border: 1px solid #e4e7ed;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-header {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
  
  :deep(.el-card__body) {
    padding: 24px;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
  
  :deep(.el-alert) {
    margin-top: 16px;
  }
}
</style> 