{"host": "http://127.0.0.1:5000", "SSLVerify": true, "security": {"type": "basic", "username": "user", "password": "password"}, "mapping": [{"url": "getdata", "httpMethod": "GET", "httpHeaders": {"ACCEPT": "application/json"}, "content": {"name": "morpheus", "job": "leader"}, "allowRedirects": true, "timeout": 0.5, "scanPeriod": 5, "converter": {"type": "json", "deviceNameJsonExpression": "SD8500", "deviceTypeJsonExpression": "SD", "attributes": [{"key": "serialNumber", "type": "string", "value": "${serial}"}], "telemetry": [{"key": "Maintainer", "type": "string", "value": "${Dev<PERSON><PERSON>}"}, {"key": "combine", "type": "string", "value": "${Develo<PERSON>}:${hum}"}]}}, {"url": "get_info", "httpMethod": "GET", "httpHeaders": {"ACCEPT": "application/json"}, "allowRedirects": true, "timeout": 0.5, "scanPeriod": 100, "converter": {"type": "custom", "deviceNameJsonExpression": "SD8500", "deviceTypeJsonExpression": "SD", "extension": "CustomRequestUplinkConverter", "extension-config": [{"key": "Totaliser", "type": "float", "fromByte": 0, "toByte": 4, "byteorder": "big", "signed": true, "multiplier": 1}, {"key": "Flow", "type": "int", "fromByte": 4, "toByte": 6, "byteorder": "big", "signed": true, "multiplier": 0.01}, {"key": "Temperature", "type": "int", "fromByte": 8, "toByte": 10, "byteorder": "big", "signed": true, "multiplier": 0.01}, {"key": "Pressure", "type": "int", "fromByte": 12, "toByte": 14, "byteorder": "big", "signed": true, "multiplier": 0.01}, {"key": "deviceStatus", "type": "int", "byteAddress": 15, "fromBit": 4, "toBit": 8, "byteorder": "big", "signed": false}, {"key": "OUT2", "type": "int", "byteAddress": 15, "fromBit": 1, "toBit": 2, "byteorder": "big"}, {"key": "OUT1", "type": "int", "byteAddress": 15, "fromBit": 0, "toBit": 1, "byteorder": "big"}]}}], "attributeUpdates": [{"httpMethod": "POST", "httpHeaders": {"CONTENT-TYPE": "application/json"}, "timeout": 0.5, "tries": 3, "allowRedirects": true, "deviceNameFilter": "SD.*", "attributeFilter": "send_data", "requestUrlExpression": "sensor/${deviceName}/${attributeKey}", "requestValueExpression": "{\"${attributeKey}\":\"${attributeValue}\"}"}], "serverSideRpc": [{"deviceNameFilter": ".*", "methodFilter": "echo", "requestUrlExpression": "sensor/${deviceName}/request/${methodName}/${requestId}", "responseTimeout": 1, "httpMethod": "GET", "requestValueExpression": "${params}", "responseValueExpression": "${temp}", "timeout": 0.5, "tries": 3, "httpHeaders": {"Content-Type": "application/json"}}, {"deviceNameFilter": ".*", "methodFilter": "no-reply", "requestUrlExpression": "sensor/${deviceName}/request/${methodName}/${requestId}", "httpMethod": "POST", "requestValueExpression": "${params}", "httpHeaders": {"Content-Type": "application/json"}}]}