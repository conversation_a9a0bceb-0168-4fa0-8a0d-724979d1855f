<template>
  <div class="odbc-connection-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>数据库连接配置</span>
          <el-tooltip content="配置ODBC数据库连接参数和编码设置" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="connectionConfig" :rules="rules" label-width="140px" ref="formRef">
        <!-- 连接字符串配置 -->
        <div class="connection-section">
          <div class="section-title">连接字符串</div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="连接字符串" prop="str" required>
                <el-input 
                  v-model="connectionConfig.str" 
                  type="textarea" 
                  :rows="3"
                  placeholder="Driver={PostgreSQL};Server=localhost;Port=5432;Database=thingsboard;Uid=********;Pwd=********;"
                  
                />
                <div class="field-hint">ODBC连接字符串，包含驱动、服务器、数据库等信息</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 连接属性配置 -->
        <div class="connection-section">
          <div class="section-title">连接属性</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="自动提交">
                <el-switch v-model="connectionConfig.attributes.autocommit"  />
                <div class="field-hint">是否启用事务自动提交</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="超时时间 (秒)" prop="attributes.timeout">
                <el-input-number 
                  v-model="connectionConfig.attributes.timeout" 
                  :min="0" 
                  :precision="0"
                  controls-position="right"
                  
                />
                <div class="field-hint">连接超时时间，0表示无限制</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="搜索转义符" prop="attributes.searchescape">
                <el-input 
                  v-model="connectionConfig.attributes.searchescape" 
                  placeholder="\"
                  
                />
                <div class="field-hint">SQL LIKE模式的转义字符</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 编码配置 -->
        <div class="connection-section">
          <div class="section-title">编码配置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="字符编码" prop="encoding">
                <el-select v-model="connectionConfig.encoding" placeholder="请选择字符编码" >
                  <el-option v-for="encoding in encodingOptions" :key="encoding" :label="encoding" :value="encoding" />
                </el-select>
                <div class="field-hint">数据传输使用的字符编码</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <div class="decoding-section">
            <div class="subsection-title">解码配置</div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="单字符解码" prop="decoding.char">
                  <el-select v-model="connectionConfig.decoding.char" placeholder="请选择解码格式" >
                    <el-option v-for="encoding in encodingOptions" :key="encoding" :label="encoding" :value="encoding" />
                  </el-select>
                  <div class="field-hint">单字节字符解码格式</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="宽字符解码" prop="decoding.wchar">
                  <el-select v-model="connectionConfig.decoding.wchar" placeholder="请选择解码格式" >
                    <el-option v-for="encoding in encodingOptions" :key="encoding" :label="encoding" :value="encoding" />
                  </el-select>
                  <div class="field-hint">宽字符（Unicode）解码格式</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="元数据解码" prop="decoding.metadata">
                  <el-select v-model="connectionConfig.decoding.metadata" placeholder="请选择解码格式" >
                    <el-option v-for="encoding in metadataEncodingOptions" :key="encoding" :label="encoding" :value="encoding" />
                  </el-select>
                  <div class="field-hint">数据库元数据解码格式</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 重连配置 -->
        <div class="connection-section">
          <div class="section-title">重连配置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="启用自动重连">
                <el-switch v-model="connectionConfig.reconnect"  />
                <div class="field-hint">连接断开时是否自动重连</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重连周期 (秒)" prop="reconnectPeriod" v-if="connectionConfig.reconnect">
                <el-input-number 
                  v-model="connectionConfig.reconnectPeriod" 
                  :min="1" 
                  :precision="0"
                  controls-position="right"
                  
                />
                <div class="field-hint">自动重连的时间间隔</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 连接测试 -->
        <div class="test-section">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-button 
                type="primary" 
                :loading="testing" 
                @click="testConnection"
                :disabled="!isConfigValid"
              >
                <el-icon><Connection /></el-icon>
                测试连接
              </el-button>
              <span class="test-hint">点击测试数据库连接是否正常</span>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { QuestionFilled, Connection } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      str: 'Driver={PostgreSQL};Server=localhost;Port=5432;Database=thingsboard;Uid=********;Pwd=********;',
      attributes: {
        autocommit: true,
        timeout: 0
      },
      encoding: 'utf-8',
      decoding: {
        char: 'utf-8',
        wchar: 'utf-8',
        metadata: 'utf-16le'
      },
      reconnect: true,
      reconnectPeriod: 60
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const testing = ref(false)

// 连接配置数据
const connectionConfig = reactive({
  str: 'Driver={PostgreSQL};Server=localhost;Port=5432;Database=thingsboard;Uid=********;Pwd=********;',
  attributes: {
    autocommit: true,
    timeout: 0
  },
  encoding: 'utf-8',
  decoding: {
    char: 'utf-8',
    wchar: 'utf-8',
    metadata: 'utf-16le'
  },
  reconnect: true,
  reconnectPeriod: 60
})

// 编码选项
const encodingOptions = [
  'ascii',
  'utf-8',
  'utf-16',
  'gbk',
  'gb2312',
  'gb18030',
  'big5',
  'iso-8859-1',
  'windows-1252'
]

const metadataEncodingOptions = [
  'utf-8',
  'utf-16',
  'utf-16le',
  'utf-16be',
  'utf-32',
  'utf-32le',
  'utf-32be'
]

// 表单验证规则
const rules = reactive({
  str: [
    { required: true, message: '请输入数据库连接字符串', trigger: 'blur' }
  ],
  'attributes.timeout': [
    { type: 'number', min: 0, message: '超时时间不能为负数', trigger: 'blur' }
  ],
  reconnectPeriod: [
    { type: 'number', min: 1, message: '重连周期必须大于0', trigger: 'blur' }
  ]
})

// 检查配置是否有效
const isConfigValid = computed(() => {
  return !!(connectionConfig.str && connectionConfig.str.trim())
})

// 测试连接
const testConnection = async () => {
  try {
    await formRef.value?.validate()
    testing.value = true
    
    // 模拟连接测试
    setTimeout(() => {
      testing.value = false
      ElMessage.success('数据库连接测试成功！')
    }, 2000)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查配置参数')
  }
}

// 处理配置变化
const handleChange = () => {
  emit('update:modelValue', { ...connectionConfig })
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(connectionConfig, {
      str: 'Driver={PostgreSQL};Server=localhost;Port=5432;Database=thingsboard;Uid=********;Pwd=********;',
      attributes: {
        autocommit: true,
        timeout: 0
      },
      encoding: 'utf-8',
      decoding: {
        char: 'utf-8',
        wchar: 'utf-8',
        metadata: 'utf-16le'
      },
      reconnect: true,
      reconnectPeriod: 60,
      ...newValue
    })
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(connectionConfig, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })
</script>

<style lang="scss" scoped>
.odbc-connection-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .connection-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f8f9fa;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .subsection-title {
      font-size: 13px;
      font-weight: 500;
      color: #606266;
      margin: 16px 0 12px 0;
      padding-bottom: 6px;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  .decoding-section {
    margin-top: 16px;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background: #fafafa;
  }

  .test-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f0f9ff;
    
    .test-hint {
      margin-left: 12px;
      font-size: 12px;
      color: #909399;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-textarea) {
    .el-textarea__inner {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
    }
  }
}
</style> 