<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑设备映射' : '添加设备映射'"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="mappingForm" :rules="rules" ref="formRef" label-width="160px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="订阅主题" prop="topicFilter" required>
                <el-input 
                  v-model="mappingForm.topicFilter" 
                  placeholder="sensor/data"
                />
                <div class="field-hint">MQTT订阅主题，支持通配符：+ 和 #</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="QoS级别" prop="subscriptionQos">
                <el-select 
                  v-model="mappingForm.subscriptionQos" 
                  placeholder="请选择QoS级别"
                  style="width: 100%"
                >
                  <el-option label="QoS 0 (最多一次)" :value="0" />
                  <el-option label="QoS 1 (至少一次)" :value="1" />
                  <el-option label="QoS 2 (恰好一次)" :value="2" />
                </el-select>
                <div class="field-hint">MQTT消息质量服务等级</div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 转换器配置 -->
          <div class="config-section">
            <h4 class="section-title">转换器配置</h4>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="转换器类型" prop="converter.type" required>
                  <el-select 
                    v-model="mappingForm.converter.type" 
                    placeholder="请选择转换器类型"
                    style="width: 100%"
                    @change="onConverterTypeChange"
                  >
                    <el-option label="JSON转换器" value="json" />
                    <el-option label="字节转换器" value="bytes" />
                    <el-option label="自定义转换器" value="custom" />
                  </el-select>
                  <div class="field-hint">选择数据转换器类型</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="只在变化时发送">
                  <el-switch v-model="mappingForm.converter.sendDataOnlyOnChange" />
                  <div class="field-hint">仅当数据发生变化时才发送</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="超时时间(ms)">
                  <el-input-number 
                    v-model="mappingForm.converter.timeout" 
                    :min="1000" 
                    :step="1000"
                    style="width: 100%"
                  />
                  <div class="field-hint">转换器处理超时时间</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 设备信息配置 -->
          <div class="config-section" v-if="mappingForm.converter.type !== 'custom'">
            <h4 class="section-title">设备信息配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备名称来源" prop="converter.deviceInfo.deviceNameExpressionSource">
                  <el-select 
                    v-model="mappingForm.converter.deviceInfo.deviceNameExpressionSource" 
                    placeholder="请选择设备名称数据源"
                    style="width: 100%"
                  >
                    <el-option label="消息内容" value="message" />
                    <el-option label="主题" value="topic" />
                    <el-option label="常量" value="constant" />
                  </el-select>
                  <div class="field-hint">设备名称的数据来源</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备名称表达式" prop="converter.deviceInfo.deviceNameExpression" required>
                  <el-input 
                    v-model="mappingForm.converter.deviceInfo.deviceNameExpression" 
                    :placeholder="getDeviceNamePlaceholder()"
                  />
                  <div class="field-hint">设备名称提取表达式</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备类型来源" prop="converter.deviceInfo.deviceProfileExpressionSource">
                  <el-select 
                    v-model="mappingForm.converter.deviceInfo.deviceProfileExpressionSource" 
                    placeholder="请选择设备类型数据源"
                    style="width: 100%"
                  >
                    <el-option label="消息内容" value="message" />
                    <el-option label="主题" value="topic" />
                    <el-option label="常量" value="constant" />
                  </el-select>
                  <div class="field-hint">设备类型的数据来源</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备类型表达式" prop="converter.deviceInfo.deviceProfileExpression">
                  <el-input 
                    v-model="mappingForm.converter.deviceInfo.deviceProfileExpression" 
                    :placeholder="getDeviceTypePlaceholder()"
                  />
                  <div class="field-hint">设备类型提取表达式</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 自定义转换器配置 -->
          <div class="config-section" v-if="mappingForm.converter.type === 'custom'">
            <h4 class="section-title">自定义转换器配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="扩展名" prop="converter.extension" required>
                  <el-input 
                    v-model="mappingForm.converter.extension" 
                    placeholder="CustomMqttUplinkConverter"
                  />
                  <div class="field-hint">自定义转换器类名</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="启用缓存">
                  <el-switch v-model="mappingForm.converter.cached" />
                  <div class="field-hint">是否缓存转换器实例</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes" v-if="mappingForm.converter.type !== 'custom'">
          <MqttDataKeys
            v-model="mappingForm.converter.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从MQTT消息中提取的属性数据"
            :converter-type="mappingForm.converter.type"
          />
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="timeseries" v-if="mappingForm.converter.type !== 'custom'">
          <MqttDataKeys
            v-model="mappingForm.converter.timeseries"
            data-type="timeseries"
            title="遥测数据配置"
            description="配置从MQTT消息中提取的遥测数据"
            :converter-type="mappingForm.converter.type"
          />
        </el-tab-pane>

        <!-- 扩展配置 -->
        <el-tab-pane label="扩展配置" name="extension" v-if="mappingForm.converter.type === 'custom'">
          <MqttExtensionConfig
            v-model="mappingForm.converter.extensionConfig"
          />
        </el-tab-pane>

        <!-- 请求映射配置 -->
        <el-tab-pane label="请求映射" name="requests">
          <MqttRequestMapping
            v-model="mappingForm.requestsMapping"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import MqttDataKeys from './MqttDataKeys.vue'
import MqttExtensionConfig from './MqttExtensionConfig.vue'
import MqttRequestMapping from './MqttRequestMapping.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mapping: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const activeTab = ref('basic')
const saving = ref(false)

// 映射表单数据
const mappingForm = reactive({
  topicFilter: 'sensor/data',
  subscriptionQos: 1,
  converter: {
    type: 'json',
    deviceInfo: {
      deviceNameExpressionSource: 'message',
      deviceNameExpression: '${serialNumber}',
      deviceProfileExpressionSource: 'constant',
      deviceProfileExpression: 'default'
    },
    sendDataOnlyOnChange: false,
    timeout: 60000,
    attributes: [],
    timeseries: [],
    extension: 'CustomMqttUplinkConverter',
    cached: true,
    extensionConfig: {}
  },
  requestsMapping: {
    connectRequests: [],
    disconnectRequests: [],
    attributeRequests: [],
    attributeUpdates: [],
    serverSideRpc: []
  }
})

// 表单验证规则
const rules = reactive({
  topicFilter: [
    { required: true, message: '请输入订阅主题', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  'converter.type': [
    { required: true, message: '请选择转换器类型', trigger: 'change' }
  ],
  'converter.deviceInfo.deviceNameExpression': [
    { required: true, message: '请输入设备名称表达式', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  'converter.extension': [
    { required: true, message: '请输入扩展名', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ]
})

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
})

// 监听dialogVisible变化
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 监听mapping变化
watch(() => props.mapping, (newMapping) => {
  if (newMapping) {
    Object.assign(mappingForm, {
      topicFilter: 'sensor/data',
      subscriptionQos: 1,
      converter: {
        type: 'json',
        deviceInfo: {
          deviceNameExpressionSource: 'message',
          deviceNameExpression: '${serialNumber}',
          deviceProfileExpressionSource: 'constant',
          deviceProfileExpression: 'default'
        },
        sendDataOnlyOnChange: false,
        timeout: 60000,
        attributes: [],
        timeseries: [],
        extension: 'CustomMqttUplinkConverter',
        cached: true,
        extensionConfig: {}
      },
      requestsMapping: {
        connectRequests: [],
        disconnectRequests: [],
        attributeRequests: [],
        attributeUpdates: [],
        serverSideRpc: []
      },
      ...newMapping
    })
  }
}, { immediate: true, deep: true })

// 获取设备名称占位符
const getDeviceNamePlaceholder = () => {
  const source = mappingForm.converter.deviceInfo.deviceNameExpressionSource
  const placeholders = {
    message: '${serialNumber}',
    topic: '(?<=sensor/)(.*?)(?=/data)',
    constant: 'DeviceName'
  }
  return placeholders[source] || '${serialNumber}'
}

// 获取设备类型占位符
const getDeviceTypePlaceholder = () => {
  const source = mappingForm.converter.deviceInfo.deviceProfileExpressionSource
  const placeholders = {
    message: '${sensorType}',
    topic: '(?<=type/)(.*?)(?=/)',
    constant: 'default'
  }
  return placeholders[source] || 'default'
}

// 转换器类型变化处理
const onConverterTypeChange = () => {
  // 重置相关配置
  if (mappingForm.converter.type === 'custom') {
    mappingForm.converter.extension = 'CustomMqttUplinkConverter'
    mappingForm.converter.cached = true
    mappingForm.converter.extensionConfig = {}
  } else {
    mappingForm.converter.deviceInfo = {
      deviceNameExpressionSource: 'message',
      deviceNameExpression: '${serialNumber}',
      deviceProfileExpressionSource: 'constant',
      deviceProfileExpression: 'default'
    }
    mappingForm.converter.attributes = []
    mappingForm.converter.timeseries = []
  }
}

// 关闭对话框
const handleClose = () => {
  emit('cancel')
}

// 保存配置
const handleSave = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    saving.value = true
    
    // 深拷贝表单数据
    const mappingData = JSON.parse(JSON.stringify(mappingForm))
    
    emit('save', mappingData)
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}
</script>

<style lang="scss" scoped>
.config-section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #409eff;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #409eff;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}

:deep(.el-tabs__content) {
  padding: 20px;
}
</style> 