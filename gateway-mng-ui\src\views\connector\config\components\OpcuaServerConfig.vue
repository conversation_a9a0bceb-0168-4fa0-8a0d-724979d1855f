<template>
  <div class="opcua-server-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>服务器配置</span>
          <el-tooltip content="OPC-UA服务器连接和通信配置" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="serverConfig" :rules="rules" label-width="160px" ref="formRef">
        <!-- 基础连接配置 -->
        <div class="config-section">
          <h4 class="section-title">连接配置</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="服务器地址" prop="url" required>
                <el-input 
                  v-model="serverConfig.url" 
                  placeholder="localhost:4840/freeopcua/server/"
                />
                <div class="field-hint">OPC-UA服务器的完整URL地址</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="连接超时" prop="timeoutInMillis" required>
                <el-input-number 
                  v-model.number="serverConfig.timeoutInMillis" 
                  :min="1000" 
                  :step="100"
                  controls-position="right"
                  placeholder="5000"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">连接超时时间 (≥1000ms)</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="扫描周期" prop="scanPeriodInMillis" required>
                <el-input-number 
                  v-model.number="serverConfig.scanPeriodInMillis" 
                  :min="1000" 
                  :step="1000"
                  controls-position="right"
                  placeholder="3600000"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">设备扫描周期 (≥1000ms)</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="轮询周期" prop="pollPeriodInMillis" required>
                <el-input-number 
                  v-model.number="serverConfig.pollPeriodInMillis" 
                  :min="50" 
                  :step="50"
                  controls-position="right"
                  placeholder="5000"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">数据轮询周期 (≥50ms)</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订阅检查周期" prop="subCheckPeriodInMillis" required>
                <el-input-number 
                  v-model.number="serverConfig.subCheckPeriodInMillis" 
                  :min="100" 
                  :step="10"
                  controls-position="right"
                  placeholder="100"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">订阅状态检查周期 (≥100ms)</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 订阅和显示配置 -->
        <div class="config-section">
          <h4 class="section-title">功能配置</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="启用订阅">
                <el-switch 
                  v-model="serverConfig.enableSubscriptions"
                />
                <div class="field-hint">启用OPC-UA订阅功能以获得实时数据更新</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="显示映射">
                <el-switch 
                  v-model="serverConfig.showMap"
                />
                <div class="field-hint">在日志中显示节点映射信息</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 安全配置 -->
        <div class="config-section">
          <h4 class="section-title">安全配置</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="安全策略" prop="security" required>
                <el-select 
                  v-model="serverConfig.security" 
                  placeholder="请选择安全策略"
                >
                  <el-option label="Basic128Rsa15" value="Basic128Rsa15" />
                  <el-option label="Basic256" value="Basic256" />
                  <el-option label="Basic256Sha256" value="Basic256Sha256" />
                  <el-option label="None" value="None" />
                </el-select>
                <div class="field-hint">OPC-UA安全策略类型</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份验证类型" prop="identity.type" required>
                <el-select 
                  v-model="serverConfig.identity.type" 
                  placeholder="请选择身份验证类型"
                  @change="handleIdentityTypeChange"
                >
                  <el-option label="匿名访问" value="anonymous" />
                  <el-option label="用户名密码" value="username" />
                  <el-option label="证书认证" value="cert.PEM" />
                </el-select>
                <div class="field-hint">客户端身份验证方式</div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 用户名密码认证 -->
          <template v-if="serverConfig.identity.type === 'username'">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户名" prop="identity.username" required>
                  <el-input 
                    v-model="serverConfig.identity.username" 
                    placeholder="请输入用户名"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="密码" prop="identity.password" required>
                  <el-input 
                    v-model="serverConfig.identity.password" 
                    type="password"
                    placeholder="请输入密码"
                    show-password
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 证书认证 -->
          <template v-if="serverConfig.identity.type === 'cert.PEM'">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="CA证书路径" prop="identity.caCert">
                  <el-input 
                    v-model="serverConfig.identity.caCert" 
                    placeholder="/path/to/ca.pem"
                  />
                  <div class="field-hint">CA根证书文件路径</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户端证书路径" prop="identity.cert">
                  <el-input 
                    v-model="serverConfig.identity.cert" 
                    placeholder="/path/to/client.pem"
                  />
                  <div class="field-hint">客户端证书文件路径</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="私钥路径" prop="identity.privateKey">
                  <el-input 
                    v-model="serverConfig.identity.privateKey" 
                    placeholder="/path/to/private.key"
                  />
                  <div class="field-hint">客户端私钥文件路径</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="安全模式" prop="identity.mode">
                  <el-select 
                    v-model="serverConfig.identity.mode" 
                    placeholder="请选择安全模式"
                  >
                    <el-option label="Sign" value="Sign" />
                    <el-option label="SignAndEncrypt" value="SignAndEncrypt" />
                  </el-select>
                  <div class="field-hint">证书安全模式</div>
                </el-form-item>
              </el-col>
            </el-row>
            
            <!-- SignAndEncrypt模式需要用户名密码 -->
            <template v-if="serverConfig.identity.mode === 'SignAndEncrypt'">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="用户名" prop="identity.username" required>
                    <el-input 
                      v-model="serverConfig.identity.username" 
                      placeholder="请输入用户名"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码" prop="identity.password" required>
                    <el-input 
                      v-model="serverConfig.identity.password" 
                      type="password"
                      placeholder="请输入密码"
                      show-password
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
          </template>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick, defineEmits, defineProps } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      url: 'localhost:4840/freeopcua/server/',
      timeoutInMillis: 5000,
      scanPeriodInMillis: 3600000,
      pollPeriodInMillis: 5000,
      enableSubscriptions: true,
      subCheckPeriodInMillis: 100,
      showMap: false,
      security: 'Basic128Rsa15',
      identity: {
        type: 'anonymous'
      }
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()

// 服务器配置数据
const serverConfig = reactive({
  url: 'localhost:4840/freeopcua/server/',
  timeoutInMillis: 5000,
  scanPeriodInMillis: 3600000,
  pollPeriodInMillis: 5000,
  enableSubscriptions: true,
  subCheckPeriodInMillis: 100,
  showMap: false,
  security: 'Basic128Rsa15',
  identity: {
    type: 'anonymous'
  },
  ...props.modelValue
})

// 表单验证规则
const rules = reactive({
  url: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  timeoutInMillis: [
    { required: true, message: '请输入连接超时时间', trigger: 'blur' },
    { type: 'number', min: 1000, message: '超时时间不能小于1000毫秒', trigger: 'blur' }
  ],
  scanPeriodInMillis: [
    { required: true, message: '请输入扫描周期', trigger: 'blur' },
    { type: 'number', min: 1000, message: '扫描周期不能小于1000毫秒', trigger: 'blur' }
  ],
  pollPeriodInMillis: [
    { required: true, message: '请输入轮询周期', trigger: 'blur' },
    { type: 'number', min: 50, message: '轮询周期不能小于50毫秒', trigger: 'blur' }
  ],
  subCheckPeriodInMillis: [
    { required: true, message: '请输入订阅检查周期', trigger: 'blur' },
    { type: 'number', min: 100, message: '订阅检查周期不能小于100毫秒', trigger: 'blur' }
  ],
  security: [
    { required: true, message: '请选择安全策略', trigger: 'change' }
  ],
  'identity.type': [
    { required: true, message: '请选择身份验证类型', trigger: 'change' }
  ],
  'identity.username': [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  'identity.password': [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ]
})

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value) {
    Object.assign(serverConfig, newValue)
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(serverConfig, (newConfig) => {
  isInternalUpdate.value = true
  emit('update:modelValue', { ...newConfig })
  // 在下一个tick重置标志
  nextTick(() => {
    isInternalUpdate.value = false
  })
}, { deep: true })

// 处理身份验证类型变化
const handleIdentityTypeChange = () => {
  // 清理不相关的字段
  if (serverConfig.identity.type === 'anonymous') {
    delete serverConfig.identity.username
    delete serverConfig.identity.password
    delete serverConfig.identity.caCert
    delete serverConfig.identity.cert
    delete serverConfig.identity.privateKey
    delete serverConfig.identity.mode
  } else if (serverConfig.identity.type === 'username') {
    delete serverConfig.identity.caCert
    delete serverConfig.identity.cert
    delete serverConfig.identity.privateKey
    delete serverConfig.identity.mode
  } else if (serverConfig.identity.type === 'cert.PEM') {
    // 证书模式保留所有字段
  }
}

// 验证表单
const validate = () => {
  return formRef.value?.validate()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

defineExpose({
  validate,
  resetForm
})
</script>

<style lang="scss" scoped>
.opcua-server-config {
  .config-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
      
      .el-icon {
        margin-left: 8px;
        color: #909399;
        cursor: help;
      }
    }
  }
  
  .config-section {
    margin-bottom: 30px;
    
    .section-title {
      margin: 0 0 20px 0;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .unit-suffix {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 24px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
    
    .el-input-number {
      width: 100%;
    }
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
}
</style> 