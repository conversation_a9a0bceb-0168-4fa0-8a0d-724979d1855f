<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="dataPointForm" :rules="rules" ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="键名" prop="key" required>
            <el-input 
              v-model="dataPointForm.key" 
              :placeholder="dataType === 'attributes' ? 'model' : 'temperature'"
            />
            <div class="field-hint">数据点的键名标识</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="值表达式" prop="value" required>
            <el-input 
              v-model="dataPointForm.value" 
              :placeholder="dataType === 'attributes' ? '${sensorModel}' : '${temp}'"
            />
            <div class="field-hint">用于提取值的表达式</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数据类型" prop="type" required>
            <el-select v-model="dataPointForm.type" placeholder="请选择数据类型">
              <el-option label="字符串 (string)" value="string" />
              <el-option label="整数 (int)" value="int" />
              <el-option label="长整数 (long)" value="long" />
              <el-option label="浮点数 (float)" value="float" />
              <el-option label="双精度 (double)" value="double" />
              <el-option label="布尔值 (bool)" value="bool" />
              <el-option label="原始数据 (raw)" value="raw" />
            </el-select>
            <div class="field-hint">{{ getDataTypeHint() }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- MQTT表达式语法说明 -->
      <div class="expression-help">
        <el-alert
          title="MQTT表达式语法说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>表达式格式:</strong></p>
            <ul>
              <li><strong>消息字段引用</strong>: ${fieldName} - 引用MQTT消息中的字段</li>
              <li><strong>动态键名</strong>: ${variableName} - 使用变量作为键名</li>
              <li><strong>字节数据切片</strong>: [start:end] - 用于bytes转换器的数据切片</li>
              <li><strong>组合表达式</strong>: ${field1}:${field2} - 组合多个字段值</li>
            </ul>
            
            <p><strong>示例配置:</strong></p>
            <ul>
              <li>温度传感器: key="temperature", value="${temp}", type="double"</li>
              <li>湿度传感器: key="humidity", value="${hum}", type="string"</li>
              <li>设备型号: key="model", value="${sensorModel}", type="string"</li>
              <li>组合数据: key="combine", value="${hum}:${temp}", type="string"</li>
              <li>原始数据: key="rawData", value="[:]", type="raw"</li>
              <li>数据切片: key="temp", value="[4:]", type="raw"</li>
            </ul>
            
            <p><strong>数据类型说明:</strong></p>
            <ul>
              <li><strong>string</strong> - 文本数据</li>
              <li><strong>int/long</strong> - 整数数据</li>
              <li><strong>float/double</strong> - 浮点数数据</li>
              <li><strong>bool</strong> - 布尔值 (true/false)</li>
              <li><strong>raw</strong> - 原始字节数据，支持切片操作</li>
            </ul>
          </div>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dataPoint: {
    type: Object,
    default: null
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries'].includes(value)
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 数据点表单数据
const dataPointForm = reactive({
  key: '',
  value: '',
  type: ''
})

// 获取对话框标题
const getDialogTitle = () => {
  const action = props.isEdit ? '编辑' : '添加'
  const type = props.dataType === 'attributes' ? '属性' : '遥测'
  return `${action}${type}数据点`
}

// 获取数据类型提示
const getDataTypeHint = () => {
  if (props.dataType === 'attributes') {
    return 'MQTT设备属性的数据类型'
  } else {
    return 'MQTT设备遥测数据的类型'
  }
}

// 表单验证规则
const rules = reactive({
  key: [
    { required: true, message: '请输入键名', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入值表达式', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.dataPoint) {
    // 加载数据点数据
    const defaultKey = props.dataType === 'attributes' ? 'model' : 'temperature'
    const defaultValue = props.dataType === 'attributes' ? '${sensorModel}' : '${temp}'
    const defaultType = props.dataType === 'attributes' ? 'string' : 'double'
    
    Object.assign(dataPointForm, {
      key: defaultKey,
      value: defaultValue,
      type: defaultType,
      ...props.dataPoint
    })
  } else if (newValue) {
    // 重置为默认值
    const defaultKey = props.dataType === 'attributes' ? 'model' : 'temperature'
    const defaultValue = props.dataType === 'attributes' ? '${sensorModel}' : '${temp}'
    const defaultType = props.dataType === 'attributes' ? 'string' : 'double'
    
    Object.assign(dataPointForm, {
      key: defaultKey,
      value: defaultValue,
      type: defaultType
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...dataPointForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '数据点更新成功' : '数据点添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.expression-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style> 