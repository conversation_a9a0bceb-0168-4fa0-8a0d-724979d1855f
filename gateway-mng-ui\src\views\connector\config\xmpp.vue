<template>
  <div class="xmpp-config">
    <el-tabs v-model="activeTab" class="xmpp-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- XMPP服务器配置标签页 -->
      <el-tab-pane label="服务器配置" name="server">
        <XmppServerConfig 
          v-model="config.server" 
        />
      </el-tab-pane>

      <!-- 设备配置标签页 -->
      <el-tab-pane label="设备配置" name="devices">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>XMPP设备配置管理</span>
              <el-button type="primary" size="small" @click="handleAddDevice">
                <el-icon><Plus /></el-icon>添加设备
              </el-button>
            </div>
          </template>
          
          <!-- 设备列表 -->
          <div v-if="config.devices.length === 0" class="empty-state">
            <el-empty description="暂无设备配置">
              <el-button type="primary" @click="handleAddDevice">添加第一个设备</el-button>
            </el-empty>
          </div>
          
          <div v-else class="devices-grid">
            <div 
              v-for="(device, index) in config.devices" 
              :key="index" 
              class="device-card"
            >
              <el-card shadow="hover">
                <template #header>
                  <div class="device-header">
                    <div class="device-info">
                      <h4 class="device-name">{{ device.jid || 'Unknown Device' }}</h4>
                      <span class="device-type">{{ device.deviceTypeExpression || 'default' }}</span>
                    </div>
                    <div class="device-actions">
                      <el-button 
                        type="primary" 
                        size="small" 
                        link 
                        @click="handleEditDevice(device, index)"
                      >
                        编辑
                      </el-button>
                      <el-button 
                        type="danger" 
                        size="small" 
                        link 
                        @click="handleDeleteDevice(index)"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </template>
                
                <div class="device-details">
                  <div class="detail-item">
                    <span class="label">设备JID:</span>
                    <span class="value">{{ device.jid || 'N/A' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">设备名称表达式:</span>
                    <span class="value">{{ device.deviceNameExpression || 'N/A' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">设备类型表达式:</span>
                    <span class="value">{{ device.deviceTypeExpression || 'N/A' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">属性数量:</span>
                    <span class="value">{{ (device.attributes || []).length }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">遥测数量:</span>
                    <span class="value">{{ (device.timeseries || []).length }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">RPC数量:</span>
                    <span class="value">{{ (device.serverSideRpc || []).length }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">属性更新数量:</span>
                    <span class="value">{{ (device.attributeUpdates || []).length }}</span>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- XMPP设备配置对话框 -->
    <el-dialog 
      v-model="deviceDialogVisible" 
      :title="editingDeviceIndex === -1 ? '添加XMPP设备' : '编辑XMPP设备'"
      width="85%"
      :before-close="handleCloseDialog"
      destroy-on-close
    >
      <XmppDeviceConfig v-model="editingDevice" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelDevice">取消</el-button>
          <el-button type="primary" @click="handleSaveDevice" :loading="saving">
            {{ editingDeviceIndex === -1 ? '添加' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import XmppServerConfig from './components/XmppServerConfig.vue'
import XmppDeviceConfig from './components/XmppDeviceConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')
const deviceDialogVisible = ref(false)
const editingDeviceIndex = ref(-1)
const editingDevice = ref({})
const saving = ref(false)

// 默认配置结构
const defaultConfigStructure = {
  id: '',
  logLevel: 'INFO',
  enableRemoteLogging: false,
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  server: {
    jid: 'gateway@localhost',
    password: 'password',
    host: 'localhost',
    port: 5222,
    use_ssl: false,
    disable_starttls: false,
    force_starttls: true,
    timeout: 10000,
    plugins: ['xep_0030', 'xep_0323', 'xep_0325']
  },
  devices: []
}

// 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 设备操作
const handleAddDevice = () => {
  editingDeviceIndex.value = -1
  editingDevice.value = createDefaultDevice()
  deviceDialogVisible.value = true
}

const handleEditDevice = (device, index) => {
  editingDeviceIndex.value = index
  editingDevice.value = JSON.parse(JSON.stringify(device))
  deviceDialogVisible.value = true
}

const handleDeleteDevice = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个XMPP设备配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    config.devices.splice(index, 1)
    ElMessage.success('设备删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleSaveDevice = async () => {
  try {
    saving.value = true
    
    // 验证必填字段
    if (!editingDevice.value.jid) {
      ElMessage.error('请输入设备JID')
      return
    }
    if (!editingDevice.value.deviceNameExpression) {
      ElMessage.error('请输入设备名称表达式')
      return
    }
    
    if (editingDeviceIndex.value === -1) {
      // 添加模式
      config.devices.push(JSON.parse(JSON.stringify(editingDevice.value)))
      ElMessage.success('设备添加成功')
    } else {
      // 编辑模式
      config.devices[editingDeviceIndex.value] = JSON.parse(JSON.stringify(editingDevice.value))
      ElMessage.success('设备更新成功')
    }
    
    deviceDialogVisible.value = false
    editingDevice.value = {}
    editingDeviceIndex.value = -1
  } catch (error) {
    console.error('保存设备失败:', error)
    ElMessage.error('保存设备失败')
  } finally {
    saving.value = false
  }
}

const handleCancelDevice = () => {
  deviceDialogVisible.value = false
  editingDevice.value = {}
  editingDeviceIndex.value = -1
}

const handleCloseDialog = () => {
  handleCancelDevice()
}

// 创建默认设备配置
const createDefaultDevice = () => {
  return {
    jid: '',
    deviceNameExpression: '',
    deviceTypeExpression: 'default',
    attributes: [],
    timeseries: [],
    attributeUpdates: [],
    serverSideRpc: []
  }
}

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'xmpp', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
        
      console.log('XMPP 连接器配置初始化成功')
    } else {
      ElMessage.warning('XMPP 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('XMPP 连接器初始化失败:', error)
    ElMessage.error('XMPP 连接器初始化失败')
  }
})



// 暴露配置数据给父组件
defineExpose({ 
  xmpp: config
})
</script>

<style lang="scss" scoped>
.xmpp-config {
  .xmpp-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
}

.config-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #303133;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  
  .device-card {
    .device-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      
      .device-info {
        flex: 1;
        
        .device-name {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          word-break: break-word;
        }
        
        .device-type {
          font-size: 12px;
          color: #909399;
          background: #f5f7fa;
          padding: 2px 8px;
          border-radius: 12px;
        }
      }
      
      .device-actions {
        display: flex;
        gap: 8px;
        margin-left: 12px;
      }
    }
    
    .device-details {
      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
        border-bottom: 1px solid #f5f7fa;
        
        &:last-child {
          border-bottom: none;
        }
        
        .label {
          font-size: 13px;
          color: #909399;
          font-weight: 500;
        }
        
        .value {
          font-size: 13px;
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }
}

.dialog-footer {
  padding: 20px 0 0;
  text-align: right;
}
</style>