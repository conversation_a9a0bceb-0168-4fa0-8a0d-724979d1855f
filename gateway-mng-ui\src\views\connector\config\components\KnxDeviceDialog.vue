<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑KNX设备' : '添加KNX设备'"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="deviceForm" :rules="rules" ref="formRef" label-width="160px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 设备信息配置 -->
        <el-tab-pane label="设备信息" name="info">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名数据类型" prop="deviceInfo.deviceNameDataType" required>
                <el-select v-model="deviceForm.deviceInfo.deviceNameDataType" placeholder="请选择数据类型">
                  <el-option label="字符串 (string)" value="string" />
                  <el-option label="整数 (int)" value="int" />
                  <el-option label="浮点数 (float)" value="float" />
                </el-select>
                <div class="field-hint">设备名称的数据类型</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备名表达式来源" prop="deviceInfo.deviceNameExpressionSource" required>
                <el-select v-model="deviceForm.deviceInfo.deviceNameExpressionSource" placeholder="请选择表达式来源">
                  <el-option label="表达式 (expression)" value="expression" />
                  <el-option label="常量 (constant)" value="constant" />
                </el-select>
                <div class="field-hint">设备名称的获取方式</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="设备名表达式" prop="deviceInfo.deviceNameExpression" required>
                <el-input 
                  v-model="deviceForm.deviceInfo.deviceNameExpression" 
                  placeholder="Device ${1/0/5}"
                />
                <div class="field-hint">设备名称表达式，支持群组地址变量替换</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备配置文件数据类型" prop="deviceInfo.deviceProfileDataType">
                <el-select v-model="deviceForm.deviceInfo.deviceProfileDataType" placeholder="请选择数据类型">
                  <el-option label="无 (none)" value="none" />
                  <el-option label="字符串 (string)" value="string" />
                  <el-option label="整数 (int)" value="int" />
                </el-select>
                <div class="field-hint">设备配置文件的数据类型</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备配置文件表达式来源" prop="deviceInfo.deviceProfileExpressionSource">
                <el-select v-model="deviceForm.deviceInfo.deviceProfileExpressionSource" placeholder="请选择表达式来源">
                  <el-option label="表达式 (expression)" value="expression" />
                  <el-option label="常量 (constant)" value="constant" />
                </el-select>
                <div class="field-hint">设备配置文件的获取方式</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备配置文件名表达式" prop="deviceInfo.deviceProfileNameExpression">
                <el-input 
                  v-model="deviceForm.deviceInfo.deviceProfileNameExpression" 
                  placeholder="default"
                />
                <div class="field-hint">设备配置文件名称</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="轮询周期" prop="pollPeriod" required>
                <el-input-number 
                  v-model="deviceForm.pollPeriod" 
                  :min="1000" 
                  :step="1000"
                  controls-position="right"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">设备数据轮询间隔</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <KnxDataPoints
            v-model="deviceForm.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从KNX设备读取的属性数据"
          />
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="timeseries">
          <KnxDataPoints
            v-model="deviceForm.timeseries"
            data-type="timeseries"
            title="遥测数据配置"
            description="配置从KNX设备读取的遥测数据"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import KnxDataPoints from './KnxDataPoints.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  device: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const activeTab = ref('info')
const saving = ref(false)

// 设备表单数据
const deviceForm = reactive({
  deviceInfo: {
    deviceNameDataType: 'string',
    deviceNameExpressionSource: 'expression',
    deviceNameExpression: 'Device ${1/0/5}',
    deviceProfileDataType: 'none',
    deviceProfileExpressionSource: 'constant',
    deviceProfileNameExpression: 'default'
  },
  pollPeriod: 5000,
  attributes: [],
  timeseries: []
})

// 表单验证规则
const rules = reactive({
  'deviceInfo.deviceNameDataType': [
    { required: true, message: '请选择设备名数据类型', trigger: 'change' }
  ],
  'deviceInfo.deviceNameExpressionSource': [
    { required: true, message: '请选择设备名表达式来源', trigger: 'change' }
  ],
  'deviceInfo.deviceNameExpression': [
    { required: true, message: '请输入设备名表达式', trigger: 'blur' }
  ],
  pollPeriod: [
    { required: true, message: '请输入轮询周期', trigger: 'blur' },
    { type: 'number', min: 1000, message: '轮询周期不能小于1000毫秒', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.device) {
    // 加载设备数据
    Object.assign(deviceForm, {
      deviceInfo: {
        deviceNameDataType: 'string',
        deviceNameExpressionSource: 'expression',
        deviceNameExpression: 'Device ${1/0/5}',
        deviceProfileDataType: 'none',
        deviceProfileExpressionSource: 'constant',
        deviceProfileNameExpression: 'default'
      },
      pollPeriod: 5000,
      attributes: [],
      timeseries: [],
      ...props.device
    })
  } else if (newValue) {
    // 重置为默认值
    Object.assign(deviceForm, {
      deviceInfo: {
        deviceNameDataType: 'string',
        deviceNameExpressionSource: 'expression',
        deviceNameExpression: 'Device ${1/0/5}',
        deviceProfileDataType: 'none',
        deviceProfileExpressionSource: 'constant',
        deviceProfileNameExpression: 'default'
      },
      pollPeriod: 5000,
      attributes: [],
      timeseries: []
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    // 重置表单
    activeTab.value = 'info'
  }
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...deviceForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '设备更新成功' : '设备添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.unit-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}
</style> 