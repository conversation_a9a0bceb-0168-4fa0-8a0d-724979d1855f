<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑文件路径' : '添加文件路径'"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="pathForm" :rules="rules" ref="formRef" label-width="160px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备模式名称" prop="devicePatternName" required>
                <el-input 
                  v-model="pathForm.devicePatternName" 
                  placeholder="asd"
                />
                <div class="field-hint">用于标识设备的模式名称</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备模式类型" prop="devicePatternType" required>
                <el-input 
                  v-model="pathForm.devicePatternType" 
                  placeholder="Device"
                />
                <div class="field-hint">设备的类型标识</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="监控路径" prop="path" required>
                <el-input 
                  v-model="pathForm.path" 
                  placeholder="fol/*_hello*.txt"
                />
                <div class="field-hint">要监控的文件路径，支持通配符模式</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="分隔符" prop="delimiter">
                <el-input 
                  v-model="pathForm.delimiter" 
                  placeholder=","
                />
                <div class="field-hint">CSV文件的字段分隔符</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="读取模式" prop="readMode">
                <el-select v-model="pathForm.readMode" placeholder="请选择读取模式">
                  <el-option label="完全读取" value="FULL" />
                  <el-option label="部分读取" value="PARTIAL" />
                </el-select>
                <div class="field-hint">文件读取模式</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最大文件大小" prop="maxFileSize">
                <el-input-number 
                  v-model="pathForm.maxFileSize" 
                  :min="1" 
                  :step="1"
                  controls-position="right"
                />
                <span class="unit-suffix">MB</span>
                <div class="field-hint">支持读取的最大文件大小</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="轮询周期" prop="pollPeriod">
                <el-input-number 
                  v-model="pathForm.pollPeriod" 
                  :min="100" 
                  :step="100"
                  controls-position="right"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">文件检查的轮询间隔</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="数据视图模式" prop="txtFileDataView">
                <el-select v-model="pathForm.txtFileDataView" placeholder="请选择数据视图模式">
                  <el-option label="表格模式" value="TABLE" />
                  <el-option label="切片模式" value="SLICED" />
                </el-select>
                <div class="field-hint">文本文件的数据解析模式</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="启用文件排序">
                <el-switch v-model="pathForm.withSortingFiles" />
                <div class="field-hint">是否按文件名排序处理</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <FtpDataPoints
            v-model="pathForm.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从文件中提取的设备属性数据"
          />
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="timeseries">
          <FtpDataPoints
            v-model="pathForm.timeseries"
            data-type="timeseries"
            title="遥测数据配置"
            description="配置从文件中提取的遥测数据"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import FtpDataPoints from './FtpDataPoints.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  path: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const activeTab = ref('basic')
const saving = ref(false)

// 路径表单数据
const pathForm = reactive({
  devicePatternName: 'asd',
  devicePatternType: 'Device',
  delimiter: ',',
  path: 'fol/*_hello*.txt',
  readMode: 'FULL',
  maxFileSize: 5,
  pollPeriod: 500,
  txtFileDataView: 'SLICED',
  withSortingFiles: true,
  attributes: [],
  timeseries: []
})

// 表单验证规则
const rules = reactive({
  devicePatternName: [
    { required: true, message: '请输入设备模式名称', trigger: 'blur' }
  ],
  devicePatternType: [
    { required: true, message: '请输入设备模式类型', trigger: 'blur' }
  ],
  path: [
    { required: true, message: '请输入监控路径', trigger: 'blur' }
  ],
  readMode: [
    { required: true, message: '请选择读取模式', trigger: 'change' }
  ],
  maxFileSize: [
    { required: true, message: '请输入最大文件大小', trigger: 'blur' },
    { type: 'number', min: 1, message: '文件大小不能小于1MB', trigger: 'blur' }
  ],
  pollPeriod: [
    { required: true, message: '请输入轮询周期', trigger: 'blur' },
    { type: 'number', min: 100, message: '轮询周期不能小于100毫秒', trigger: 'blur' }
  ],
  txtFileDataView: [
    { required: true, message: '请选择数据视图模式', trigger: 'change' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.path) {
    // 加载路径数据
    Object.assign(pathForm, {
      devicePatternName: 'asd',
      devicePatternType: 'Device',
      delimiter: ',',
      path: 'fol/*_hello*.txt',
      readMode: 'FULL',
      maxFileSize: 5,
      pollPeriod: 500,
      txtFileDataView: 'SLICED',
      withSortingFiles: true,
      attributes: [],
      timeseries: [],
      ...props.path
    })
  } else if (newValue) {
    // 重置为默认值
    Object.assign(pathForm, {
      devicePatternName: 'asd',
      devicePatternType: 'Device',
      delimiter: ',',
      path: 'fol/*_hello*.txt',
      readMode: 'FULL',
      maxFileSize: 5,
      pollPeriod: 500,
      txtFileDataView: 'SLICED',
      withSortingFiles: true,
      attributes: [],
      timeseries: []
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    // 重置表单
    activeTab.value = 'basic'
  }
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...pathForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '路径更新成功' : '路径添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.unit-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-select) {
  width: 100%;
}
</style> 