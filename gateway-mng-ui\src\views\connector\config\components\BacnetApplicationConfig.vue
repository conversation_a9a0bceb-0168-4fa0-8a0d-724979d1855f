<template>
  <div class="bacnet-application-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>应用配置</span>
          <el-tooltip content="BACnet应用层配置，定义网关在BACnet网络中的身份和行为" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="applicationConfig" :rules="rules" label-width="160px" ref="formRef">
        <!-- 基础配置 -->
        <div class="config-section">
          <h4 class="section-title">基础配置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="对象名称" prop="objectName" required>
                <el-input 
                  v-model="applicationConfig.objectName" 
                  placeholder="TB_gateway"
                  
                />
                <div class="field-hint">BACnet网络中的对象名称标识</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="监听地址" prop="host" required>
                <el-input 
                  v-model="applicationConfig.host" 
                  placeholder="0.0.0.0"
                  
                />
                <div class="field-hint">网关监听的IP地址</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="监听端口" prop="port" required>
                <el-input-number 
                  v-model.number="applicationConfig.port" 
                  :min="1" 
                  :max="65535"
                  controls-position="right"
                  placeholder="47808"
                  
                />
                <div class="field-hint">BACnet通信端口 (1-65535)</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="对象标识符" prop="objectIdentifier" required>
                <el-input-number 
                  v-model.number="applicationConfig.objectIdentifier" 
                  :min="0" 
                  :max="4194303"
                  controls-position="right"
                  placeholder="599"
                  
                />
                <div class="field-hint">唯一的设备实例号 (0-4194303)</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="厂商标识符" prop="vendorIdentifier" required>
                <el-input-number 
                  v-model.number="applicationConfig.vendorIdentifier" 
                  :min="0" 
                  :max="65535"
                  controls-position="right"
                  placeholder="15"
                  
                />
                <div class="field-hint">厂商ID，15为IoTCloud</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="网络掩码" prop="mask">
                <el-input 
                  v-model="applicationConfig.mask" 
                  placeholder="255.255.255.0"
                  
                />
                <div class="field-hint">可选的网络掩码</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 高级设置 -->
        <el-card class="advanced-settings">
          <template #header>
            <div class="card-header">
              <span>高级设置</span>
              <el-button 
                type="text" 
                @click="showAdvanced = !showAdvanced"
                :icon="showAdvanced ? ArrowUp : ArrowDown"
              >
                {{ showAdvanced ? '收起' : '展开' }}
              </el-button>
            </div>
          </template>
          
          <el-collapse-transition>
            <div v-show="showAdvanced">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="最大APDU长度" prop="maxApduLengthAccepted">
                    <el-input-number 
                      v-model.number="applicationConfig.maxApduLengthAccepted" 
                      :min="50" 
                      :max="1476"
                      controls-position="right"
                      placeholder="1476"
                      
                    />
                    <div class="field-hint">应用数据单元最大长度 (50-1476)</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="分段支持" prop="segmentationSupported">
                    <el-select 
                      v-model="applicationConfig.segmentationSupported" 
                      placeholder="请选择分段支持类型"
                      
                    >
                      <el-option label="无分段" value="noSegmentation" />
                      <el-option label="分段传输" value="segmentedTransmit" />
                      <el-option label="分段接收" value="segmentedReceive" />
                      <el-option label="分段双向" value="segmentedBoth" />
                    </el-select>
                    <div class="field-hint">数据分段传输支持类型</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="网络号" prop="networkNumber">
                    <el-input-number 
                      v-model.number="applicationConfig.networkNumber" 
                      :min="0" 
                      :max="65535"
                      controls-position="right"
                      placeholder="3"
                      
                    />
                    <div class="field-hint">BACnet网络编号 (0-65535)</div>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="设备发现超时" prop="deviceDiscoveryTimeoutInSec">
                    <el-input-number 
                      v-model.number="applicationConfig.deviceDiscoveryTimeoutInSec" 
                      :min="1" 
                      :max="300"
                      controls-position="right"
                      placeholder="5"
                      
                    />
                    <span class="unit-suffix">秒</span>
                    <div class="field-hint">设备发现操作的超时时间 (1-300秒)</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-collapse-transition>
        </el-card>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick, defineEmits, defineProps } from 'vue'
import { QuestionFilled, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      objectName: 'TB_gateway',
      host: '0.0.0.0',
      port: 47808,
      mask: '',
      objectIdentifier: 599,
      maxApduLengthAccepted: 1476,
      segmentationSupported: 'segmentedBoth',
      networkNumber: 3,
      vendorIdentifier: 15,
      deviceDiscoveryTimeoutInSec: 5
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const showAdvanced = ref(false)

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 确保数值字段为数字类型
const ensureNumberTypes = (config) => {
  const result = { ...config }
  
  // 转换数值字段
  if (result.port !== undefined) {
    result.port = typeof result.port === 'string' ? parseInt(result.port) || 47808 : result.port
  }
  if (result.objectIdentifier !== undefined) {
    result.objectIdentifier = typeof result.objectIdentifier === 'string' ? parseInt(result.objectIdentifier) || 599 : result.objectIdentifier
  }
  if (result.vendorIdentifier !== undefined) {
    result.vendorIdentifier = typeof result.vendorIdentifier === 'string' ? parseInt(result.vendorIdentifier) || 15 : result.vendorIdentifier
  }
  if (result.maxApduLengthAccepted !== undefined) {
    result.maxApduLengthAccepted = typeof result.maxApduLengthAccepted === 'string' ? parseInt(result.maxApduLengthAccepted) || 1476 : result.maxApduLengthAccepted
  }
  if (result.networkNumber !== undefined) {
    result.networkNumber = typeof result.networkNumber === 'string' ? parseInt(result.networkNumber) || 3 : result.networkNumber
  }
  if (result.deviceDiscoveryTimeoutInSec !== undefined) {
    result.deviceDiscoveryTimeoutInSec = typeof result.deviceDiscoveryTimeoutInSec === 'string' ? parseInt(result.deviceDiscoveryTimeoutInSec) || 5 : result.deviceDiscoveryTimeoutInSec
  }
  
  return result
}

// 应用配置数据
const applicationConfig = reactive({
  objectName: 'TB_gateway',
  host: '0.0.0.0',
  port: 47808,
  mask: '',
  objectIdentifier: 599,
  maxApduLengthAccepted: 1476,
  segmentationSupported: 'segmentedBoth',
  networkNumber: 3,
  vendorIdentifier: 15,
  deviceDiscoveryTimeoutInSec: 5,
  ...ensureNumberTypes(props.modelValue || {})
})

// 表单验证规则
const rules = reactive({
  objectName: [
    { required: true, message: '请输入对象名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入监听地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入监听端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围为 1-65535', trigger: 'blur' }
  ],
  objectIdentifier: [
    { required: true, message: '请输入对象标识符', trigger: 'blur' },
    { type: 'number', min: 0, max: 4194303, message: '对象标识符范围为 0-4194303', trigger: 'blur' }
  ],
  vendorIdentifier: [
    { required: true, message: '请输入厂商标识符', trigger: 'blur' },
    { type: 'number', min: 0, max: 65535, message: '厂商标识符范围为 0-65535', trigger: 'blur' }
  ],
  maxApduLengthAccepted: [
    { type: 'number', min: 50, max: 1476, message: 'APDU长度范围为 50-1476', trigger: 'blur' }
  ],
  networkNumber: [
    { type: 'number', min: 0, max: 65535, message: '网络号范围为 0-65535', trigger: 'blur' }
  ],
  deviceDiscoveryTimeoutInSec: [
    { type: 'number', min: 1, max: 300, message: '超时时间范围为 1-300秒', trigger: 'blur' }
  ]
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value && newValue) {
    const normalizedValue = ensureNumberTypes(newValue)
    Object.assign(applicationConfig, normalizedValue)
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(applicationConfig, (newConfig) => {
  isInternalUpdate.value = true
  emit('update:modelValue', { ...newConfig })
  // 在下一个tick重置标志
  nextTick(() => {
    isInternalUpdate.value = false
  })
}, { deep: true })



// 验证表单
const validate = () => {
  return formRef.value?.validate()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

defineExpose({
  validate,
  resetForm
})
</script>

<style lang="scss" scoped>
.bacnet-application-config {
  .config-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
      
      .el-icon {
        margin-left: 8px;
        color: #909399;
        cursor: help;
      }
    }
  }
  
  .config-section {
    margin-bottom: 30px;
    
    .section-title {
      margin: 0 0 20px 0;
      padding-bottom: 10px;
      border-bottom: 1px solid #e4e7ed;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .advanced-settings {
    margin-top: 20px;
    
    .card-header {
      .el-button {
        padding: 0;
        font-size: 14px;
        
        .el-icon {
          margin-left: 4px;
        }
      }
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .unit-suffix {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 24px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
    
    .el-input-number {
      width: 100%;
    }
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
}
</style> 