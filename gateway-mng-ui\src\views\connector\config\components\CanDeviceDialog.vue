<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑设备' : '添加设备'"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="deviceForm" :rules="rules" ref="formRef" label-width="160px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称" prop="name" required>
                <el-input 
                  v-model="deviceForm.name" 
                  placeholder="Car"
                />
                <div class="field-hint">CAN设备的显示名称</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="仅变化时发送数据">
                <el-switch v-model="deviceForm.sendDataOnlyOnChange" />
                <div class="field-hint">仅当数据发生变化时才发送到平台</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="允许未知RPC">
                <el-switch v-model="deviceForm.enableUnknownRpc" />
                <div class="field-hint">是否允许执行未在配置中定义的RPC方法</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="启用受限模式">
                <el-switch v-model="deviceForm.strictEval" />
                <div class="field-hint">启用受限模式以增强安全性</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <CanDataPoints
            v-model="deviceForm.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从CAN总线读取的设备属性数据"
          />
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="timeseries">
          <CanDataPoints
            v-model="deviceForm.timeseries"
            data-type="timeseries"
            title="遥测数据配置"
            description="配置从CAN总线读取的遥测数据"
          />
        </el-tab-pane>

        <!-- 属性更新 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <CanDataPoints
            v-model="deviceForm.attributeUpdates"
            data-type="attributeUpdates"
            title="属性更新配置"
            description="配置向CAN设备写入属性的操作"
          />
        </el-tab-pane>

        <!-- RPC配置 -->
        <el-tab-pane label="RPC配置" name="serverSideRpc">
          <CanDataPoints
            v-model="deviceForm.serverSideRpc"
            data-type="serverSideRpc"
            title="RPC方法配置"
            description="配置设备的远程过程调用方法"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import CanDataPoints from './CanDataPoints.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  device: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const activeTab = ref('basic')
const saving = ref(false)

// 设备表单数据
const deviceForm = reactive({
  name: 'Car',
  sendDataOnlyOnChange: false,
  enableUnknownRpc: true,
  strictEval: false,
  attributes: [],
  timeseries: [],
  attributeUpdates: [],
  serverSideRpc: []
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.device) {
    // 加载设备数据
    Object.assign(deviceForm, {
      name: 'Car',
      sendDataOnlyOnChange: false,
      enableUnknownRpc: true,
      strictEval: false,
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      serverSideRpc: [],
      ...props.device
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    // 重置表单
    activeTab.value = 'basic'
  }
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...deviceForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '设备更新成功' : '设备添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 