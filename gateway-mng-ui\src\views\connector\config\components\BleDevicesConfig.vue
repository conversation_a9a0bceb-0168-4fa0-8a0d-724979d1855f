<template>
  <div class="ble-devices-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>BLE设备配置</span>
          <el-button type="primary" size="small" @click="handleAddDevice">
            <el-icon><Plus /></el-icon>
            添加设备
          </el-button>
        </div>
      </template>
      
      <!-- 设备列表 -->
      <div v-if="devices.length === 0" class="empty-state">
        <el-empty description="暂无设备配置">
          <el-button type="primary" @click="handleAddDevice">添加第一个设备</el-button>
        </el-empty>
      </div>
      
      <div v-else class="devices-grid">
        <div 
          v-for="(device, index) in devices" 
          :key="index" 
          class="device-card"
        >
          <el-card shadow="hover">
            <template #header>
              <div class="device-header">
                <div class="device-info">
                  <h4 class="device-name">{{ device.name || '未命名设备' }}</h4>
                  <span class="device-mac">{{ device.MACAddress || '未设置MAC地址' }}</span>
                </div>
                <div class="device-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    link 
                    @click="handleEditDevice(device, index)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    link 
                    @click="handleDeleteDevice(index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="device-details">
              <div class="detail-item">
                <span class="label">轮询周期:</span>
                <span class="value">{{ device.pollPeriod }}ms</span>
              </div>
              <div class="detail-item">
                <span class="label">连接超时:</span>
                <span class="value">{{ device.timeout }}ms</span>
              </div>
              <div class="detail-item">
                <span class="label">遥测数量:</span>
                <span class="value">{{ (device.telemetry || []).length }}</span>
              </div>
              <div class="detail-item">
                <span class="label">属性数量:</span>
                <span class="value">{{ (device.attributes || []).length }}</span>
              </div>
              <div class="detail-item">
                <span class="label">RPC数量:</span>
                <span class="value">{{ (device.serverSideRpc || []).length }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 设备配置对话框 -->
    <BleDeviceDialog
      v-model="dialogVisible"
      :device="currentDevice"
      :is-edit="isEdit"
      @save="handleSaveDevice"
      @cancel="handleCancelDevice"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import BleDeviceDialog from './BleDeviceDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const devices = ref([])
const dialogVisible = ref(false)
const currentDevice = ref(null)
const currentIndex = ref(-1)
const isEdit = ref(false)

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  devices.value = [...(newValue || [])]
}, { deep: true, immediate: true })

// 监听设备列表变化
watch(devices, (newDevices) => {
  emit('update:modelValue', [...newDevices])
}, { deep: true })

// 添加设备
const handleAddDevice = () => {
  currentDevice.value = createDefaultDevice()
  currentIndex.value = -1
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑设备
const handleEditDevice = (device, index) => {
  currentDevice.value = { ...device }
  currentIndex.value = index
  isEdit.value = true
  dialogVisible.value = true
}

// 删除设备
const handleDeleteDevice = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个设备配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    devices.value.splice(index, 1)
    ElMessage.success('设备删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存设备
const handleSaveDevice = (deviceData) => {
  if (isEdit.value) {
    // 编辑模式
    devices.value[currentIndex.value] = { ...deviceData }
    ElMessage.success('设备配置更新成功')
  } else {
    // 添加模式
    devices.value.push({ ...deviceData })
    ElMessage.success('设备添加成功')
  }
  
  dialogVisible.value = false
  currentDevice.value = null
  currentIndex.value = -1
}

// 取消设备配置
const handleCancelDevice = () => {
  dialogVisible.value = false
  currentDevice.value = null
  currentIndex.value = -1
}

// 创建默认设备配置
const createDefaultDevice = () => {
  return {
    name: 'Temperature and humidity sensor',
    MACAddress: '',
    pollPeriod: 500000,
    showMap: false,
    timeout: 10000,
    connectRetry: 5,
    connectRetryInSeconds: 0,
    waitAfterConnectRetries: 10,
    telemetry: [],
    attributes: [],
    attributeUpdates: [],
    serverSideRpc: []
  }
}
</script>

<style lang="scss" scoped>
.ble-devices-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
  
  .devices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    
    .device-card {
      .device-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .device-info {
          flex: 1;
          
          .device-name {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            word-break: break-word;
          }
          
          .device-mac {
            font-size: 12px;
            color: #909399;
            background: #f5f7fa;
            padding: 2px 8px;
            border-radius: 12px;
            font-family: monospace;
          }
        }
        
        .device-actions {
          display: flex;
          gap: 8px;
          margin-left: 12px;
        }
      }
      
      .device-details {
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f5f7fa;
          
          &:last-child {
            border-bottom: none;
          }
          
          .label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 600;
          }
        }
      }
    }
  }
  
  :deep(.el-card) {
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
  
  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f2f5;
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .devices-grid {
    grid-template-columns: 1fr;
  }
}
</style> 