<template>
  <div class="mqtt-data-mapping-config">
    <div class="config-section">
      <div class="section-header">
        <div class="section-title">数据映射列表</div>
        <el-button type="primary" :icon="CirclePlus" @click="addMapping">
          添加数据映射
        </el-button>
      </div>
      
      <div v-if="mappings.length === 0" class="empty-state">
        <el-empty description="暂无数据映射配置">
          <el-button type="primary" @click="addMapping">添加第一个映射</el-button>
        </el-empty>
      </div>

      <div v-for="(mapping, index) in mappings" :key="index" class="mapping-config-card">
        <div class="card-header">
          <div class="card-title">
            <el-icon><DataBoard /></el-icon>
            映射 {{ index + 1 }}: {{ mapping.name || mapping.topicFilter || '未命名映射' }}
          </div>
          <div class="card-actions">
            <el-button type="primary" size="small" @click="editMapping(index)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteMapping(index)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
        
        <div class="card-content">
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">主题过滤器:</span>
                <span class="value">{{ mapping.topicFilter || '未设置' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">转换器类型:</span>
                <span class="value">{{ getConverterTypeLabel(mapping.converter?.type) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">QoS等级:</span>
                <span class="value">QoS {{ mapping.subscriptionQos ?? 1 }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">设备名称表达式:</span>
                <span class="value">{{ mapping.converter?.deviceInfo?.deviceNameExpression || '未设置' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">属性数量:</span>
                <span class="value">{{ mapping.converter?.attributes?.length || 0 }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">遥测数量:</span>
                <span class="value">{{ mapping.converter?.timeseries?.length || 0 }}</span>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <div class="info-item">
                <span class="label">启用状态:</span>
                <el-tag :type="mapping.enabled !== false ? 'success' : 'danger'" size="small">
                  {{ mapping.enabled !== false ? '启用' : '禁用' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <span class="label">仅变化时发送:</span>
                <el-tag :type="mapping.converter?.sendDataOnlyOnChange ? 'success' : 'info'" size="small">
                  {{ mapping.converter?.sendDataOnlyOnChange ? '是' : '否' }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 映射配置对话框 -->
    <el-dialog
      v-model="mappingDialogVisible"
      :title="mappingDialogTitle"
      width="80%"
      :close-on-click-modal="false"
      destroy-on-close>
      <MqttDataMappingDialog
        v-if="mappingDialogVisible"
        :mapping-config="currentMappingConfig"
        :is-edit="isEditMode"
        @save="handleMappingSave"
        @cancel="handleMappingCancel"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, defineEmits, defineProps, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CirclePlus, Delete, Edit, DataBoard } from '@element-plus/icons-vue'
import MqttDataMappingDialog from './MqttDataMappingDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const mappings = ref([])
const mappingDialogVisible = ref(false)
const isEditMode = ref(false)
const currentMappingIndex = ref(-1)
const currentMappingConfig = ref({})

// 标准的isInternalUpdate模式
const isInternalUpdate = ref(false)

// 计算属性
const mappingDialogTitle = computed(() => {
  return isEditMode.value ? '编辑数据映射' : '添加数据映射'
})

// 创建默认映射
const createDefaultMapping = () => {
  return {
    name: '',
    topicFilter: 'sensor/+/data',
    subscriptionQos: 1,
    enabled: true,
    converter: {
      type: 'json',
      deviceInfo: {
        deviceNameExpressionSource: 'message',
        deviceNameExpression: '${serialNumber}',
        deviceProfileExpressionSource: 'constant',
        deviceProfileExpression: 'default'
      },
      sendDataOnlyOnChange: false,
      timeout: 60000,
      attributes: [],
      timeseries: []
    }
  }
}

// 确保映射对象结构完整
const ensureMappingStructure = (mapping) => {
  const defaultMapping = createDefaultMapping()
  
  return {
    name: mapping.name || defaultMapping.name,
    topicFilter: mapping.topicFilter || defaultMapping.topicFilter,
    subscriptionQos: mapping.subscriptionQos ?? defaultMapping.subscriptionQos,
    enabled: mapping.enabled ?? defaultMapping.enabled,
    converter: {
      type: mapping.converter?.type || defaultMapping.converter.type,
      deviceInfo: {
        deviceNameExpressionSource: mapping.converter?.deviceInfo?.deviceNameExpressionSource || defaultMapping.converter.deviceInfo.deviceNameExpressionSource,
        deviceNameExpression: mapping.converter?.deviceInfo?.deviceNameExpression || defaultMapping.converter.deviceInfo.deviceNameExpression,
        deviceProfileExpressionSource: mapping.converter?.deviceInfo?.deviceProfileExpressionSource || defaultMapping.converter.deviceInfo.deviceProfileExpressionSource,
        deviceProfileExpression: mapping.converter?.deviceInfo?.deviceProfileExpression || defaultMapping.converter.deviceInfo.deviceProfileExpression
      },
      sendDataOnlyOnChange: mapping.converter?.sendDataOnlyOnChange ?? defaultMapping.converter.sendDataOnlyOnChange,
      timeout: mapping.converter?.timeout || defaultMapping.converter.timeout,
      attributes: mapping.converter?.attributes || defaultMapping.converter.attributes,
      timeseries: mapping.converter?.timeseries || defaultMapping.converter.timeseries
    }
  }
}

// 转换器类型标签映射
const converterTypeLabels = {
  json: 'JSON转换器',
  bytes: '字节转换器',
  custom: '自定义转换器'
}

// 获取转换器类型标签
const getConverterTypeLabel = (type) => {
  return converterTypeLabels[type] || '未知类型'
}

// 添加映射
const addMapping = () => {
  isEditMode.value = false
  currentMappingIndex.value = -1
  currentMappingConfig.value = createDefaultMapping()
  mappingDialogVisible.value = true
}

// 编辑映射
const editMapping = (index) => {
  isEditMode.value = true
  currentMappingIndex.value = index
  currentMappingConfig.value = { ...mappings.value[index] }
  mappingDialogVisible.value = true
}

// 删除映射
const deleteMapping = async (index) => {
  const mapping = mappings.value[index]
  const mappingName = mapping.name || mapping.topicFilter || `映射 ${index + 1}`
  
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${mappingName}" 吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    mappings.value.splice(index, 1)
    ElMessage.success('映射删除成功')
  } catch (error) {
    // 用户取消删除
  }
}

// 处理映射保存
const handleMappingSave = (mappingConfig) => {
  console.log('MQTT映射配置 - 保存映射:', mappingConfig)
  
  try {
    // 深拷贝配置以避免引用问题
    const configToSave = JSON.parse(JSON.stringify(mappingConfig))
    
    if (isEditMode.value) {
      // 编辑模式 - 更新现有映射
      mappings.value[currentMappingIndex.value] = configToSave
      console.log('MQTT映射配置 - 更新映射成功:', currentMappingIndex.value)
    } else {
      // 添加模式 - 添加新映射
      mappings.value.push(configToSave)
      console.log('MQTT映射配置 - 添加映射成功')
    }
    
    mappingDialogVisible.value = false
  } catch (error) {
    console.error('MQTT映射配置 - 保存映射失败:', error)
    ElMessage.error('保存映射配置失败')
  }
}

// 处理映射取消
const handleMappingCancel = () => {
  mappingDialogVisible.value = false
}

// 监听props变化 - 标准模式
watch(() => props.modelValue, (newValue) => {
  if (isInternalUpdate.value) return
  
  console.log('MQTT映射配置 - 外部数据更新:', newValue)
  
  if (newValue && Array.isArray(newValue)) {
    mappings.value = newValue.map(mapping => ensureMappingStructure(mapping))
  } else {
    mappings.value = []
  }
}, { deep: true, immediate: true })

// 监听mappings变化 - 标准模式
watch(mappings, (newMappings) => {
  isInternalUpdate.value = true
  nextTick(() => {
    isInternalUpdate.value = false
  })
  
  console.log('MQTT映射配置 - 数据更新:', newMappings)
  emit('update:modelValue', [...newMappings])
}, { deep: true })
</script>

<style lang="scss" scoped>
.mqtt-data-mapping-config {
  .config-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: #fafafa;
    border-radius: 8px;
    border: 2px dashed #e4e7ed;
  }

  .mapping-config-card {
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border-color: #409eff;
    }
    
    &:last-child {
      margin-bottom: 0;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #e4e7ed;
      background: #f8f9fa;
      
      .card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        
        .el-icon {
          color: #409eff;
          font-size: 18px;
        }
      }
      
      .card-actions {
        display: flex;
        gap: 8px;
      }
    }

    .card-content {
      padding: 16px 20px;

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #606266;
          margin-right: 8px;
          min-width: 120px;
        }

        .value {
          color: #303133;
          word-break: break-all;
        }
      }
    }
  }
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px;
}

:deep(.el-button + .el-button) {
  margin-left: 8px;
}
</style> 