[{"timeout": 100, "command": ["/bin/sh", "-c", "ps -A -o cpu,%mem | awk '{cpu += $1}END{print cpu}'"], "attributeOnGateway": "CPU"}, {"timeout": 100, "command": ["/bin/sh", "-c", "free -m | grep Swap | awk '{print ($3/$2)*100}'"], "attributeOnGateway": "Memory"}, {"timeout": 100, "command": ["/bin/sh", "-c", "hostname -I"], "attributeOnGateway": "IP address"}, {"timeout": 100, "command": ["/bin/sh", "-c", "lsb_release -ds"], "attributeOnGateway": "OS"}, {"timeout": 100, "command": ["/bin/sh", "-c", "uptime"], "attributeOnGateway": "Uptime"}, {"timeout": 100, "command": ["/bin/sh", "-c", "lsusb"], "attributeOnGateway": "USBs"}]