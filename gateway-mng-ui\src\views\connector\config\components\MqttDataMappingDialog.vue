<template>
  <div class="mqtt-data-mapping-dialog">
    <el-form :model="mappingConfig" :rules="mappingRules" ref="mappingFormRef" label-width="140px">
      <!-- 基础配置 -->
      <div class="config-section">
        <div class="section-title">基础配置</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="映射名称" prop="name" required>
              <el-input v-model="mappingConfig.name" placeholder="温湿度传感器映射" />
              <div class="field-hint">为该映射配置一个便于识别的名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主题筛选" prop="topicFilter" required>
              <el-input v-model="mappingConfig.topicFilter" placeholder="sensor/+/data" />
              <div class="field-hint">MQTT主题过滤器，支持通配符 + 和 #</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="订阅QoS">
              <el-select v-model="mappingConfig.subscriptionQos" style="width: 100%">
                <el-option label="QoS 0 - 最多一次" :value="0" />
                <el-option label="QoS 1 - 至少一次" :value="1" />
                <el-option label="QoS 2 - 仅一次" :value="2" />
              </el-select>
              <div class="field-hint">MQTT消息质量服务等级</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用状态">
              <el-switch 
                v-model="mappingConfig.enabled" 
                active-text="启用" 
                inactive-text="禁用"
              />
              <div class="field-hint">是否启用此映射配置</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 转换器配置 -->
      <div class="config-section">
        <div class="section-title">转换器配置</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="转换器类型" prop="converter.type" required>
              <el-select v-model="mappingConfig.converter.type" style="width: 100%">
                <el-option label="JSON转换器" value="json" />
                <el-option label="字节转换器" value="bytes" />
                <el-option label="自定义转换器" value="custom" />
              </el-select>
              <div class="field-hint">选择消息转换器类型</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="仅数据变化时发送">
              <el-switch v-model="mappingConfig.converter.sendDataOnlyOnChange" />
              <div class="field-hint">只有在数据值发生变化时才发送到平台</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="超时时间(ms)">
              <el-input-number 
                v-model="mappingConfig.converter.timeout" 
                :min="1000" 
                :step="1000"
                :max="300000"
                style="width: 100%"
              />
              <div class="field-hint">消息处理超时时间</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 设备信息配置 -->
      <div class="config-section">
        <div class="section-title">设备信息配置</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="设备名称来源" prop="converter.deviceInfo.deviceNameExpressionSource">
              <el-select v-model="mappingConfig.converter.deviceInfo.deviceNameExpressionSource" style="width: 100%">
                <el-option label="消息内容" value="message" />
                <el-option label="主题" value="topic" />
                <el-option label="常量" value="constant" />
              </el-select>
              <div class="field-hint">设备名称的数据来源</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备名称表达式" prop="converter.deviceInfo.deviceNameExpression" required>
              <el-input v-model="mappingConfig.converter.deviceInfo.deviceNameExpression" placeholder="${serialNumber}" />
              <div class="field-hint">
                <span v-if="mappingConfig.converter.deviceInfo.deviceNameExpressionSource === 'message'">
                  从消息JSON中提取，如: ${serialNumber}
                </span>
                <span v-else-if="mappingConfig.converter.deviceInfo.deviceNameExpressionSource === 'topic'">
                  从主题中提取，如: ${topic[1]} (获取第二段)
                </span>
                <span v-else>
                  固定设备名称，如: TempSensor01
                </span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备类型来源">
              <el-select v-model="mappingConfig.converter.deviceInfo.deviceProfileExpressionSource" style="width: 100%">
                <el-option label="消息内容" value="message" />
                <el-option label="主题" value="topic" />
                <el-option label="常量" value="constant" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备类型表达式">
              <el-input v-model="mappingConfig.converter.deviceInfo.deviceProfileExpression" placeholder="Thermometer" />
              <div class="field-hint">设备类型名称或表达式</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 属性配置 -->
      <div class="config-section">
        <div class="section-title">属性配置</div>
        <MqttAttributeConfig 
          v-model="mappingConfig.converter.attributes" 
          title="设备属性"
          description="配置从MQTT消息中提取的设备属性"
        />
      </div>
      
      <!-- 时序数据配置 -->
      <div class="config-section">
        <div class="section-title">时序数据配置</div>
        <MqttTimeseriesConfig 
          v-model="mappingConfig.converter.timeseries" 
          title="遥测数据"
          description="配置从MQTT消息中提取的遥测数据"
        />
      </div>
    </el-form>

    <!-- 对话框底部按钮 -->
    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        {{ isEdit ? '更新' : '添加' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import MqttAttributeConfig from './MqttAttributeConfig.vue'
import MqttTimeseriesConfig from './MqttTimeseriesConfig.vue'

// Props
const props = defineProps({
  mappingConfig: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['save', 'cancel'])

// 响应式数据
const mappingFormRef = ref(null)
const saving = ref(false)

// 映射配置
const mappingConfig = reactive({
  name: '',
  topicFilter: 'sensor/+/data',
  subscriptionQos: 1,
  enabled: true,
  converter: {
    type: 'json',
    deviceInfo: {
      deviceNameExpressionSource: 'message',
      deviceNameExpression: '${serialNumber}',
      deviceProfileExpressionSource: 'constant',
      deviceProfileExpression: 'default'
    },
    sendDataOnlyOnChange: false,
    timeout: 60000,
    attributes: [],
    timeseries: []
  },
  ...props.mappingConfig
})

// 表单验证规则
const mappingRules = reactive({
  name: [
    { required: true, message: '请输入映射名称', trigger: 'blur' }
  ],
  topicFilter: [
    { required: true, message: '请输入主题筛选', trigger: 'blur' }
  ],
  'converter.type': [
    { required: true, message: '请选择转换器类型', trigger: 'change' }
  ],
  'converter.deviceInfo.deviceNameExpression': [
    { required: true, message: '请输入设备名称表达式', trigger: 'blur' }
  ]
})

// 方法
const handleSave = async () => {
  try {
    await mappingFormRef.value.validate()
    saving.value = true
    
    // 清理不需要的字段
    const configToSave = { ...mappingConfig }
    
    emit('save', configToSave)
    ElMessage.success(props.isEdit ? '映射配置已更新' : '映射配置已添加')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单配置')
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听外部配置变化
watch(() => props.mappingConfig, (newConfig) => {
  Object.assign(mappingConfig, {
    name: '',
    topicFilter: 'sensor/+/data',
    subscriptionQos: 1,
    enabled: true,
    converter: {
      type: 'json',
      deviceInfo: {
        deviceNameExpressionSource: 'message',
        deviceNameExpression: '${serialNumber}',
        deviceProfileExpressionSource: 'constant',
        deviceProfileExpression: 'default'
      },
      sendDataOnlyOnChange: false,
      timeout: 60000,
      attributes: [],
      timeseries: []
    },
    ...newConfig
  })
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
.mqtt-data-mapping-dialog {
  .config-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input), :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-switch) {
    --el-switch-on-color: #409eff;
  }
}
</style> 