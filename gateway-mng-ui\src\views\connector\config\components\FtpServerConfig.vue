<template>
  <div class="ftp-server-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>FTP服务器配置</span>
          <el-tooltip content="配置FTP服务器的连接参数、安全设置和认证方式" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="serverConfig" :rules="rules" label-width="140px" ref="formRef">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="主机地址" prop="host" required>
              <el-input 
                v-model="serverConfig.host" 
                placeholder="0.0.0.0"
                clearable
                
              />
              <div class="field-hint">FTP服务器的IP地址或域名</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="端口号" prop="port" required>
              <el-input-number 
                v-model="serverConfig.port" 
                :min="1" 
                :max="65535"
                :precision="0"
                controls-position="right"
                
              />
              <div class="field-hint">FTP服务端口，默认21</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="启用TLS加密">
              <el-switch 
                v-model="serverConfig.TLSSupport" 
                
              />
              <div class="field-hint">启用FTP over TLS/SSL安全连接</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 安全认证配置 -->
        <div class="security-section">
          <div class="section-title">安全认证</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="认证类型" prop="security.type" required>
                <el-select 
                  v-model="serverConfig.security.type" 
                  placeholder="请选择认证类型"
                  @change="handleSecurityTypeChange"
                >
                  <el-option 
                    label="匿名登录" 
                    value="anonymous"
                  />
                  <el-option 
                    label="用户名密码" 
                    value="basic"
                  />
                </el-select>
                <div class="field-hint">选择FTP服务器的认证方式</div>
              </el-form-item>
            </el-col>
            
            <template v-if="serverConfig.security.type === 'basic'">
              <el-col :span="8">
                <el-form-item label="用户名" prop="security.username" required>
                  <el-input 
                    v-model="serverConfig.security.username" 
                    placeholder="admin"
                    clearable
                    
                  />
                  <div class="field-hint">FTP服务器登录用户名</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="密码" prop="security.password" required>
                  <el-input 
                    v-model="serverConfig.security.password" 
                    type="password"
                    placeholder="admin"
                    show-password
                    clearable
                    
                  />
                  <div class="field-hint">FTP服务器登录密码</div>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </div>
        
        <!-- 连接测试 -->
        <div class="test-section">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-button 
                type="primary" 
                :loading="testing" 
                @click="testConnection"
                :disabled="!isConfigValid"
              >
                <el-icon><Connection /></el-icon>
                测试连接
              </el-button>
              <span class="test-hint">点击测试FTP服务器连接是否正常</span>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick, defineEmits, defineProps } from 'vue'
import { QuestionFilled, Connection } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      host: '0.0.0.0',
      port: 21,
      TLSSupport: false,
      security: {
        type: 'basic',
        username: 'admin',
        password: 'admin'
      }
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const testing = ref(false)

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 服务器配置数据
const serverConfig = reactive({
  host: '0.0.0.0',
  port: 21,
  TLSSupport: false,
  security: {
    type: 'basic',
    username: 'admin',
    password: 'admin'
  }
})

// 表单验证规则
const rules = reactive({
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' },
    { 
      pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^[a-zA-Z0-9.-]+$/,
      message: '请输入有效的IP地址或域名',
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号范围：1-65535', trigger: 'blur' }
  ],
  'security.type': [
    { required: true, message: '请选择认证类型', trigger: 'change' }
  ],
  'security.username': [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  'security.password': [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
})

// 检查配置是否有效
const isConfigValid = computed(() => {
  if (!serverConfig.host || !serverConfig.port) return false
  if (serverConfig.security.type === 'basic') {
    return !!(serverConfig.security.username && serverConfig.security.password)
  }
  return true
})

// 处理认证类型变化
const handleSecurityTypeChange = (type) => {
  if (type === 'anonymous') {
    // 匿名登录时清除用户名密码
    serverConfig.security.username = ''
    serverConfig.security.password = ''
  } else if (type === 'basic') {
    // 基础认证时设置默认值
    if (!serverConfig.security.username) {
      serverConfig.security.username = 'admin'
    }
    if (!serverConfig.security.password) {
      serverConfig.security.password = 'admin'
    }
  }
  handleChange()
}

// 测试连接
const testConnection = async () => {
  try {
    await formRef.value?.validate()
    testing.value = true
    
    // 模拟连接测试
    setTimeout(() => {
      testing.value = false
      ElMessage.success('FTP服务器连接测试成功！')
    }, 2000)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查配置参数')
  }
}

// 处理配置变化
const handleChange = () => {
  if (!isInternalUpdate.value) {
    emit('update:modelValue', { ...serverConfig })
  }
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value && newValue) {
    isInternalUpdate.value = true
    Object.assign(serverConfig, {
      host: newValue.host || '0.0.0.0',
      port: newValue.port || 21,
      TLSSupport: newValue.TLSSupport || false,
      security: {
        type: newValue.security?.type || 'basic',
        username: newValue.security?.username || 'admin',
        password: newValue.security?.password || 'admin'
      }
    })
    nextTick(() => {
      isInternalUpdate.value = false
    })
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(serverConfig, () => {
  handleChange()
}, { deep: true })
</script>

<style lang="scss" scoped>
.ftp-server-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .security-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f8f9fa;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
    }
  }

  .test-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f0f9ff;
    
    .test-hint {
      margin-left: 12px;
      font-size: 12px;
      color: #909399;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-select) {
    width: 100%;
  }
}
</style> 