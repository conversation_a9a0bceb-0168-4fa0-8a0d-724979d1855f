import request from '@/utils/request'

// 登录方法
export function login(name, password) {
  const data = {
    name,
    password
  }
  return request({
    url: '/gateway/v1/user/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法
// export function register(data) {
//   return request({
//     url: '/gateway/v1/user/register',
//     headers: {
//       isToken: false
//     },
//     method: 'post',
//     data: data
//   })
// }

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/gateway/v1/user/detail',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/gateway/v1/user/login_out',
    method: 'get'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}

// 修改密码
export function editPassword(data) {
  return request({
    url: '/gateway/v1/user/update_pwd',
    method: 'post',
    data: data
  })
}