<template>
  <el-card class="rest-attribute-update-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="card-title">
          <span>属性更新 {{ index + 1 }}</span>
          <el-tag :type="getMethodTagType(updateData.HTTPMethod)" size="small">
            {{ updateData.HTTPMethod }}
          </el-tag>
          <el-tag :type="updateData.SSLVerify ? 'success' : 'info'" size="small">
            {{ updateData.SSLVerify ? 'SSL验证' : '无SSL' }}
          </el-tag>
        </div>
        <el-button type="danger" size="small" @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>
    
    <el-form :model="updateData" label-width="120px">
      <!-- HTTP配置 -->
      <div class="section">
        <div class="section-title">HTTP配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="HTTP方法" required>
              <el-select v-model="updateData.HTTPMethod" @change="updateValue">
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="PATCH" value="PATCH" />
                <el-option label="GET" value="GET" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SSL验证">
              <el-switch v-model="updateData.SSLVerify" @change="updateValue" />
              <div class="field-hint">是否验证SSL证书</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="允许重定向">
              <el-switch v-model="updateData.allowRedirects" @change="updateValue" />
              <div class="field-hint">是否允许HTTP重定向</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        <RestHttpHeaders 
          v-model="updateData.httpHeaders" 
          @update:modelValue="updateValue"
        />
      </div>

      <!-- 安全配置 -->
      <div class="section">
        <div class="section-title">安全配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认证类型">
              <el-select v-model="updateData.security.type" @change="handleSecurityTypeChange">
                <el-option label="匿名访问" value="anonymous" />
                <el-option label="基本认证" value="basic" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="updateData.security.type === 'basic'">
            <el-form-item label="用户名">
              <el-input 
                v-model="updateData.security.username" 
                placeholder="username"
                @input="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="updateData.security.type === 'basic'">
            <el-form-item label="密码">
              <el-input 
                v-model="updateData.security.password" 
                type="password"
                placeholder="password"
                show-password
                @input="updateValue"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 请求配置 -->
      <div class="section">
        <div class="section-title">请求配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="超时时间(秒)">
              <el-input-number 
                v-model="updateData.timeout" 
                :min="0.1" 
                :max="300"
                :step="0.1"
                :precision="1"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
              <div class="field-hint">请求超时时间</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重试次数">
              <el-input-number 
                v-model="updateData.tries" 
                :min="1" 
                :max="10"
                :step="1"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
              <div class="field-hint">请求失败时的重试次数</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 过滤器配置 -->
      <div class="section">
        <div class="section-title">过滤器配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名过滤器" required>
              <el-input 
                v-model="updateData.deviceNameFilter" 
                placeholder=".*"
                @input="updateValue"
              />
              <div class="field-hint">设备名的正则表达式过滤器，如：SN.*</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="属性过滤器" required>
              <el-input 
                v-model="updateData.attributeFilter" 
                placeholder=".*"
                @input="updateValue"
              />
              <div class="field-hint">属性名的正则表达式过滤器</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表达式配置 -->
      <div class="section">
        <div class="section-title">表达式配置</div>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请求URL表达式" required>
              <el-input 
                v-model="updateData.requestUrlExpression" 
                placeholder="http://127.0.0.1:5001/"
                @input="updateValue"
              />
              <div class="field-hint">构建请求URL的表达式，可使用 ${deviceName}、${attributeKey} 等变量</div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="值表达式" required>
              <el-input 
                type="textarea"
                :rows="3"
                v-model="updateData.valueExpression" 
                placeholder='{"deviceName":"${deviceName}","${attributeKey}":"${attributeValue}"}'
                @input="updateValue"
              />
              <div class="field-hint">构建请求体的表达式，支持JSON格式，可使用设备名、属性键值等变量</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表达式帮助 -->
      <div class="section">
        <el-collapse size="small">
          <el-collapse-item name="expressions" title="表达式使用说明">
            <div class="help-content">
              <h4>可用变量</h4>
              <ul>
                <li><code>${deviceName}</code> - 设备名称</li>
                <li><code>${attributeKey}</code> - 属性键名</li>
                <li><code>${attributeValue}</code> - 属性值</li>
              </ul>
              
              <h4>URL表达式示例</h4>
              <ul>
                <li><code>http://127.0.0.1:5001/device/${deviceName}/attribute/${attributeKey}</code></li>
                <li><code>http://api.example.com/devices/${deviceName}/update</code></li>
              </ul>
              
              <h4>值表达式示例</h4>
              <pre><code>{
  "deviceName": "${deviceName}",
  "${attributeKey}": "${attributeValue}",
  "timestamp": "${timestamp}"
}</code></pre>
              
              <h4>工作流程</h4>
              <p>当IoTCloud中的设备属性发生更新时：</p>
              <ol>
                <li>检查设备名和属性名是否匹配过滤器</li>
                <li>使用URL表达式构建请求地址</li>
                <li>使用值表达式构建请求体</li>
                <li>发送HTTP请求到外部系统</li>
              </ol>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 配置预览 -->
      <div class="section" v-if="updateData.requestUrlExpression">
        <div class="section-title">配置预览</div>
        <div class="preview-content">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="HTTP方法">
              <el-tag :type="getMethodTagType(updateData.HTTPMethod)" size="small">
                {{ updateData.HTTPMethod }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="SSL验证">
              <el-tag :type="updateData.SSLVerify ? 'success' : 'info'" size="small">
                {{ updateData.SSLVerify ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="设备过滤器">
              <code>{{ updateData.deviceNameFilter }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="属性过滤器">
              <code>{{ updateData.attributeFilter }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="超时时间">
              {{ updateData.timeout }}秒
            </el-descriptions-item>
            <el-descriptions-item label="重试次数">
              {{ updateData.tries }}次
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import RestHttpHeaders from './RestHttpHeaders.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

// 更新数据
const updateData = computed({
  get: () => ({
    HTTPMethod: 'POST',
    SSLVerify: false,
    httpHeaders: {
      'CONTENT-TYPE': 'application/json'
    },
    security: {
      type: 'anonymous'
    },
    timeout: 0.5,
    tries: 3,
    allowRedirects: true,
    deviceNameFilter: '.*',
    attributeFilter: '.*',
    requestUrlExpression: '',
    valueExpression: '',
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 获取HTTP方法标签类型
const getMethodTagType = (method) => {
  const methodTagMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'PATCH': 'info'
  }
  return methodTagMap[method] || 'info'
}

// 安全类型变化处理
const handleSecurityTypeChange = () => {
  const newData = { ...updateData.value }
  if (newData.security.type === 'anonymous') {
    delete newData.security.username
    delete newData.security.password
  } else {
    newData.security.username = ''
    newData.security.password = ''
  }
  emit('update:modelValue', newData)
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...updateData.value })
}

// 删除配置
const handleDelete = () => {
  emit('delete')
}
</script>

<style lang="scss" scoped>
.rest-attribute-update-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .help-content {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
    }
    
    ul, ol {
      margin: 0 0 16px 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 4px;
        font-size: 13px;
        color: #606266;
        line-height: 1.5;
        
        code {
          background: #f5f7fa;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
          color: #e6a23c;
        }
      }
    }
    
    p {
      margin: 8px 0;
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
    }
    
    pre {
      margin: 8px 0;
      padding: 12px;
      background: #f6f8fa;
      border: 1px solid #e1e4e8;
      border-radius: 4px;
      overflow-x: auto;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
        font-size: 12px;
        line-height: 1.4;
        color: #24292e;
      }
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .el-collapse-item__content {
    padding: 12px 0 0 0;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style> 