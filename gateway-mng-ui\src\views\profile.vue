<template>
  <div class="base_box">
    <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="120">
      <el-row>
        <el-col :span="24" class="type-title">
          <div class="type-icon"></div>
          <div>修改密码</div>
          <div class="type-icon"></div>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户名" prop="name">
            {{ editForm.name }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="旧密码" prop="password">
            <el-input v-model="editForm.password" type="password" show-password />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="新密码" prop="new_password">
            <el-input v-model="editForm.new_password" placeholder="请输入新密码" type="password" show-password />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="确认密码" prop="confirm_password">
            <el-input v-model="editForm.confirm_password" placeholder="请确认新密码" type="password" show-password />
          </el-form-item>
        </el-col>
        <el-col :span="24" class="button-box">
          <el-button type="primary" @click="handleConfirm">确认</el-button>
          <el-button @click="handeleClose">取消</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getInfo, editPassword } from '@/api/login'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

const editFormRef = ref()
const editForm = ref({ name: '', password: '', new_password: '', confirm_password: '' })
const equalToPassword = (rule, value, callback) => {
  if (editForm.value.new_password !== value) {
    callback(new Error("两次输入的密码不一致"));
  } else {
    callback();
  }
};
const editRules = {
  new_password: [
    { required: true, trigger: "blur", message: "请输入您的密码" },
    { min: 5, max: 20, message: "用户密码长度必须介于 5 和 20 之间", trigger: "blur" }
  ],
  confirm_password: [
    { required: true, trigger: "blur", message: "请再次输入您的密码" },
    { required: true, validator: equalToPassword, trigger: "blur" }
  ]
};

onMounted(() => {
  getInfo().then(res => {
    editForm.value = res.data
  })
})

const handleConfirm = () => {
  editPassword(editForm.value).then(res => {
    if (res.msg === 'success') {
      ElMessage({ message: '修改成功', type: 'success', })
    } else {
      ElMessage.error(res.data)
    }
  })
}
const handeleClose = () => {
  router.push({ path: '/index' })
  editForm.value = { name: '', password: '', new_password: '', confirm_password: '' }
}

</script>

<style lang="scss" scoped>
.base_box {
  padding: 37px;
}

.type-title {
  height: 38px;
  background: #F3FAFF;
  font-size: 20px;
  color: #242932;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin: 41px 0 !important;

  .type-icon {
    width: 16px;
    height: 4px;
    background: #0078FF;
    border-radius: 2px;
    margin: 0 12px;
  }
}

.type-title:first-child {
  margin: 0;
}

.button-box {
  display: flex;
  justify-content: center;
}


:deep(.el-form .el-form-item__label) {
  font-weight: 400 !important;
  font-size: 14px;
  color: #868686;
}
</style>