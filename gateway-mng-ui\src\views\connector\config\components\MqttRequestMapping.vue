<template>
  <div class="mqtt-request-mapping">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <h4>请求映射配置</h4>
            <p class="description">配置设备连接、断开、属性请求、属性更新和RPC映射</p>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="border-card">
        <!-- 连接请求 -->
        <el-tab-pane label="连接请求" name="connectRequests">
          <MqttRequestMappingSection
            v-model="requestsMapping.connectRequests"
            request-type="connectRequests"
            title="连接请求映射"
            description="设备连接时的请求配置"
          />
        </el-tab-pane>

        <!-- 断开请求 -->
        <el-tab-pane label="断开请求" name="disconnectRequests">
          <MqttRequestMappingSection
            v-model="requestsMapping.disconnectRequests"
            request-type="disconnectRequests"
            title="断开请求映射"
            description="设备断开连接时的请求配置"
          />
        </el-tab-pane>

        <!-- 属性请求 -->
        <el-tab-pane label="属性请求" name="attributeRequests">
          <MqttRequestMappingSection
            v-model="requestsMapping.attributeRequests"
            request-type="attributeRequests"
            title="属性请求映射"
            description="获取设备属性的请求配置"
          />
        </el-tab-pane>

        <!-- 属性更新 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <MqttRequestMappingSection
            v-model="requestsMapping.attributeUpdates"
            request-type="attributeUpdates"
            title="属性更新映射"
            description="更新设备属性的配置"
          />
        </el-tab-pane>

        <!-- 服务端RPC -->
        <el-tab-pane label="服务端RPC" name="serverSideRpc">
          <MqttRequestMappingSection
            v-model="requestsMapping.serverSideRpc"
            request-type="serverSideRpc"
            title="服务端RPC映射"
            description="服务端远程过程调用配置"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps, nextTick } from 'vue'
import MqttRequestMappingSection from './MqttRequestMappingSection.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      connectRequests: [],
      disconnectRequests: [],
      attributeRequests: [],
      attributeUpdates: [],
      serverSideRpc: []
    })
  }
})

const emit = defineEmits(['update:modelValue'])

// 标准的isInternalUpdate模式
const isInternalUpdate = ref(false)

const activeTab = ref('connectRequests')
const requestsMapping = reactive({
  connectRequests: [],
  disconnectRequests: [],
  attributeRequests: [],
  attributeUpdates: [],
  serverSideRpc: []
})

// 监听props变化 - 标准模式
watch(() => props.modelValue, (newValue) => {
  if (isInternalUpdate.value) return
  
  if (newValue && typeof newValue === 'object') {
    Object.assign(requestsMapping, {
      connectRequests: [],
      disconnectRequests: [],
      attributeRequests: [],
      attributeUpdates: [],
      serverSideRpc: [],
      ...newValue
    })
  }
}, { deep: true, immediate: true })

// 监听请求映射变化 - 标准模式
watch(requestsMapping, (newMapping) => {
  isInternalUpdate.value = true
  nextTick(() => {
    isInternalUpdate.value = false
  })
  
  emit('update:modelValue', { ...newMapping })
}, { deep: true })
</script>

<style lang="scss" scoped>
.mqtt-request-mapping {
  .card-header {
    .header-info {
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .description {
        margin: 0;
        font-size: 13px;
        color: #606266;
      }
    }
  }
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style> 