<template>
  <div class="socket-config">
    <el-tabs v-model="activeTab" class="socket-tabs" v-if="configInitialized">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- Socket配置标签页 -->
      <el-tab-pane label="Socket配置" name="socket">
        <SocketServerConfig 
          v-model="config.socket" 
          v-if="config.socket"
        />
      </el-tab-pane>

      <!-- 设备配置标签页 -->
      <el-tab-pane label="设备配置" name="devices">
        <SocketDevicesConfig 
          v-model="config.devices" 
          v-if="config.devices"
        />
      </el-tab-pane>
    </el-tabs>
    <div v-else class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import SocketServerConfig from './components/SocketServerConfig.vue'
import SocketDevicesConfig from './components/SocketDevicesConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')
const configInitialized = ref(false)

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  socket: {
    type: 'TCP',
    address: '127.0.0.1',
    port: 50000,
    bufferSize: 1024
  },
  devices: []
}

// 配置数据结构 - 使用ref而不是reactive
const config = ref({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config.value, 'socket', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config.value)
      
      // 标记配置已初始化
      configInitialized.value = true
        
      console.log('Socket 连接器配置初始化成功')
    } else {
      ElMessage.warning('Socket 连接器配置初始化部分失败，请检查配置')
      configInitialized.value = true // 即使失败也要显示界面
    }
  } catch (error) {
    console.error('Socket 连接器初始化失败:', error)
    ElMessage.error('Socket 连接器初始化失败')
    configInitialized.value = true // 即使失败也要显示界面
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  socket: config
})
</script>

<style lang="scss" scoped>
.socket-config {
  .socket-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
  
  .loading-state {
    padding: 20px;
  }
}
</style>