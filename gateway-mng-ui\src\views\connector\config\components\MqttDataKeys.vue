<template>
  <div class="mqtt-data-keys">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <h4>{{ title }}</h4>
            <p class="description">{{ description }}</p>
          </div>
          <el-button type="primary" size="small" @click="addDataKey">
            <el-icon><Plus /></el-icon>
            添加{{ dataType === 'attributes' ? '属性' : '遥测' }}
          </el-button>
        </div>
      </template>

      <div v-if="dataKeys.length === 0" class="empty-state">
        <el-empty :description="`暂无${dataType === 'attributes' ? '属性' : '遥测'}配置`">
          <el-button type="primary" @click="addDataKey">
            添加第一个{{ dataType === 'attributes' ? '属性' : '遥测' }}
          </el-button>
        </el-empty>
      </div>

      <div v-else class="data-keys-list">
        <div 
          v-for="(dataKey, index) in dataKeys" 
          :key="index"
          class="data-key-item"
        >
          <el-card shadow="hover">
            <template #header>
              <div class="data-key-header">
                <div class="data-key-info">
                  <span class="key-name">{{ dataKey.key || '未设置键名' }}</span>
                  <el-tag :type="getTypeTagType(dataKey.type)" size="small">
                    {{ getTypeLabel(dataKey.type) }}
                  </el-tag>
                </div>
                <el-button 
                  type="danger" 
                  size="small" 
                  link
                  @click="removeDataKey(index)"
                >
                  <el-icon><Delete /></el-icon>删除
                </el-button>
              </div>
            </template>
            
            <el-form :model="dataKey" label-width="100px" size="small">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="键名" required>
                    <el-input 
                      v-model="dataKey.key" 
                      :placeholder="dataType === 'attributes' ? 'model' : 'temperature'"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="数据类型" required>
                    <el-select v-model="dataKey.type" style="width: 100%">
                      <el-option label="字符串" value="string" />
                      <el-option label="整数" value="int" />
                      <el-option label="浮点数" value="double" />
                      <el-option label="布尔值" value="bool" />
                      <el-option label="原始数据" value="raw" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="值表达式" required>
                    <el-input 
                      v-model="dataKey.value" 
                      :placeholder="getValuePlaceholder()"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries'].includes(value)
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  converterType: {
    type: String,
    default: 'json'
  }
})

const emit = defineEmits(['update:modelValue'])

const dataKeys = ref([])

// 数据类型标签颜色
const getTypeTagType = (type) => {
  const typeMap = {
    string: '',
    int: 'success',
    double: 'warning',
    bool: 'info',
    raw: 'danger'
  }
  return typeMap[type] || ''
}

// 数据类型标签
const getTypeLabel = (type) => {
  const typeMap = {
    string: '字符串',
    int: '整数',
    double: '浮点数',
    bool: '布尔值',
    raw: '原始数据'
  }
  return typeMap[type] || type
}

// 获取值表达式占位符
const getValuePlaceholder = () => {
  if (props.converterType === 'bytes') {
    return props.dataType === 'attributes' ? '[0:4]' : '[4:8]'
  } else {
    return props.dataType === 'attributes' ? '${model}' : '${temperature}'
  }
}

// 添加数据键
const addDataKey = () => {
  const newDataKey = {
    key: '',
    type: 'string',
    value: ''
  }
  dataKeys.value.push(newDataKey)
  ElMessage.success(`${props.dataType === 'attributes' ? '属性' : '遥测'}添加成功`)
}

// 移除数据键
const removeDataKey = (index) => {
  dataKeys.value.splice(index, 1)
  ElMessage.success(`${props.dataType === 'attributes' ? '属性' : '遥测'}删除成功`)
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  dataKeys.value = [...(newValue || [])]
}, { deep: true, immediate: true })

// 监听数据键变化
watch(dataKeys, (newDataKeys) => {
  emit('update:modelValue', [...newDataKeys])
}, { deep: true })
</script>

<style lang="scss" scoped>
.mqtt-data-keys {
  .card-header {
    .header-info {
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .description {
        margin: 0;
        font-size: 13px;
        color: #606266;
      }
    }
    
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
  }

  .data-keys-list {
    .data-key-item {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .data-key-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .data-key-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .key-name {
            font-weight: 600;
            color: #303133;
          }
        }
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style> 