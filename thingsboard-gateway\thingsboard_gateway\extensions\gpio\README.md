# GPIO Connector for ThingsBoard Gateway

## 概述

GPIO连接器允许ThingsBoard Gateway通过GPIO引脚与外部设备进行通信。它支持读取和控制GPIO引脚状态，适用于树莓派、工业计算机等具有GPIO接口的设备。

## 特性

- 支持多个GPIO引脚的并行控制
- 灵活的引脚模式配置（输入/输出）
- 可配置的上拉下拉电阻
- 实时状态监控和轮询
- 支持遥测数据、属性、属性更新和RPC
- 多种数据类型转换
- 线程安全的GPIO操作

## 系统要求

### 硬件要求
- 支持GPIO的设备（如树莓派、工业计算机等）
- 正确连接的GPIO外设

### 软件要求
- Linux操作系统
- WiringPi库（用于GPIO操作）
- Python 3.7+
- ThingsBoard Gateway

### 安装WiringPi
```bash
# 在树莓派上安装WiringPi
sudo apt update
sudo apt install wiringpi

# 验证安装
gpio -v
gpio readall
```

## 配置说明

### 基础配置

```json
{
  "name": "GPIO Connector",
  "logLevel": "INFO",
  "enableRemoteLogging": false,
  "id": "87b5a8f4-82c1-4b5d-a9e3-f7c2d4e6a8b1",
  "uplinkQueueSize": 100000,
  "devices": [...]
}
```

### 设备配置

```json
{
  "name": "GPIO Device",
  "deviceName": "GPIO Device",
  "deviceType": "gpio_controller",
  "type": "gpio",
  "converter": "GpioUplinkConverter",
  "downlink_converter": "GpioDownlinkConverter",
  "gpioPins": [17, 18, 19, 20],
  "pinMode": "out",
  "pullUpDown": "off",
  "pollingPeriod": 1000,
  "telemetry": [...],
  "attributes": [...],
  "attributeUpdates": [...],
  "serverSideRpc": [...]
}
```

#### 配置参数说明

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `name` | string | 是 | - | 设备名称 |
| `deviceName` | string | 否 | name值 | ThingsBoard中显示的设备名 |
| `deviceType` | string | 否 | "default" | 设备类型 |
| `converter` | string | 是 | - | 上行数据转换器 |
| `downlink_converter` | string | 否 | - | 下行数据转换器 |
| `gpioPins` | array | 是 | - | 使用的GPIO引脚列表 |
| `pinMode` | string | 否 | "out" | 引脚模式：in（输入）/out（输出） |
| `pullUpDown` | string | 否 | "off" | 上拉下拉配置：off/up/down |
| `pollingPeriod` | number | 否 | 1000 | 轮询周期（毫秒） |

### 遥测配置

```json
{
  "telemetry": [
    {
      "key": "gpio17_state",
      "type": "bool",
      "pin": 17
    },
    {
      "key": "all_pins_bitmap",
      "type": "hex"
    }
  ]
}
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `key` | string | 是 | 遥测数据键名 |
| `type` | string | 否 | 数据类型：int/float/bool/string/hex/binary |
| `pin` | number | 否 | 特定GPIO引脚，不指定则使用所有引脚的位图 |

### 属性配置

```json
{
  "attributes": [
    {
      "key": "gpio_mode",
      "type": "string",
      "pin": 17
    }
  ]
}
```

### 属性更新配置

```json
{
  "attributeUpdates": [
    {
      "attributeOnPlatform": "led_control",
      "pin": 17,
      "valueMapping": "0->OFF,1->ON"
    }
  ]
}
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `attributeOnPlatform` | string | 是 | 平台上的属性名 |
| `pin` | number | 是 | 要控制的GPIO引脚 |
| `valueMapping` | string | 否 | 值映射规则 |

### RPC配置

```json
{
  "serverSideRpc": [
    {
      "method": "setGpioPin",
      "type": "int",
      "pin": 17,
      "withResponse": true,
      "responseTimeoutSec": 5
    }
  ]
}
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `method` | string | 是 | RPC方法名 |
| `type` | string | 否 | 参数类型 |
| `pin` | number | 否 | 要控制的GPIO引脚 |
| `withResponse` | boolean | 否 | 是否返回响应 |
| `responseTimeoutSec` | number | 否 | 响应超时时间 |

## 使用示例

### 1. LED控制示例

```json
{
  "name": "LED Controller",
  "gpioPins": [17],
  "pinMode": "out",
  "telemetry": [
    {
      "key": "led_status",
      "type": "bool",
      "pin": 17
    }
  ],
  "attributeUpdates": [
    {
      "attributeOnPlatform": "led_control",
      "pin": 17
    }
  ],
  "serverSideRpc": [
    {
      "method": "toggleLed",
      "type": "bool",
      "pin": 17,
      "withResponse": true
    }
  ]
}
```

### 2. 按钮输入示例

```json
{
  "name": "Button Input",
  "gpioPins": [21, 22],
  "pinMode": "in",
  "pullUpDown": "up",
  "pollingPeriod": 100,
  "telemetry": [
    {
      "key": "button1_pressed",
      "type": "bool",
      "pin": 21
    },
    {
      "key": "button2_pressed",
      "type": "bool",
      "pin": 22
    }
  ]
}
```

### 3. 混合输入输出示例

```json
{
  "name": "Mixed GPIO",
  "gpioPins": [17, 18, 21, 22],
  "pinMode": "out",
  "telemetry": [
    {
      "key": "output_status",
      "type": "hex"
    }
  ],
  "serverSideRpc": [
    {
      "method": "setPinValue",
      "type": "int",
      "withResponse": true
    }
  ]
}
```

## RPC调用示例

### 设置GPIO引脚
```javascript
// ThingsBoard Rule Engine或前端调用
{
  "method": "setGpioPin",
  "params": {
    "pin": 17,
    "value": 1
  }
}
```

### 读取所有引脚状态
```javascript
{
  "method": "readAllPins",
  "params": {}
}
```

### 切换引脚状态
```javascript
{
  "method": "togglePin",
  "params": {
    "pin": 18
  }
}
```

## 错误处理

常见错误及解决方案：

1. **GPIO权限错误**
   ```bash
   sudo usermod -a -G gpio $USER
   sudo chmod 666 /dev/gpiomem
   ```

2. **WiringPi未安装**
   ```bash
   sudo apt install wiringpi
   ```

3. **引脚占用冲突**
   - 检查其他程序是否使用了相同的GPIO引脚
   - 修改配置使用不同的引脚

4. **引脚模式错误**
   - 确保引脚模式与实际硬件连接匹配
   - 输入引脚需要正确配置上拉下拉电阻

## 最佳实践

1. **引脚规划**
   - 避免使用系统保留的GPIO引脚
   - 为不同功能的引脚分组管理

2. **轮询周期优化**
   - 对于快速变化的信号，使用较短的轮询周期
   - 对于状态稳定的设备，使用较长的轮询周期以节省资源

3. **错误监控**
   - 启用适当的日志级别进行调试
   - 监控GPIO设备的连接状态

4. **安全考虑**
   - 为关键的输出引脚实现安全检查
   - 使用属性更新而非直接RPC进行重要操作

## 故障排除

### 查看GPIO状态
```bash
gpio readall
```

### 测试GPIO引脚
```bash
# 设置引脚为输出模式
gpio mode 17 out

# 设置引脚输出高电平
gpio write 17 1

# 读取引脚状态
gpio read 17
```

### 查看连接器日志
```bash
tail -f /var/log/thingsboard-gateway/connector.log | grep GPIO
```

## 版本兼容性

- ThingsBoard Gateway 3.7.4+
- Python 3.7+
- WiringPi (最新版本)

## 支持

如需帮助，请：
1. 查看ThingsBoard Gateway文档
2. 检查GitHub Issues
3. 联系技术支持团队 