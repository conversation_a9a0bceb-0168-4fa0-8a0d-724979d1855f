<template>
  <div class="gateway-grpc-config">
    <el-form :model="config" :rules="rules" ref="formRef" label-width="140px">
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>GRPC服务配置</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="启用GRPC">
              <el-switch 
                v-model="config.enabled"
                @change="handleChange"
              />
              <div class="field-hint">启用GRPC服务接口</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="服务端口" prop="serverPort" required>
              <el-input-number 
                v-model="config.serverPort" 
                :min="1" 
                :max="65535"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">GRPC服务监听端口</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="心跳间隔(毫秒)" prop="keepAliveTimeMs">
              <el-input-number 
                v-model="config.keepAliveTimeMs" 
                :min="1000"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">心跳包发送间隔</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const config = ref({
  enabled: false,
  serverPort: 9595,
  keepAliveTimeMs: 10001,
  keepAliveTimeoutMs: 5000,
  keepAlivePermitWithoutCalls: true,
  maxPingsWithoutData: 0,
  minTimeBetweenPingsMs: 10000,
  minPingIntervalWithoutDataMs: 5000
})

const rules = {
  serverPort: [
    { required: true, message: '请输入服务端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围 1-65535', trigger: 'blur' }
  ]
}

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(config.value, newValue)
  }
}, { deep: true, immediate: true })

watch(config, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
  emit('change', { ...newConfig })
}, { deep: true })

const handleChange = () => {
  formRef.value?.validateField(['serverPort'])
}

defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.gateway-grpc-config {
  .config-card {
    border: 1px solid #e4e7ed;
    
    .card-header {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
  
  :deep(.el-card__body) {
    padding: 24px;
  }
}
</style> 