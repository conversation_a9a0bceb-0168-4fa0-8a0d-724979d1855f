<template>
  <div class="opcua-mapping-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>设备映射配置</span>
          <el-button type="primary" size="small" @click="handleAddMapping">
            <el-icon><Plus /></el-icon>
            添加映射
          </el-button>
        </div>
      </template>
      
      <!-- 映射列表 -->
      <div v-if="mappings.length === 0" class="empty-state">
        <el-empty description="暂无设备映射配置">
          <el-button type="primary" @click="handleAddMapping">添加第一个映射</el-button>
        </el-empty>
      </div>
      
      <div v-else class="mappings-grid">
        <div 
          v-for="(mapping, index) in mappings" 
          :key="index" 
          class="mapping-card"
        >
          <el-card shadow="hover">
            <template #header>
              <div class="mapping-header">
                <div class="mapping-info">
                  <h4 class="mapping-name">{{ getMappingDisplayName(mapping) }}</h4>
                  <span class="mapping-type">{{ getMappingDeviceInfo(mapping) }}</span>
                </div>
                <div class="mapping-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    link 
                    @click="handleEditMapping(mapping, index)"
                  >
                    编辑
                  </el-button>
                  <el-button 
                    type="danger" 
                    size="small" 
                    link 
                    @click="handleDeleteMapping(index)"
                  >
                    删除
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="mapping-details">
              <div class="detail-item">
                <span class="label">设备节点:</span>
                <span class="value">{{ mapping.deviceNodePattern || 'N/A' }}</span>
              </div>
              <div class="detail-item">
                <span class="label">节点源类型:</span>
                <span class="value">{{ getSourceTypeDisplay(mapping.deviceNodeSource) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">属性数量:</span>
                <span class="value">{{ (mapping.attributes || []).length }}</span>
              </div>
              <div class="detail-item">
                <span class="label">遥测数量:</span>
                <span class="value">{{ (mapping.timeseries || []).length }}</span>
              </div>
              <div class="detail-item">
                <span class="label">RPC数量:</span>
                <span class="value">{{ (mapping.rpc_methods || []).length }}</span>
              </div>
              <div class="detail-item">
                <span class="label">属性更新数量:</span>
                <span class="value">{{ (mapping.attributes_updates || []).length }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>

    <!-- 映射配置对话框 -->
    <OpcuaMappingDialog
      v-model="dialogVisible"
      :mapping="currentMapping"
      :is-edit="isEdit"
      @save="handleSaveMapping"
      @cancel="handleCancelMapping"
    />
  </div>
</template>

<script setup>
import { ref, watch, nextTick, defineEmits, defineProps } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import OpcuaMappingDialog from './OpcuaMappingDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const mappings = ref([])
const dialogVisible = ref(false)
const currentMapping = ref(null)
const currentIndex = ref(-1)
const isEdit = ref(false)

// 源类型映射
const sourceTypeMap = {
  path: '路径',
  identifier: '标识符'
}

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value) {
    mappings.value = [...(newValue || [])]
  }
}, { deep: true, immediate: true })

// 监听映射列表变化
watch(mappings, (newMappings) => {
  isInternalUpdate.value = true
  emit('update:modelValue', [...newMappings])
  // 在下一个tick重置标志
  nextTick(() => {
    isInternalUpdate.value = false
  })
}, { deep: true })

// 获取映射显示名称
const getMappingDisplayName = (mapping) => {
  return mapping.deviceInfo?.deviceNameExpression || 'Unknown Device'
}

// 获取映射设备信息
const getMappingDeviceInfo = (mapping) => {
  return mapping.deviceInfo?.deviceProfileExpression || 'default'
}

// 获取源类型显示名称
const getSourceTypeDisplay = (sourceType) => {
  return sourceTypeMap[sourceType] || sourceType || 'path'
}

// 添加映射
const handleAddMapping = () => {
  currentMapping.value = createDefaultMapping()
  currentIndex.value = -1
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑映射
const handleEditMapping = (mapping, index) => {
  currentMapping.value = { ...mapping }
  currentIndex.value = index
  isEdit.value = true
  dialogVisible.value = true
}

// 删除映射
const handleDeleteMapping = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个设备映射配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    mappings.value.splice(index, 1)
    ElMessage.success('映射删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存映射
const handleSaveMapping = (mappingData) => {
  if (isEdit.value) {
    // 编辑模式
    mappings.value[currentIndex.value] = { ...mappingData }
    ElMessage.success('映射配置更新成功')
  } else {
    // 添加模式
    mappings.value.push({ ...mappingData })
    ElMessage.success('映射添加成功')
  }
  
  dialogVisible.value = false
  currentMapping.value = null
  currentIndex.value = -1
}

// 取消映射配置
const handleCancelMapping = () => {
  dialogVisible.value = false
  currentMapping.value = null
  currentIndex.value = -1
}

// 创建默认映射配置
const createDefaultMapping = () => {
  return {
    deviceNodePattern: 'Root\\.Objects\\.Device1',
    deviceNodeSource: 'path',
    deviceInfo: {
      deviceNameExpression: 'Device ${Root\\.Objects\\.Device1\\.serialNumber}',
      deviceNameExpressionSource: 'path',
      deviceProfileExpression: 'Device',
      deviceProfileExpressionSource: 'constant'
    },
    attributes: [],
    timeseries: [],
    rpc_methods: [],
    attributes_updates: []
  }
}
</script>

<style lang="scss" scoped>
.opcua-mapping-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
  
  .mappings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    
    .mapping-card {
      .mapping-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .mapping-info {
          flex: 1;
          
          .mapping-name {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            word-break: break-word;
          }
          
          .mapping-type {
            font-size: 12px;
            color: #909399;
            background: #f5f7fa;
            padding: 2px 8px;
            border-radius: 12px;
          }
        }
        
        .mapping-actions {
          display: flex;
          gap: 8px;
          margin-left: 12px;
        }
      }
      
      .mapping-details {
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f5f7fa;
          
          &:last-child {
            border-bottom: none;
          }
          
          .label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 600;
            text-align: right;
            max-width: 60%;
            word-break: break-word;
          }
        }
      }
    }
  }
  
  :deep(.el-card) {
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
  
  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f2f5;
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .mappings-grid {
    grid-template-columns: 1fr;
  }
}
</style> 