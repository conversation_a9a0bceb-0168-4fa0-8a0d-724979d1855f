<template>
  <div class="can-backend-config">
    <!-- SocketCAN 配置 -->
    <div v-if="interfaceType === 'socketcan'">
      <el-form :model="backendConfig" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="启用FD模式">
              <el-switch v-model="backendConfig.fd"  />
              <div class="field-hint">启用CAN FD (Flexible Data-Rate) 支持</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- CANalyst-II 配置 -->
    <div v-else-if="interfaceType === 'canalystii'">
      <el-form :model="backendConfig" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="设备编号">
              <el-input-number 
                v-model="backendConfig.device" 
                :min="0" 
                :max="255"
                controls-position="right"
                
              />
              <div class="field-hint">CANalyst设备编号</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="硬件ID">
              <el-input-number 
                v-model="backendConfig.unique_hardware_id" 
                :min="0"
                controls-position="right"
                
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="串口">
              <el-input 
                v-model="backendConfig.serial" 
                placeholder="COM1"
                
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发送缓冲区大小">
              <el-input-number 
                v-model="backendConfig.tx_buffer_entries" 
                :min="1024" 
                :step="1024"
                controls-position="right"
                
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接收缓冲区大小">
              <el-input-number 
                v-model="backendConfig.rx_buffer_entries" 
                :min="1024" 
                :step="1024"
                controls-position="right"
                
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="DLL文件">
              <el-input 
                v-model="backendConfig.dll" 
                placeholder="usb2can.dll"
                
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- Kvaser 配置 -->
    <div v-else-if="interfaceType === 'kvaser'">
      <el-form :model="backendConfig" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="通道编号">
              <el-input-number 
                v-model="backendConfig.channel" 
                :min="0" 
                :max="7"
                controls-position="right"
                
              />
              <div class="field-hint">Kvaser设备的通道编号</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="比特率">
              <el-select v-model="backendConfig.bitrate" >
                <el-option label="125 kbit/s" :value="125000" />
                <el-option label="250 kbit/s" :value="250000" />
                <el-option label="500 kbit/s" :value="500000" />
                <el-option label="1 Mbit/s" :value="1000000" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- PCAN 配置 -->
    <div v-else-if="interfaceType === 'pcan'">
      <el-form :model="backendConfig" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备">
              <el-select v-model="backendConfig.device" >
                <el-option label="PCAN_USBBUS1" value="PCAN_USBBUS1" />
                <el-option label="PCAN_USBBUS2" value="PCAN_USBBUS2" />
                <el-option label="PCAN_USBBUS3" value="PCAN_USBBUS3" />
                <el-option label="PCAN_USBBUS4" value="PCAN_USBBUS4" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="比特率">
              <el-select v-model="backendConfig.bitrate" >
                <el-option label="125 kbit/s" :value="125000" />
                <el-option label="250 kbit/s" :value="250000" />
                <el-option label="500 kbit/s" :value="500000" />
                <el-option label="1 Mbit/s" :value="1000000" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- Vector 配置 -->
    <div v-else-if="interfaceType === 'vector'">
      <el-form :model="backendConfig" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="应用名称">
              <el-input 
                v-model="backendConfig.app_name" 
                placeholder="CANoe"
                
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通道编号">
              <el-input-number 
                v-model="backendConfig.channel" 
                :min="0" 
                :max="15"
                controls-position="right"
                
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="比特率">
              <el-select v-model="backendConfig.bitrate" >
                <el-option label="125 kbit/s" :value="125000" />
                <el-option label="250 kbit/s" :value="250000" />
                <el-option label="500 kbit/s" :value="500000" />
                <el-option label="1 Mbit/s" :value="1000000" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- Serial 配置 -->
    <div v-else-if="interfaceType === 'serial'">
      <el-form :model="backendConfig" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="串口设备">
              <el-input 
                v-model="backendConfig.port" 
                placeholder="/dev/ttyUSB0"
                
              />
              <div class="field-hint">串口设备路径</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="波特率">
              <el-select v-model="backendConfig.baudrate" >
                <el-option label="9600" :value="9600" />
                <el-option label="19200" :value="19200" />
                <el-option label="38400" :value="38400" />
                <el-option label="57600" :value="57600" />
                <el-option label="115200" :value="115200" />
                <el-option label="230400" :value="230400" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- Virtual 配置 -->
    <div v-else-if="interfaceType === 'virtual'">
      <el-alert
        title="虚拟CAN接口"
        type="info"
        description="虚拟CAN接口不需要额外配置，主要用于测试和仿真场景。"
        show-icon
        :closable="false"
      />
    </div>

    <!-- 其他接口类型的通用配置 -->
    <div v-else>
      <el-form :model="backendConfig" label-width="140px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="自定义配置">
              <el-input 
                v-model="customConfigStr" 
                type="textarea" 
                :rows="4"
                placeholder="请输入JSON格式的后端配置"
                @change="handleCustomConfigChange"
              />
              <div class="field-hint">请根据选择的接口类型输入相应的配置参数</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  interfaceType: {
    type: String,
    required: true,
    default: 'socketcan'
  },
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 确保数值字段为数字类型
const ensureNumberTypes = (config) => {
  const result = { ...config }
  
  // 转换可能的数值字段
  const numberFields = ['device', 'unique_hardware_id', 'tx_buffer_entries', 'rx_buffer_entries', 
                       'channel', 'bitrate', 'baudrate']
  
  numberFields.forEach(field => {
    if (result[field] !== undefined) {
      result[field] = typeof result[field] === 'string' ? parseInt(result[field]) || 0 : result[field]
    }
  })
  
  return result
}

// 后端配置数据
const backendConfig = reactive({})

// 自定义配置字符串
const customConfigStr = ref('')

// 初始化默认配置
const initDefaultConfig = (interfaceType) => {
  const defaultConfigs = {
    socketcan: { fd: true },
    canalystii: { 
      device: 0, 
      kwargs: {}, 
      unique_hardware_id: 0, 
      serial: 'COM1', 
      tx_buffer_entries: 4096, 
      rx_buffer_entries: 4096, 
      dll: 'usb2can.dll' 
    },
    kvaser: { channel: 0, bitrate: 500000 },
    pcan: { device: 'PCAN_USBBUS1', bitrate: 500000 },
    vector: { app_name: 'CANoe', channel: 0, bitrate: 500000 },
    virtual: {},
    serial: { port: '/dev/ttyUSB0', baudrate: 115200 }
  }
  
  // 只有在当前配置为空时才设置默认值
  if (Object.keys(backendConfig).length === 0) {
    Object.assign(backendConfig, defaultConfigs[interfaceType] || {})
  }
  
  updateCustomConfigStr()
}

// 更新自定义配置字符串
const updateCustomConfigStr = () => {
  try {
    customConfigStr.value = JSON.stringify(backendConfig, null, 2)
  } catch (error) {
    console.error('序列化配置失败:', error)
  }
}

// 处理自定义配置变化
const handleCustomConfigChange = () => {
  try {
    const parsed = JSON.parse(customConfigStr.value)
    Object.assign(backendConfig, parsed)
    handleChange()
  } catch (error) {
    ElMessage.error('配置格式错误，请检查JSON格式')
  }
}

// 处理配置变化
const handleChange = () => {
  emit('update:modelValue', { ...backendConfig })
}

// 监听接口类型变化，初始化默认配置
watch(() => props.interfaceType, (newType) => {
  initDefaultConfig(newType)
}, { immediate: true })

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value && newValue) {
    const normalizedValue = ensureNumberTypes(newValue)
    Object.assign(backendConfig, normalizedValue)
    updateCustomConfigStr()
  }
}, { deep: true, immediate: true })

// 监听后端配置变化
watch(backendConfig, (newValue) => {
  isInternalUpdate.value = true
  emit('update:modelValue', { ...newValue })
  updateCustomConfigStr()
  nextTick(() => {
    isInternalUpdate.value = false
  })
}, { deep: true })
</script>

<style lang="scss" scoped>
.can-backend-config {
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-alert) {
    margin: 20px 0;
  }
}
</style> 