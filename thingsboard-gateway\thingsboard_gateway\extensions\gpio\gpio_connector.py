#     Copyright 2024. ThingsBoard
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.

"""Import libraries"""

import time
import subprocess
from random import choice
from string import ascii_lowercase
from threading import Thread, Event, Lock
from thingsboard_gateway.tb_utility.tb_utility import TBUtility
from queue import Queue
from typing import List, TYPE_CHECKING
from time import monotonic, sleep

from thingsboard_gateway.connectors.connector import Connector  # Import base class for connector and logger
from thingsboard_gateway.tb_utility.tb_loader import TBModuleLoader
from thingsboard_gateway.tb_utility.tb_logger import init_logger

if TYPE_CHECKING:
    #  necessary for type checking to avoid circular import
    from thingsboard_gateway.gateway.tb_gateway_service import TBGatewayService


class GpioDevice(Thread):
    """
    GPIO device class is used to represent a device that is connected to GPIO pins.
    It is used to read data from GPIO pins and send it to the platform.
    """
    def __init__(self, device_config, uplink_converter, stop_event: Event, logger, uplink_queue):
        super().__init__()
        self.__log = logger
        self.uplink_queue = uplink_queue
        self.daemon = True
        self.stopped = True
        self.__connector_stopped = stop_event
        self.config = device_config
        self.name = self.config.get('deviceName', self.config.get('name', 'GpioDevice'))
        self.type = self.config.get('deviceType', self.config.get('type', 'default'))
        self.uplink_converter = uplink_converter
        self.downlink_converter = None
        self.__rpc_in_progress = Event()
        self.__previous_connect = 0
        
        # GPIO配置
        self.gpio_pins = self.config.get('gpioPins', [17, 18, 19, 20])
        self.polling_period = self.config.get('pollingPeriod', 1000) / 1000.0  # 转换为秒
        self.pin_mode = self.config.get('pinMode', 'out')  # 'in' or 'out'
        self.pull_up_down = self.config.get('pullUpDown', 'off')  # 'up', 'down', 'off'
        
        self.__gpio_lock = Lock()
        self.__last_gpio_state = {}
        
        # 初始化GPIO
        self.__init_gpio()

    def __init_gpio(self):
        """初始化GPIO引脚"""
        try:
            for pin in self.gpio_pins:
                # 设置引脚模式
                subprocess.run(['gpio', 'mode', str(pin), self.pin_mode], 
                             capture_output=True, text=True, check=True)
                
                # 设置上拉下拉电阻（仅在输入模式下有效）
                if self.pin_mode == 'in' and self.pull_up_down != 'off':
                    subprocess.run(['gpio', 'mode', str(pin), self.pull_up_down], 
                                 capture_output=True, text=True, check=True)
                
                # 初始化状态
                self.__last_gpio_state[pin] = 0
                
            self.__log.info("GPIO pins initialized for device %s: %s", self.name, self.gpio_pins)
        except Exception as e:
            self.__log.error("Failed to initialize GPIO pins for device %s: %s", self.name, e)

    def read_gpio(self):
        """读取GPIO引脚状态"""
        gpio_data = {}
        try:
            with self.__gpio_lock:
                for pin in self.gpio_pins:
                    result = subprocess.run(['gpio', 'read', str(pin)], 
                                          capture_output=True, text=True, check=True)
                    gpio_data[pin] = int(result.stdout.strip())
        except Exception as e:
            self.__log.error("Failed to read GPIO pins for device %s: %s", self.name, e)
        return gpio_data

    def write_gpio(self, pin_values):
        """写入GPIO引脚状态"""
        try:
            with self.__gpio_lock:
                for pin, value in pin_values.items():
                    if pin in self.gpio_pins:
                        subprocess.run(['gpio', 'write', str(pin), str(value)], 
                                     capture_output=True, text=True, check=True)
                        self.__log.debug("GPIO pin %s set to %s for device %s", pin, value, self.name)
        except Exception as e:
            self.__log.error("Failed to write GPIO pins for device %s: %s", self.name, e)

    def run(self):
        """主方法，用于读取GPIO数据并发送到平台"""
        self.__log.info("GPIO Device %s started", self.name)
        self.stopped = False
        
        while not self.__connector_stopped.is_set() and not self.stopped:
            try:
                if not self.__rpc_in_progress.is_set():
                    gpio_data = self.read_gpio()
                    if gpio_data:
                        try:
                            converted_data = self.uplink_converter.convert(None, gpio_data)
                            if converted_data:
                                self.uplink_queue.put(converted_data)
                        except Exception as e:
                            self.__log.error("Failed to convert GPIO data from device %s: %s", self.name, e)
                
                # 等待轮询周期
                sleep(self.polling_period)
                
            except Exception as e:
                self.__log.exception("Error in GPIO device %s: %s", self.name, e)
                self.stop()
        
        self.__log.info("GPIO Device %s stopped", self.name)

    def handle_rpc_request(self, rpc_method, params):
        """处理RPC请求"""
        result = {"success": True}
        processed = False
        
        for rpc_config in self.config.get("serverSideRpc", []):
            if rpc_method == rpc_config.get("method"):
                processed = True
                self.__rpc_in_progress.set()
                try:
                    if self.downlink_converter is not None:
                        converted_data = self.downlink_converter.convert(rpc_config, params)
                        if converted_data:
                            self.write_gpio(converted_data)
                            result = {"success": True, "result": "GPIO pins updated"}
                        else:
                            result = {"error": "No GPIO data to write", "success": False}
                    else:
                        # 直接处理GPIO写入请求
                        if 'pin' in params and 'value' in params:
                            pin_values = {params['pin']: params['value']}
                            self.write_gpio(pin_values)
                            result = {"success": True, "result": f"GPIO pin {params['pin']} set to {params['value']}"}
                        else:
                            result = {"error": "Invalid GPIO parameters", "success": False}
                except Exception as e:
                    self.__log.error("Failed to process RPC with method: %r, params: %r, config: %r - Error: %s",
                                   rpc_method, params, rpc_config, e)
                    result = {"error": str(e), "success": False}
                finally:
                    self.__rpc_in_progress.clear()
        
        if not processed:
            result = {"error": "Method not found", "success": False}
        
        return result

    def stop(self):
        """停止设备"""
        self.stopped = True

    def is_connected(self):
        """检查设备连接状态"""
        # GPIO设备总是连接的（如果系统支持GPIO）
        try:
            # 简单测试GPIO可用性
            subprocess.run(['gpio', '-v'], capture_output=True, text=True, check=True)
            return True
        except:
            return False


class GpioConnector(Thread, Connector):
    """
    GPIO connector class is used to represent a GPIO connector.
    It is used to manage devices connected to GPIO pins.
    """
    def __init__(self, gateway: 'TBGatewayService', config, connector_type):
        super().__init__()
        self._connector_type = connector_type  # required to have for get connector type method
        self.__config = config  # required to have for get config method
        self.__id = self.__config["id"]  # required to have for get id method
        self.__gateway = gateway  # required to have for send data to storage method or to use other gateway methods
        self.name = self.__config["name"]  # required to have for get name method
        self.__connected = False  # required to have for is connected method
        self.__uplink_queue = Queue(self.__config.get('uplinkQueueSize', 100000))
        self._log = init_logger(self.__gateway, self.name, level=self.__config.get('logLevel'),
                                enable_remote_logging=self.__config.get('enableRemoteLogging', False),
                                is_connector_logger=True)
        self._converter_log = init_logger(self.__gateway, self.name, level=self.__config.get('logLevel'),
                                          enable_remote_logging=self.__config.get('enableRemoteLogging', False),
                                          is_converter_logger=True)
        self._log.info("Starting %s connector", self.get_name())
        self.daemon = True
        self.stopped = Event()
        self.stopped.set()
        self.__devices: List[GpioDevice] = []
        self._log.info('Connector %s initialization success.', self.get_name())

    def __start_devices(self):
        """启动所有设备"""
        failed_to_connect_devices = len(self.__devices)
        for device in self.__devices:
            try:
                device.start()
                failed_to_connect_devices -= 1
            except Exception as e:
                self._log.exception("Failed to start device %s, error: %s", device.name, e)
        self.__connected = failed_to_connect_devices == 0

    def open(self):
        """Service method to start the connector."""
        self.stopped.clear()
        self.start()

    def get_name(self):
        return self.name

    def get_type(self):
        return self._connector_type

    def is_connected(self):
        return self.__connected

    def is_stopped(self):
        return self.stopped.is_set()

    def get_config(self):
        return self.__config

    def get_id(self):
        return self.__id

    def __load_devices(self):
        """Method to create devices objects using configuration file and create converters for them."""
        devices_config = self.__config.get('devices')
        try:
            if devices_config is not None:
                for device_config in devices_config:
                    device = None
                    uplink_converter_class_name = device_config.get('converter', device_config.get('uplink_converter'))
                    if uplink_converter_class_name is not None:
                        converter_class = TBModuleLoader.import_module(self._connector_type,
                                                                       uplink_converter_class_name)
                        uplink_converter = converter_class(device_config, self._log)
                        device = GpioDevice(device_config, uplink_converter, self.stopped,
                                          self._log, self.__uplink_queue)
                    else:
                        self._log.error('Converter configuration for the connector %s -- \
                            not found, please check your configuration file.', self.get_name())
                    
                    if device_config.get('downlink_converter') is not None:
                        downlink_converter_class = TBModuleLoader.import_module(self._connector_type,
                                                                                device_config.get('downlink_converter'))
                        if device is not None:
                            device.downlink_converter = downlink_converter_class(device_config, self._converter_log)
                    
                    if device is not None:
                        self.__devices.append(device)
            else:
                self._log.error('Section "devices" in the configuration not found. \
                    A connector %s has being stopped.', self.get_name())
                self.close()
        except Exception as e:
            self._log.error('Failed to load devices, error: %s', e)

    def run(self):
        """Main method to manage devices connected to GPIO pins and process data from them."""
        try:
            self.__load_devices()
            self.__start_devices()
            self._log.info("Devices in configuration file found: %s ",
                           '\n'.join(device.name for device in self.__devices))
            
            while not self.stopped.is_set():
                try:
                    connected_devices = len(self.__devices)
                    for device in self.__devices:
                        if not device.stopped and not device.is_connected():
                            connected_devices -= 1
                            self._log.error("Device %s is not connected", device.name)
                    
                    self.__connected = connected_devices == len(self.__devices)
                    
                    if not self.__uplink_queue.empty():
                        data = self.__uplink_queue.get()
                        self.__gateway.send_to_storage(self.name, self.__id, data)
                    else:
                        sleep(0.05)
                        
                except Exception as e:
                    self._log.error("Failed to process data from device %s, error: %s", self.name, e)
        except Exception as e:
            self._log.error("Failed to process data from device %s, error: %s", self.name, e)

    def close(self):
        """Service method to stop the connector and all devices connected to it."""
        self.stopped.set()
        for device in self.__devices:
            self.__gateway.del_device(device.name)
            device.stop()
        self._log.stop()

    def on_attributes_update(self, content):
        """Callback method to process attribute updates from the platform."""
        self._log.debug("Received attribute update: %s", content)
        device_name = content.get("device")

        if device_name is None:
            self._log.error("Device name is not provided in the attribute update request: %s", content)
            return

        device = next((d for d in self.__devices if d.name == device_name), None)
        if device is None:
            self._log.error("Device %s not found in connector.", device_name)
            return

        request_config_list = device.config.get("attributeUpdates")
        if request_config_list is None:
            self._log.error("Attribute update configuration for device %s not found", device_name)
            return

        # Create a mapping from attribute name on platform to its config
        attribute_map = {
            config.get("attributeOnPlatform", config.get("key")): config
            for config in request_config_list
        }

        # Process each attribute from the update
        for attribute_key, value in content["data"].items():
            if attribute_key in attribute_map:
                attribute_config = attribute_map[attribute_key]
                try:
                    # 处理GPIO属性更新
                    if attribute_config.get("pin") is not None:
                        pin = attribute_config.get("pin")
                        pin_values = {pin: int(value)}
                        device.write_gpio(pin_values)
                        self._log.info("Updated GPIO pin %s to %s for device %s",
                                     pin, value, device_name)
                    else:
                        # 使用传统方式处理
                        device.write_gpio({attribute_key: int(value)})

                except Exception as e:
                    self._log.error("Failed to send attribute update for key '%s' to device %s: %s",
                                  attribute_key, device_name, e)
            else:
                self._log.warning("Attribute update for key '%s' for device %s is not configured.",
                                  attribute_key, device_name)

    def server_side_rpc_handler(self, content):
        """Callback method to process RPC requests from the platform."""
        self._log.debug("Received RPC request: %s", content)
        device_name = content.get("device")
        rpc_data = content.get("data", {})
        rpc_method = rpc_data.get("method")
        req_id = rpc_data.get("id")
        params = rpc_data.get("params")
        
        if device_name is not None:
            for device in self.__devices:
                if device_name == device.name:
                    result = device.handle_rpc_request(rpc_method, params)
                    if "error" in result:
                        self._log.error("Failed to process RPC request for device %s, error: %s",
                                      device_name, result["error"])
                    if result is not None:
                        self.__gateway.send_rpc_reply(device=device_name,
                                                      req_id=req_id,
                                                      content=result,
                                                      wait_for_publish=True,
                                                      quality_of_service=1)
        else:
            self._log.error("Device name is not provided in the RPC request: %s", content)


# Backward compatibility alias
CustomGpioConnector = GpioConnector
