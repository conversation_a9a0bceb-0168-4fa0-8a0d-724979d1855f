<template>
  <div class="knx-attribute-updates-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>属性更新配置</span>
          <el-button type="primary" size="small" @click="addAttributeUpdate">
            <el-icon><Plus /></el-icon>
            添加属性更新
          </el-button>
        </div>
      </template>
      
      <div v-if="attributeUpdatesConfig.length === 0" class="empty-state">
        <el-empty description="暂无属性更新配置">
          <el-button type="primary" @click="addAttributeUpdate">添加第一个属性更新</el-button>
        </el-empty>
      </div>
      
      <div v-else class="attribute-updates-list">
        <el-card 
          v-for="(update, index) in attributeUpdatesConfig" 
          :key="index" 
          class="update-card"
          shadow="hover"
        >
          <template #header>
            <div class="update-header">
              <div class="update-info">
                <span class="update-key">{{ update.key || '未设置键名' }}</span>
                <el-tag size="small" type="info">{{ update.dataType || 'string' }}</el-tag>
              </div>
              <div class="update-actions">
                <el-button type="primary" size="small" link @click="editAttributeUpdate(update, index)">
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-button type="danger" size="small" link @click="deleteAttributeUpdate(index)">
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="update-summary">
            <el-row :gutter="16">
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">键名:</span>
                  <span class="value">{{ update.key || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">群组地址:</span>
                  <span class="value">{{ update.groupAddress || '-' }}</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="summary-item">
                  <span class="label">设备名筛选器:</span>
                  <span class="value">{{ update.deviceNameFilter || '-' }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 属性更新配置对话框 -->
    <KnxAttributeUpdateDialog
      v-model="dialogVisible"
      :attribute-update="currentAttributeUpdate"
      :is-edit="isEdit"
      @save="handleSaveAttributeUpdate"
      @cancel="handleCancelAttributeUpdate"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import KnxAttributeUpdateDialog from './KnxAttributeUpdateDialog.vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const currentAttributeUpdate = ref(null)
const isEdit = ref(false)
const currentIndex = ref(-1)

// 属性更新配置数据
const attributeUpdatesConfig = reactive([])

// 添加属性更新
const addAttributeUpdate = () => {
  currentAttributeUpdate.value = null
  isEdit.value = false
  currentIndex.value = -1
  dialogVisible.value = true
}

// 编辑属性更新
const editAttributeUpdate = (update, index) => {
  currentAttributeUpdate.value = { ...update }
  isEdit.value = true
  currentIndex.value = index
  dialogVisible.value = true
}

// 删除属性更新
const deleteAttributeUpdate = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个属性更新配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    attributeUpdatesConfig.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存属性更新
const handleSaveAttributeUpdate = (updateData) => {
  if (isEdit.value && currentIndex.value >= 0) {
    // 编辑模式
    attributeUpdatesConfig.splice(currentIndex.value, 1, updateData)
    ElMessage.success('属性更新配置更新成功')
  } else {
    // 添加模式
    attributeUpdatesConfig.push(updateData)
    ElMessage.success('属性更新配置添加成功')
  }
  
  dialogVisible.value = false
  handleChange()
}

// 取消操作
const handleCancelAttributeUpdate = () => {
  dialogVisible.value = false
}

// 处理配置变化
const handleChange = () => {
  const updates = attributeUpdatesConfig.map(update => ({ ...update }))
  emit('update:modelValue', updates)
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (Array.isArray(newValue)) {
    attributeUpdatesConfig.splice(0, attributeUpdatesConfig.length, ...newValue)
  }
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
.knx-attribute-updates-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .attribute-updates-list {
    .update-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .update-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .update-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .update-key {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .update-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .update-summary {
        margin-top: 16px;
        
        .summary-item {
          display: flex;
          flex-direction: column;
          gap: 4px;
          
          .label {
            font-size: 12px;
            color: #909399;
          }
          
          .value {
            font-size: 14px;
            color: #303133;
            font-weight: 500;
            word-break: break-all;
          }
        }
      }
    }
  }
}
</style> 