# ThingsBoard Gateway 可视化配置界面

本项目是ThingsBoard Gateway的可视化配置界面，提供了网关配置、连接器管理、网络配置等功能。

## 系统测试环境

- 开发板: Orangepi zero3
- CPU: Cortex-A53
- 内存: 2GB
- 存储: 32GB板载存储
- 系统: Debian 11

## 项目介绍

### 功能特性

1. 网关基础配置
   - 服务器连接配置
   - 数据统计配置
   - 设备筛选配置
   - 身份认证配置

2. 连接器管理
   - 支持多种连接器(MQTT/Modbus/OPCUA等)
   - 连接器状态监控
   - 设备列表查看
   - 设备数据实时监控

3. 网络配置
   - 网卡配置
   - IP地址配置
   - DNS服务器配置
   - WiFi网络扫描与连接
   - 有线/无线网络区分管理

4. 设备管理
   - 设备列表查看
   - 设备状态监控
   - 设备数据实时展示
   - 设备数据历史记录

5. 远程协议
   - 客户端ID配置
   - 远程协助启动/停止

## 部署说明

### 0. 系统运行环境必须项
1. 禁用ifupdown方式，安装network-manager（配置网络使用nmcli命令）
```bash
git clone <repository_url> /home/<USER>
apt install gcc python3-dev lm-sensors network-manager
```

### 1. ThingsBoard Gateway部署

1. 克隆仓库到/home目录
```bash
git clone <repository_url> /home/<USER>
```

2. 设置虚拟环境及安装依赖包
```bash
cd /home/<USER>/
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

3. 设置开机自启动
```bash
sudo ./install.sh
```

### 2. 远程协助客户端部署（NPS/NPC）
注意：服务端 http://iotcloud.top:8081/ （ admin/iotcloud-nps-key123 ）

```bash
tar -zvxf linux_amd64_client.tar.gz
mv npc /usr/bin/
rm -rf conf/ linux_amd64_client.tar.gz
```

### 3. 前端界面部署

1. 安装nginx
```bash
apt-get update
apt-get install nginx
```

2. 配置nginx
创建配置文件：
```bash
vi /etc/nginx/sites-enabled/default
```

修改为如下配置：
```nginx
server {
    listen       80;
    # server_name  localhost;

    location / {
        root   /srv/gatewayconfig/dist;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /prod-api/{
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass   http://127.0.0.1:20400/;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_read_timeout 600;
    }
}
```

3. 部署前端文件
```bash
mkdir -p /srv/gatewayconfig
cp -r dist/ /srv/gatewayconfig/
```

4. 重启nginx
```bash
systemctl restart nginx
```

## 访问方式

部署完成后，可通过以下地址访问：
```
http://<设备IP>:80
```

## 项目结构

```
gateway-mng-ui/
├── src/                    # 源代码目录
│   ├── api/               # API接口定义
│   ├── assets/           # 静态资源
│   ├── components/       # 公共组件
│   ├── router/          # 路由配置
│   ├── views/           # 页面组件
│   └── App.vue          # 根组件
├── public/               # 公共资源
└── package.json         # 项目配置
```

## 开发说明

1. 安装依赖
```bash
npm install
```

2. 开发环境运行
```bash
npm run dev
```

3. 生产环境构建
```bash
npm run build
```

## 注意事项

1. ThingsBoard Gateway必须部署在/home目录下
2. 确保系统用户具有相应的权限执行配置修改操作
3. 网络配置功能需要sudo权限
