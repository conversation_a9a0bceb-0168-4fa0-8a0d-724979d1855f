/**
 * 配置工具函数
 */

/**
 * 清理配置对象中的空值
 * @param {any} obj - 要清理的对象
 * @returns {any} 清理后的对象，如果对象为空则返回undefined
 */
export const cleanEmptyValues = (obj) => {
    if (obj === null || obj === undefined) {
      return undefined
    }
    
    if (Array.isArray(obj)) {
      const cleanedArray = obj.map(item => cleanEmptyValues(item)).filter(item => item !== undefined)
      return cleanedArray.length > 0 ? cleanedArray : undefined
    }
    
    if (typeof obj === 'object') {
      const cleanedObj = {}
      let hasValidKeys = false
      
      for (const [key, value] of Object.entries(obj)) {
        // 跳过空字符串、null、undefined，但保留数字0和布尔值false
        if (value === '' || value === null || value === undefined) {
          continue
        }
        
        // 对于数组，如果是空数组也跳过
        if (Array.isArray(value) && value.length === 0) {
          continue
        }
        
        // 递归清理嵌套对象
        const cleanedValue = cleanEmptyValues(value)
        if (cleanedValue !== undefined) {
          cleanedObj[key] = cleanedValue
          hasValidKeys = true
        }
      }
      
      return hasValidKeys ? cleanedObj : undefined
    }
    
    // 对于基本类型，如果是空字符串则返回undefined，否则返回原值
    // 保留数字0和布尔值false
    if (obj === '') {
      return undefined
    }
    
    return obj
  }
  
  /**
   * 深度克隆对象
   * @param {any} obj - 要克隆的对象
   * @returns {any} 克隆后的对象
   */
  export const deepClone = (obj) => {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime())
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => deepClone(item))
    }
    
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    
    return cloned
  }
  
  /**
   * 验证配置对象的基本结构
   * @param {object} config - 配置对象
   * @returns {boolean} 是否有效
   */
  export const validateConfig = (config) => {
    if (!config || typeof config !== 'object') {
      return false
    }
    
    // 检查必要的顶级字段
    const requiredFields = ['thingsboard']
    for (const field of requiredFields) {
      if (!config[field]) {
        return false
      }
    }
    
    // 检查IoTCloud配置的必要字段
    const tbConfig = config.thingsboard
    if (!tbConfig.host || !tbConfig.port) {
      return false
    }
    
    return true
  }