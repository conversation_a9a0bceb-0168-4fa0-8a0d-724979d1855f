<template>
  <div class="device-info-config">
    <div class="section-title">设备信息配置</div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="设备名来源">
          <el-select v-model="deviceInfo.deviceNameExpressionSource" style="width: 100%">
            <el-option label="消息内容" value="message" />
            <el-option label="主题" value="topic" />
            <el-option label="常量" value="constant" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="设备名表达式">
          <el-input v-model="deviceInfo.deviceNameExpression" placeholder="${serialNumber}" />
        </el-form-item>
      </el-col>
      <el-col :span="8" v-if="showDeviceProfile">
        <el-form-item label="设备类型来源">
          <el-select v-model="deviceInfo.deviceProfileExpressionSource" style="width: 100%">
            <el-option label="消息内容" value="message" />
            <el-option label="主题" value="topic" />
            <el-option label="常量" value="constant" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20" v-if="showDeviceProfile">
      <el-col :span="8">
        <el-form-item label="设备类型表达式">
          <el-input v-model="deviceInfo.deviceProfileExpression" placeholder="default" />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  showDeviceProfile: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue'])

// 设备信息数据
const deviceInfo = computed({
  get: () => {
    // 确保返回完整的设备信息对象
    const defaultInfo = {
      deviceNameExpressionSource: 'message',
      deviceNameExpression: '${serialNumber}',
      deviceProfileExpressionSource: 'constant',
      deviceProfileExpression: 'default'
    }
    
    return { ...defaultInfo, ...props.modelValue }
  },
  set: (value) => emit('update:modelValue', value)
})

// 监听变化并确保数据结构完整
watch(() => props.modelValue, (newValue) => {
  if (!newValue || typeof newValue !== 'object') {
    emit('update:modelValue', {
      deviceNameExpressionSource: 'message',
      deviceNameExpression: '${serialNumber}',
      deviceProfileExpressionSource: 'constant',
      deviceProfileExpression: 'default'
    })
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.device-info-config {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin: 15px 0;
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 15px;
  }
  
  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }
}
</style> 