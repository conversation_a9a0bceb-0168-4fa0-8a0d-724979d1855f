// 默认配置加载工具
import bacnetConfig from '@/assets/default_configs/bacnet.json'
import bleConfig from '@/assets/default_configs/ble.json'
import canConfig from '@/assets/default_configs/can.json'
import ftpConfig from '@/assets/default_configs/ftp.json'
import gpioConfig from '@/assets/default_configs/gpio.json'
import knxConfig from '@/assets/default_configs/knx.json'
import modbusConfig from '@/assets/default_configs/modbus.json'
import mqttConfig from '@/assets/default_configs/mqtt.json'
import ocppConfig from '@/assets/default_configs/ocpp.json'
import odbcConfig from '@/assets/default_configs/odbc.json'
import opcuaConfig from '@/assets/default_configs/opcua.json'
import requestConfig from '@/assets/default_configs/request.json'
import restConfig from '@/assets/default_configs/rest.json'
import serialConfig from '@/assets/default_configs/serial.json'
import snmpConfig from '@/assets/default_configs/snmp.json'
import socketConfig from '@/assets/default_configs/socket.json'
import xmppConfig from '@/assets/default_configs/xmpp.json'

// 默认配置映射
const DEFAULT_CONFIGS = {
  bacnet: bacnetConfig,
  ble: bleConfig,
  can: canConfig,
  ftp: ftpConfig,
  gpio: gpioConfig,
  knx: knxConfig,
  modbus: modbusConfig,
  mqtt: mqttConfig,
  ocpp: ocppConfig,
  odbc: odbcConfig,
  opcua: opcuaConfig,
  request: requestConfig,
  rest: restConfig,
  serial: serialConfig,
  snmp: snmpConfig,
  socket: socketConfig,
  xmpp: xmppConfig
}

/**
 * 获取指定连接器类型的默认配置
 * @param {string} connectorType - 连接器类型
 * @returns {Object|null} 默认配置对象，如果类型不存在则返回null
 */
export function getDefaultConfig(connectorType) {
  if (!connectorType || !DEFAULT_CONFIGS[connectorType]) {
    console.warn(`不支持的连接器类型: ${connectorType}`)
    return null
  }
  
  // 返回深拷贝的配置对象，避免修改原始配置
  return JSON.parse(JSON.stringify(DEFAULT_CONFIGS[connectorType]))
}

/**
 * 检查是否支持指定的连接器类型
 * @param {string} connectorType - 连接器类型
 * @returns {boolean} 是否支持
 */
export function isSupportedConnectorType(connectorType) {
  return DEFAULT_CONFIGS.hasOwnProperty(connectorType)
}

/**
 * 获取所有支持的连接器类型列表
 * @returns {string[]} 支持的连接器类型数组
 */
export function getSupportedConnectorTypes() {
  return Object.keys(DEFAULT_CONFIGS)
}

/**
 * 为指定连接器类型生成唯一ID
 * @param {string} connectorType - 连接器类型
 * @returns {string} UUID格式的ID
 */
export function generateConnectorId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 为默认配置添加必要的字段（如ID、名称等）
 * @param {Object} config - 默认配置对象
 * @param {string} connectorName - 连接器名称
 * @param {string} connectorType - 连接器类型
 * @returns {Object} 完整的配置对象
 */
export function prepareDefaultConfig(config, connectorName, connectorType = null) {
  if (!config) return null
  
  const preparedConfig = { ...config }
  
  // 添加基础字段
  preparedConfig.name = connectorName
  
  // 如果配置中没有ID，则生成一个
  if (!preparedConfig.id) {
    preparedConfig.id = generateConnectorId()
  }
  
  // 设置默认日志级别（如果没有）
  if (!preparedConfig.logLevel) {
    preparedConfig.logLevel = 'INFO'
  }
  
  return preparedConfig
}

export default {
  getDefaultConfig,
  isSupportedConnectorType,
  getSupportedConnectorTypes,
  generateConnectorId,
  prepareDefaultConfig
} 