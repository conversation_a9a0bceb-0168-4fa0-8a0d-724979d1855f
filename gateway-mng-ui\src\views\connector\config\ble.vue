<template>
  <div class="ble-config">
    <el-tabs v-model="activeTab" class="ble-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
        
        <!-- BLE特有配置 -->
        <el-card class="config-card" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>BLE连接器专用配置</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="显示映射">
                <el-switch v-model="config.showMap" />
                <div class="field-hint">在界面中显示数据映射信息</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="被动扫描模式">
                <el-switch v-model="config.passiveScanMode" />
                <div class="field-hint">启用被动扫描模式可以降低功耗</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-tab-pane>

      <!-- 扫描器配置标签页 -->
      <el-tab-pane label="扫描器配置" name="scanner">
        <BleScanner 
          v-model="config.scanner" 
        />
      </el-tab-pane>

      <!-- 设备配置标签页 -->
      <el-tab-pane label="设备配置" name="devices">
        <BleDevicesConfig 
          v-model="config.devices" 
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'
import BleScanner from './components/BleScanner.vue'
import BleDevicesConfig from './components/BleDevicesConfig.vue'

const route = useRoute()
const activeTab = ref('general')

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  showMap: false,
  passiveScanMode: true,
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  scanner: {
    timeout: 10,
    deviceScanningTimeout: 10,
    passiveScanMode: true,
    scanTimeoutMs: 10000
  },
  devices: []
}

// 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'ble', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
      
      console.log('BLE 连接器配置初始化成功')
    } else {
      ElMessage.warning('BLE 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('BLE 连接器初始化失败:', error)
    ElMessage.error('BLE 连接器初始化失败')
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  ble: config
})
</script>

<style lang="scss" scoped>
.ble-config {
  .ble-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
  
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
}
</style>