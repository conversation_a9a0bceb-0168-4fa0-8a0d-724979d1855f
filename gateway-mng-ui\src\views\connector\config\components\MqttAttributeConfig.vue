<template>
  <div class="mqtt-attribute-config">
    <el-divider content-position="left">
      <div class="section-header">
        <span>{{ title }}</span>
        <el-button type="primary" size="small" @click="addAttribute">
          <el-icon><Plus /></el-icon>
          添加属性
        </el-button>
      </div>
    </el-divider>
    
    <div v-if="attributes.length === 0" class="empty-state">
      <el-empty :description="`暂无${title}配置`" :image-size="80">
        <el-button type="primary" @click="addAttribute">添加第一个属性</el-button>
      </el-empty>
    </div>
    
    <div v-else class="attributes-list">
      <div 
        v-for="(attribute, index) in attributes" 
        :key="index"
        class="attribute-item"
      >
        <el-card shadow="hover" size="small">
          <template #header>
            <div class="attribute-header">
              <div class="attribute-info">
                <span class="attribute-name">{{ attribute.key || `属性 ${index + 1}` }}</span>
                <el-tag v-if="attribute.type" :type="getTypeColor(attribute.type)" size="small">
                  {{ getTypeDisplay(attribute.type) }}
                </el-tag>
              </div>
              <el-button 
                type="danger" 
                size="small" 
                link
                @click="removeAttribute(index)"
              >
                <el-icon><Delete /></el-icon>删除
              </el-button>
            </div>
          </template>
          
          <el-form :model="attribute" label-width="100px" size="small">
            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="属性键" required>
                  <el-input v-model="attribute.key" placeholder="temperature" />
                  <div class="field-hint">属性的唯一标识符</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数据类型" required>
                  <el-select v-model="attribute.type" style="width: 100%">
                    <el-option label="字符串" value="string" />
                    <el-option label="整数" value="int" />
                    <el-option label="浮点数" value="double" />
                    <el-option label="布尔值" value="bool" />
                    <el-option label="长整数" value="long" />
                    <el-option label="JSON" value="json" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="值表达式" required>
                  <el-input v-model="attribute.value" placeholder="${model}" />
                  <div class="field-hint">从消息中提取值的表达式</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </div>
    
    <div v-if="description" class="description">
      <p>{{ description }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: '属性配置'
  },
  description: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

// 标准的isInternalUpdate模式
const isInternalUpdate = ref(false)

const attributes = ref([])

// 获取数据类型显示名称
const getTypeDisplay = (type) => {
  const typeMap = {
    string: '字符串',
    int: '整数',
    double: '浮点数',
    bool: '布尔值',
    long: '长整数',
    json: 'JSON'
  }
  return typeMap[type] || type
}

// 获取数据类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    string: '',
    int: 'success',
    double: 'warning',
    bool: 'info',
    long: 'success',
    json: 'info'    // 改用info而不是primary
  }
  return colorMap[type] || ''
}

// 创建默认属性
const createDefaultAttribute = () => {
  return {
    key: '',
    type: 'string',
    value: ''
  }
}

// 添加属性
const addAttribute = () => {
  const newAttribute = createDefaultAttribute()
  attributes.value.push(newAttribute)
  ElMessage.success('属性添加成功')
}

// 移除属性
const removeAttribute = (index) => {
  attributes.value.splice(index, 1)
  ElMessage.success('属性删除成功')
}

// 监听props变化 - 标准模式
watch(() => props.modelValue, (newValue) => {
  if (isInternalUpdate.value) return
  
  attributes.value = [...(newValue || [])]
}, { deep: true, immediate: true })

// 监听属性变化 - 标准模式
watch(attributes, (newAttributes) => {
  isInternalUpdate.value = true
  nextTick(() => {
    isInternalUpdate.value = false
  })
  
  emit('update:modelValue', [...newAttributes])
}, { deep: true })
</script>

<style lang="scss" scoped>
.mqtt-attribute-config {
  margin-top: 16px;

  .section-header {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #303133;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }

  .attributes-list {
    margin-top: 16px;
    
    .attribute-item {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .attribute-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .attribute-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .attribute-name {
            font-weight: 600;
            color: #303133;
          }
        }
      }
    }
  }

  .description {
    margin-top: 16px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;
    
    p {
      margin: 0;
      font-size: 13px;
      color: #606266;
      line-height: 1.5;
    }
  }

  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
}

:deep(.el-divider__text) {
  background: #fff;
  padding: 0 16px;
}

:deep(.el-form-item) {
  margin-bottom: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background: #fafafa;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style> 