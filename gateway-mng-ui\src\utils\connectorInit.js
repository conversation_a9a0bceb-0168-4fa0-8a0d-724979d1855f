import { getDefaultConfig, prepareDefaultConfig } from './defaultConfigs.js'
import { getFileFromName } from '@/api/file.js'
import { ElMessage } from 'element-plus'

/**
 * 通用连接器初始化函数
 * @param {Object} config - 响应式配置对象
 * @param {string} connectorType - 连接器类型
 * @param {Object} route - Vue路由对象
 * @param {Object} defaultStructure - 默认配置结构
 * @returns {Promise<boolean>} 初始化是否成功
 */
export async function initializeConnectorConfig(config, connectorType, route, defaultStructure = null) {
  try {
    // 如果是编辑模式且有配置文件，加载配置文件
    if (route.query && route.query.configuration) {
      return await loadConfigFromFile(config, connectorType, route.query.configuration, defaultStructure)
    } else {
      // 新建模式，使用默认配置
      return initializeWithDefaultConfig(config, connectorType, defaultStructure)
    }
  } catch (error) {
    console.error(`${connectorType} 连接器初始化失败:`, error)
    ElMessage.error(`${connectorType} 连接器初始化失败`)
    // 初始化失败时尝试使用默认配置
    return initializeWithDefaultConfig(config, connectorType, defaultStructure)
  }
}

/**
 * 从配置文件加载配置
 * @param {Object} config - 响应式配置对象
 * @param {string} connectorType - 连接器类型
 * @param {string} configFileName - 配置文件名
 * @param {Object} defaultStructure - 默认配置结构
 * @returns {Promise<boolean>} 加载是否成功
 */
async function loadConfigFromFile(config, connectorType, configFileName, defaultStructure) {
  try {
    const response = await getFileFromName(configFileName)
    if (response.data) {
      // 验证配置完整性
      const validationErrors = validateConnectorConfig(response.data, connectorType)
      if (validationErrors.length > 0) {
        console.warn(`${connectorType} 配置验证警告:`, validationErrors)
        ElMessage.warning(`配置文件存在问题: ${validationErrors[0]}`)
      }

      // 合并配置，确保所有必要字段存在
      const mergedConfig = mergeConfigWithDefaults(response.data, connectorType, defaultStructure)
      
      // 更新配置对象
      Object.assign(config, mergedConfig)
      
      ElMessage.success('配置加载成功')
      return true
    }
  } catch (error) {
    console.error(`加载 ${connectorType} 配置文件失败:`, error)
    ElMessage.error('加载配置文件失败，使用默认配置')
    // 加载失败时使用默认配置
    return initializeWithDefaultConfig(config, connectorType, defaultStructure)
  }
  return false
}

/**
 * 使用默认配置初始化
 * @param {Object} config - 响应式配置对象
 * @param {string} connectorType - 连接器类型
 * @param {Object} defaultStructure - 默认配置结构
 * @returns {boolean} 初始化是否成功
 */
function initializeWithDefaultConfig(config, connectorType, defaultStructure) {
  try {
    // 获取本地默认配置
    const defaultConfig = getDefaultConfig(connectorType)
    
    if (defaultConfig) {
      // 准备配置数据
      const preparedConfig = prepareDefaultConfig(defaultConfig, 'default', connectorType)
      
      // 如果有自定义默认结构，与默认配置合并
      const finalConfig = defaultStructure 
        ? mergeConfigStructures(preparedConfig, defaultStructure)
        : preparedConfig
      
      // 更新配置对象
      Object.assign(config, finalConfig)
      
      console.log(`${connectorType} 连接器已使用默认配置初始化`)
      return true
    } else if (defaultStructure) {
      // 如果没有默认配置文件但有默认结构，使用默认结构
      Object.assign(config, defaultStructure)
      console.log(`${connectorType} 连接器已使用内置默认结构初始化`)
      return true
    } else {
      console.warn(`${connectorType} 连接器没有可用的默认配置`)
      return false
    }
  } catch (error) {
    console.error(`${connectorType} 默认配置初始化失败:`, error)
    return false
  }
}

/**
 * 合并配置与默认值
 * @param {Object} loadedConfig - 加载的配置
 * @param {string} connectorType - 连接器类型
 * @param {Object} defaultStructure - 默认配置结构
 * @returns {Object} 合并后的配置
 */
function mergeConfigWithDefaults(loadedConfig, connectorType, defaultStructure) {
  // 获取默认配置
  const defaultConfig = getDefaultConfig(connectorType)
  
  let baseConfig = {}
  
  // 首先使用本地默认配置作为基础
  if (defaultConfig) {
    baseConfig = { ...defaultConfig }
  }
  
  // 如果有自定义默认结构，进一步合并
  if (defaultStructure) {
    baseConfig = mergeConfigStructures(baseConfig, defaultStructure)
  }
  
  // 最后与加载的配置合并，加载的配置具有最高优先级
  return mergeConfigStructures(baseConfig, loadedConfig)
}

/**
 * 深度合并配置结构
 * @param {Object} base - 基础配置
 * @param {Object} override - 覆盖配置
 * @returns {Object} 合并后的配置
 */
function mergeConfigStructures(base, override) {
  const result = { ...base }
  
  for (const key in override) {
    if (override.hasOwnProperty(key)) {
      if (override[key] && typeof override[key] === 'object' && !Array.isArray(override[key])) {
        // 递归合并对象
        result[key] = mergeConfigStructures(result[key] || {}, override[key])
      } else {
        // 直接覆盖（包括数组）
        result[key] = override[key]
      }
    }
  }
  
  return result
}

/**
 * 验证连接器配置
 * @param {Object} configData - 配置数据
 * @param {string} connectorType - 连接器类型
 * @returns {string[]} 验证错误列表
 */
function validateConnectorConfig(configData, connectorType) {
  const errors = []
  
  // 通用验证
  if (!configData.name && !configData.id) {
    errors.push('缺少连接器名称或ID')
  }
  
  // 特定连接器类型的验证
  switch (connectorType) {
    case 'mqtt':
      if (!configData.broker) {
        errors.push('缺少MQTT代理配置')
      } else {
        if (!configData.broker.host) errors.push('缺少MQTT服务器地址')
        if (!configData.broker.port || configData.broker.port < 1 || configData.broker.port > 65535) {
          errors.push('MQTT端口必须在1-65535之间')
        }
      }
      break
      
    case 'modbus':
      if (!configData.master && !configData.slave) {
        errors.push('缺少Modbus主从配置')
      }
      break
      
    case 'opcua':
      if (!configData.server) {
        errors.push('缺少OPC-UA服务器配置')
      }
      break
      
    case 'rest':
      if (!configData.server) {
        errors.push('缺少REST服务器配置')
      }
      break
      
    case 'can':
      if (!configData.interface || !configData.channel) {
        errors.push('缺少CAN接口或通道配置')
      }
      break
      
    // 其他连接器类型的验证规则可以在这里添加
  }
  
  return errors
}

/**
 * 为连接器配置生成唯一ID（如果没有）
 * @param {Object} config - 配置对象
 * @returns {string} 生成的ID
 */
export function ensureConfigId(config) {
  if (!config.id) {
    config.id = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
  return config.id
}

export default {
  initializeConnectorConfig,
  ensureConfigId,
  validateConnectorConfig
} 