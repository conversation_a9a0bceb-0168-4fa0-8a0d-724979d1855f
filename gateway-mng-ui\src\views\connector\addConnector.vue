<template>
  <div class="base_box">
    <el-form ref="tbFormRef" :model="connectorForm" :rules="connectorRules" inline label-width="auto">
      <div class="type-select">
        <el-form-item label="类型" prop="type">
          <el-select v-model="connectorForm.type" placeholder="" :disabled="isEdit">
            <el-option v-for="(item, index) in typeOption" :key="index" :label="getConnectorTypeName(item)" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="connectorForm.name" />
        </el-form-item>
        <el-form-item label="文件名">
          <el-input v-model="connectorForm.configuration" disabled />
        </el-form-item>
        <el-form-item label="本地连接器">
          <el-switch 
            v-model="connectorForm.isLocalOnly"
            active-text="是"
            inactive-text="否"
            disabled
          />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            （本地连接器可编辑，非本地连接器会被远程覆盖）
          </div>
        </el-form-item>
        <el-button :type="isEdit ? 'success' : 'primary'" :icon="isEdit ? Check : Plus" class="add-btn"
          @click="onSubmit"> {{ isEdit ? '保存连接器' : '新增连接器' }}</el-button>
        <el-button v-show="isEdit" type="primary" :icon="Back" plain @click="goBack">返回</el-button>
        <el-button v-show="!isEdit" type="info" :icon="Download" plain @click="loadDefaultConfig">载入默认配置</el-button>
      </div>

      <div class="type-select">
        <component :is="myComponent" ref="myComponentRef"></component>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, defineAsyncComponent } from 'vue'
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Back, Check, Download } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router';

import { updataFile, getAllFile } from '@/api/file.js'
import { readJson } from '@/api/gateway.js'
import { getConnectorTypes, getConnectorTypeName, SUPPORTED_TYPES, updateConnectorFullMetadata, getConnectorMetadataByConfigFile } from '@/api/connector.js'
import { getDefaultConfig, prepareDefaultConfig } from '@/utils/defaultConfigs.js'

// 组件映射
const componentMap = {
  mqtt: () => import('./config/mqtt'),
  modbus: () => import('./config/modbus'),
  opcua: () => import('./config/opcua'),
  ble: () => import('./config/ble'),
  request: () => import('./config/request'),
  can: () => import('./config/can'),
  bacnet: () => import('./config/bacnet'),
  knx: () => import('./config/knx'),
  odbc: () => import('./config/odbc'),
  rest: () => import('./config/rest'),
  snmp: () => import('./config/snmp'),
  ftp: () => import('./config/ftp'),
  socket: () => import('./config/socket'),
  xmpp: () => import('./config/xmpp'),
  ocpp: () => import('./config/ocpp'),
  gpio: () => import('./config/gpio'),
  serial: () => import('./config/serial')
}

const route = useRoute();
const router = useRouter()

onMounted(async () => {
  if (JSON.stringify(route.query) !== "{}") {
    connectorForm.value = { ...route.query }
    isEdit.value = true
    
    // 如果是编辑模式，读取配置文件获取真实的连接器名称和元数据
    if (route.query.configuration) {
      try {
        // 读取配置文件
        const configRes = await readJson(route.query.configuration)
        if (configRes.data && configRes.data.name) {
          // 使用配置文件中的真实名称
          connectorForm.value.name = configRes.data.name
          originalName.value = configRes.data.name // 保存原始名称
        }
        
        // 读取连接器元数据
        const typesRes = await getConnectorTypes()
        const metadata = getConnectorMetadataByConfigFile(route.query.configuration, typesRes.data)
        if (metadata) {
          connectorForm.value.isLocalOnly = metadata.isLocalOnly !== undefined ? metadata.isLocalOnly : true
        }
      } catch (error) {
        console.warn(`读取连接器配置文件 ${route.query.configuration} 失败:`, error)
        // 如果读取失败，保持使用传入的名称
        originalName.value = connectorForm.value.name || ''
        connectorForm.value.isLocalOnly = route.query.isLocalOnly !== undefined ? route.query.isLocalOnly : true
      }
    }
  } else {
    isEdit.value = false
  }
})

const tbFormRef = ref()
const connectorForm = ref({ type: 'mqtt', isLocalOnly: true })
const connectorRules = ref({
  name: [
    { required: true, message: '请输入名称！', trigger: 'change', },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '请输入字母、数字、下划线或短横线！', trigger: 'blur', }
  ],
})
const typeOption = ref(SUPPORTED_TYPES)
const myComponentRef = ref()
const isEdit = ref(false)
const originalName = ref('') // 用于跟踪原始名称

// 使用动态组件加载
const myComponent = computed(() => {
  const type = connectorForm.value.type
  if (!type || !componentMap[type]) {
    return null
  }
  return defineAsyncComponent(componentMap[type])
})

const onSubmit = async () => {
  let typeParams = { ...Object.values(myComponentRef.value)[0] }
  if (!tbFormRef.value) return
  
  // 检查配置文件名是否重复（仅在新增时检查）
  if (!isEdit.value) {
    try {
      const res = await getAllFile()
      const existingFiles = res.data || []
      if (existingFiles.includes(connectorForm.value.configuration)) {
        ElMessage({
          type: 'error',
          message: `配置文件 ${connectorForm.value.configuration} 已存在，请使用不同的连接器名称`
        })
        return
      }
    } catch (error) {
      console.error('检查文件是否存在失败:', error)
      ElMessage({
        type: 'error',
        message: '检查文件是否存在失败，请重试'
      })
      return
    }
  }
  
  tbFormRef.value.validate((valid, fields) => {
    if (valid) {
      if (connectorForm.value.type === 'modbus') {
        typeParams.slave.baudrate = typeParams.slave.baudrate ? Number(typeParams.slave.baudrate) : typeParams.slave.baudrate
        typeParams.slave.stopbits = typeParams.slave.stopbits ? Number(typeParams.slave.stopbits) : typeParams.slave.stopbits
        typeParams.master.slaves.forEach(item => {
          if (item.type === 'serial') {
            item.baudrate = item.baudrate ? Number(item.baudrate) : item.baudrate
            item.stopbits = item.stopbits ? Number(item.stopbits) : item.stopbits
          }
        })
      }
      
      if (connectorForm.value.type === 'serial') {
        // 确保serial连接器的设备配置中波特率为数字类型
        if (typeParams.devices && Array.isArray(typeParams.devices)) {
          typeParams.devices.forEach(device => {
            if (device.baudrate) {
              device.baudrate = Number(device.baudrate)
            }
          })
        }
      }
      
      // 确保所有连接器类型都包含名称
      typeParams.name = connectorForm.value.name

      // 注意：不在配置文件中保存 type 字段，type 信息由 connector_types.json 管理

      // 先更新连接器元数据，再保存配置文件
      const fileNameKey = connectorForm.value.configuration.replace('.json', '')
      
      updateConnectorFullMetadata(
        fileNameKey, 
        connectorForm.value.type, 
        connectorForm.value.name, 
        connectorForm.value.isLocalOnly || true
      ).then(() => {
        // 保存连接器配置文件
        return updataFile({ file_name: connectorForm.value.configuration, file_text: JSON.stringify(typeParams) })
      }).then(res => {
        if (res.msg === 'success') {
          ElMessage({
            type: 'success',
            message: JSON.stringify(route.query) === "{}" ? '新增成功！' : '修改成功！'
          })
          router.push({ path: '/connector/index' })
        } else {
          throw new Error('保存连接器配置失败')
        }
      }).catch(error => {
        ElMessage({
          type: 'error',
          message: error.message || '操作失败，请重试！'
        })
      })
    }
  })
}
const goBack = () => {
  router.push({ path: '/connector/index' })
}

// 加载默认配置
const loadDefaultConfig = () => {
  if (!connectorForm.value.type) {
    ElMessage({
      type: 'warning',
      message: '请先选择连接器类型'
    })
    return
  }

  ElMessageBox.confirm(
    '载入默认配置将覆盖当前已填写的内容，是否继续？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    try {
      // 从本地配置文件获取默认配置
      const defaultConfig = getDefaultConfig(connectorForm.value.type)
      
      if (!defaultConfig) {
        ElMessage({
          type: 'error',
          message: `暂不支持 ${connectorForm.value.type} 连接器的默认配置`
        })
        return
      }

      // 准备配置数据，添加必要字段
      const preparedConfig = prepareDefaultConfig(
        defaultConfig, 
        connectorForm.value.name || 'default',
        connectorForm.value.type
      )

        // 获取当前组件实例
        const componentInstance = myComponentRef.value
        if (componentInstance) {
          // 获取组件暴露的属性名（通常是连接器类型名称）
          const propName = Object.keys(componentInstance)[0]
          if (propName) {
            // 更新组件的数据
          Object.assign(componentInstance[propName], preparedConfig)
            ElMessage({
              type: 'success',
              message: '默认配置已载入'
            })
        } else {
          ElMessage({
            type: 'error',
            message: '无法找到组件配置属性'
          })
        }
      } else {
        ElMessage({
          type: 'error',
          message: '无法获取组件实例'
        })
      }
    } catch (error) {
      console.error('载入默认配置失败:', error)
      ElMessage({
        type: 'error',
        message: '载入默认配置失败: ' + (error.message || '未知错误')
      })
    }
  }).catch(() => {
    // 用户取消操作
  })
}

watch(() => connectorForm.value.name, (newName) => {
  // 只在新增模式下根据名称生成配置文件名
  if (!isEdit.value && newName) {
    connectorForm.value.configuration = newName + '.json'
  } else if (!isEdit.value && !newName) {
    connectorForm.value.configuration = null
  }
  // 编辑模式下保持原有的配置文件名不变
})

</script>

<style lang="scss" scoped>
.base_box {
  background: transparent;

  .type-select {
    background: #fff;
    padding: 20px 40px;
    border-radius: 8px;
  }

  .type-select:last-child {
    margin-top: 10px;
    height: 100%;
  }
}

:deep(.el-form-item__label) {
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  // margin-left: 34px;
}

:deep(.each-block) {
  .el-form-item__label {
    margin-left: 0 !important;
  }
}

:deep(.el-form-item) {
  margin-top: 10px;
  margin-bottom: 10px;
}

.add-btn {
  width: 128px;
}
</style>