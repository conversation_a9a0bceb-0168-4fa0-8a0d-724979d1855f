<template>
  <el-card class="rest-data-key-card" shadow="never">
    <template #header>
      <div class="key-header">
        <div class="key-info">
          <el-tag :type="getTypeTagType(keyData.type)" size="small">{{ getTypeDisplay(keyData.type) }}</el-tag>
          <span class="key-name">{{ keyData.key || '未命名' }}</span>
        </div>
        <el-button type="danger" size="small" link @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>

    <el-form :model="keyData" label-width="80px">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="键名" required>
            <el-input 
              v-model="keyData.key" 
              placeholder="temperature"
              @input="updateValue"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据类型" required>
            <el-select v-model="keyData.type" @change="updateValue">
              <el-option label="字符串" value="string" />
              <el-option label="整数" value="int" />
              <el-option label="浮点数" value="float" />
              <el-option label="双精度" value="double" />
              <el-option label="布尔值" value="boolean" />
              <el-option label="长整数" value="long" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="值表达式" required>
            <el-input 
              v-model="keyData.value" 
              placeholder="${temp}"
              @input="updateValue"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 表达式帮助 -->
      <div class="expression-help">
        <el-collapse size="small">
          <el-collapse-item name="help" title="表达式使用说明">
            <div class="help-content">
              <h4>JSON路径表达式</h4>
              <ul>
                <li><code>${temp}</code> - 获取JSON中的temp字段值</li>
                <li><code>${sensor.temperature}</code> - 获取嵌套对象中的值</li>
                <li><code>${data[0].value}</code> - 获取数组中的值</li>
                <li><code>constant_value</code> - 使用常量值</li>
              </ul>
              <h4>数据类型说明</h4>
              <ul>
                <li><strong>string</strong> - 文本数据，如设备名称、状态等</li>
                <li><strong>int</strong> - 整数，如计数、等级等</li>
                <li><strong>float/double</strong> - 小数，如温度、湿度等</li>
                <li><strong>boolean</strong> - 布尔值，如开关状态等</li>
              </ul>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 配置预览 -->
      <div v-if="keyData.key && keyData.value" class="config-preview">
        <div class="preview-title">配置预览</div>
        <div class="preview-content">
          <code>
            "{{ keyData.key }}": ({{ keyData.type }}) {{ keyData.value }}
          </code>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      type: 'string',
      key: '',
      value: ''
    })
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

// 数据键配置
const keyData = computed({
  get: () => ({
    type: 'string',
    key: '',
    value: '',
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 获取数据类型显示名称
const getTypeDisplay = (type) => {
  const typeMap = {
    'string': '字符串',
    'int': '整数',
    'float': '浮点数',
    'double': '双精度',
    'boolean': '布尔值',
    'long': '长整数'
  }
  return typeMap[type] || type
}

// 获取类型标签类型
const getTypeTagType = (type) => {
  const typeTagMap = {
    'string': 'info',
    'int': 'primary',
    'float': 'warning',
    'double': 'warning',
    'boolean': 'success',
    'long': 'primary'
  }
  return typeTagMap[type] || 'info'
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...keyData.value })
}

// 处理删除
const handleDelete = () => {
  emit('delete')
}
</script>

<style lang="scss" scoped>
.rest-data-key-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  
  &:hover {
    border-color: #c0c4cc;
  }
  
  .key-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .key-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .key-name {
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .expression-help {
    margin-top: 16px;
    
    .help-content {
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #606266;
      }
      
      ul {
        margin: 0 0 16px 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 4px;
          font-size: 13px;
          color: #606266;
          line-height: 1.5;
          
          code {
            background: #f5f7fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
            color: #e6a23c;
          }
          
          strong {
            color: #409eff;
          }
        }
      }
    }
  }
  
  .config-preview {
    margin-top: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    
    .preview-title {
      font-size: 12px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 8px;
    }
    
    .preview-content {
      code {
        background: #fff;
        padding: 8px 12px;
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
        color: #e6a23c;
        font-size: 12px;
        display: block;
        border: 1px solid #e4e7ed;
      }
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f2f5;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .el-collapse-item__content {
    padding: 12px 0 0 0;
  }
}
</style> 