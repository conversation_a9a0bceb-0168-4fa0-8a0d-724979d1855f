<template>
  <div class="gateway-security-config">
    <el-form :model="config" :rules="rules" ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="认证方式" prop="type" required>
            <el-select 
              v-model="config.type" 
              placeholder="请选择认证方式"
              @change="handleTypeChange"
            >
              <el-option label="Access Token" value="accessToken" />
              <el-option label="用户名密码" value="usernamePassword" />
              <el-option label="TLS + Access Token" value="tlsAccessToken" />
              <el-option label="TLS + 私钥" value="tlsPrivateKey" />
            </el-select>
            <div class="field-hint">选择与IoTCloud服务器的认证方式</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- Access Token 认证 -->
      <template v-if="config.type === 'accessToken' || config.type === 'tlsAccessToken'">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="Access Token" prop="accessToken" required>
              <el-input 
                v-model="config.accessToken" 
                placeholder="请输入Access Token"
                show-password
                @change="handleChange"
              />
              <div class="field-hint">从IoTCloud设备详情页获取的访问令牌</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 用户名密码认证 -->
      <template v-if="config.type === 'usernamePassword'">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="客户端ID" prop="clientId">
              <el-input 
                v-model="config.clientId" 
                placeholder="请输入客户端ID"
                @change="handleChange"
              />
              <div class="field-hint">MQTT客户端标识符</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户名" prop="username" required>
              <el-input 
                v-model="config.username" 
                placeholder="请输入用户名"
                @change="handleChange"
              />
              <div class="field-hint">IoTCloud用户名</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="密码" prop="password" required>
              <el-input 
                v-model="config.password" 
                placeholder="请输入密码"
                show-password
                @change="handleChange"
              />
              <div class="field-hint">IoTCloud密码</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- TLS 证书配置 -->
      <template v-if="config.type === 'tlsAccessToken' || config.type === 'tlsPrivateKey'">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="CA证书路径" prop="caCert" required>
              <el-input 
                v-model="config.caCert" 
                placeholder="/path/to/ca.crt"
                @change="handleChange"
              />
              <div class="field-hint">CA根证书文件路径</div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- TLS 私钥认证特有配置 -->
        <template v-if="config.type === 'tlsPrivateKey'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="私钥文件路径" prop="privateKey" required>
                <el-input 
                  v-model="config.privateKey" 
                  placeholder="/path/to/private.key"
                  @change="handleChange"
                />
                <div class="field-hint">客户端私钥文件路径</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证书文件路径" prop="cert" required>
                <el-input 
                  v-model="config.cert" 
                  placeholder="/path/to/client.crt"
                  @change="handleChange"
                />
                <div class="field-hint">客户端证书文件路径</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="证书检查周期(秒)" prop="checkCertPeriod">
                <el-input-number 
                  v-model="config.checkCertPeriod" 
                  :min="60"
                  :precision="0"
                  controls-position="right"
                  style="width: 100%"
                  @change="handleChange"
                />
                <div class="field-hint">检查证书有效性的时间间隔</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="证书到期提醒(天)" prop="certificateDaysLeft">
                <el-input-number 
                  v-model="config.certificateDaysLeft" 
                  :min="1"
                  :precision="0"
                  controls-position="right"
                  style="width: 100%"
                  @change="handleChange"
                />
                <div class="field-hint">证书到期前多少天开始提醒</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </template>

      <!-- 认证方式说明 -->
      <el-alert 
        :title="getSecurityTypeDescription()" 
        type="info" 
        :closable="false"
        show-icon
      />
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const config = ref({
  type: 'accessToken',
  accessToken: '',
  clientId: '',
  username: '',
  password: '',
  caCert: '',
  privateKey: '',
  cert: '',
  checkCertPeriod: 3600,
  certificateDaysLeft: 30
})

// 表单验证规则
const rules = ref({
  type: [
    { required: true, message: '请选择认证方式', trigger: 'change' }
  ],
  accessToken: [
    { required: true, message: '请输入Access Token', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  caCert: [
    { required: true, message: '请输入CA证书路径', trigger: 'blur' }
  ],
  privateKey: [
    { required: true, message: '请输入私钥文件路径', trigger: 'blur' }
  ],
  cert: [
    { required: true, message: '请输入证书文件路径', trigger: 'blur' }
  ]
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(config.value, newValue)
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
  emit('change', { ...newConfig })
}, { deep: true })

// 处理认证方式变化
const handleTypeChange = () => {
  // 清空其他认证方式的字段
  const fieldsToReset = {
    accessToken: '',
    clientId: '',
    username: '',
    password: '',
    caCert: '',
    privateKey: '',
    cert: ''
  }
  
  Object.assign(config.value, fieldsToReset)
  handleChange()
}

// 处理配置变化
const handleChange = () => {
  // 触发验证
  formRef.value?.clearValidate()
}

// 获取认证方式说明
const getSecurityTypeDescription = () => {
  const descriptions = {
    accessToken: 'Access Token认证：使用设备访问令牌进行身份验证，适用于大多数场景。',
    usernamePassword: '用户名密码认证：使用IoTCloud用户账号进行身份验证，需要有效的用户凭据。',
    tlsAccessToken: 'TLS + Access Token：在TLS加密连接基础上使用访问令牌，提供更高的安全性。',
    tlsPrivateKey: 'TLS + 私钥：使用客户端证书和私钥进行双向TLS认证，提供最高级别的安全性。'
  }
  return descriptions[config.value.type] || ''
}

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.gateway-security-config {
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
  
  :deep(.el-alert) {
    margin-top: 16px;
  }
}
</style> 