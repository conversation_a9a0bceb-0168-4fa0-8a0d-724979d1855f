[{"timeout": 100, "command": "ps -A -o cpu,%mem | awk '{cpu += $1}END{print cpu}'", "attributeOnGateway": "CPU"}, {"timeout": 100, "command": "ps -A -o %cpu,%mem | awk '{mem += $2}END{print mem}'", "attributeOnGateway": "Memory"}, {"timeout": 100, "command": ["/bin/sh", "-c", "hostname -I"], "attributeOnGateway": "IP address"}, {"timeout": 100, "command": ["/bin/sh", "-c", "cat /etc/*-release | grep \"PRETTY_NAME\" | sed 's/PRETTY_NAME=//g'"], "attributeOnGateway": "OS"}, {"timeout": 100, "command": ["/bin/sh", "-c", "uptime"], "attributeOnGateway": "Uptime"}, {"timeout": 100, "command": ["/bin/sh", "-c", "lsusb"], "attributeOnGateway": "USBs"}]