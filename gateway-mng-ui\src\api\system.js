import request from '@/utils/request'

// 获取系统温度信息
export function getSystemTemperature() {
  return request({
    url: '/gateway/v1/system/temperature',
    method: 'get'
  })
}

// 获取CPU使用率信息
export function getSystemCpu() {
  return request({
    url: '/gateway/v1/system/cpu',
    method: 'get'
  })
}

// 获取内存使用情况
export function getSystemMemory() {
  return request({
    url: '/gateway/v1/system/memory',
    method: 'get'
  })
}

// 获取硬盘使用情况
export function getSystemDisk() {
  return request({
    url: '/gateway/v1/system/disk',
    method: 'get'
  })
}

// 获取系统概览信息（包含所有监控数据）
export function getSystemOverview() {
  return request({
    url: '/gateway/v1/system/overview',
    method: 'get'
  })
} 