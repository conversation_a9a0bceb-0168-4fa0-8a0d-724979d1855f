<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基础端点配置 -->
      <div class="section">
        <div class="section-title">端点配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="端点路径" prop="endpoint" required>
              <el-input 
                v-model="form.endpoint" 
                placeholder="/api/v1/data"
              />
              <div class="field-hint">REST端点的路径</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="HTTP方法" prop="HTTPMethods" required>
              <el-select 
                v-model="form.HTTPMethods" 
                multiple 
                placeholder="选择HTTP方法"
                style="width: 100%"
              >
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
                <el-option label="HEAD" value="HEAD" />
                <el-option label="OPTIONS" value="OPTIONS" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        
        <div class="headers-config">
          <div class="headers-header">
            <span>请求头设置</span>
            <el-button type="primary" size="small" @click="addHeader">
              <el-icon><Plus /></el-icon>
              添加头部
            </el-button>
          </div>
          
          <div v-if="!form.httpHeaders || Object.keys(form.httpHeaders).length === 0" class="empty-headers">
            <el-empty description="暂无HTTP头配置" :image-size="60">
              <el-button type="primary" size="small" @click="addHeader">添加第一个头部</el-button>
            </el-empty>
          </div>
          
          <div v-else class="headers-list">
            <div 
              v-for="(value, key) in form.httpHeaders" 
              :key="key" 
              class="header-item"
            >
              <el-input 
                :model-value="key" 
                placeholder="头部名称"
                @input="updateHeaderKey(key, $event)"
                style="width: 200px"
              />
              <el-input 
                v-model="form.httpHeaders[key]" 
                placeholder="头部值"
                style="flex: 1; margin: 0 8px"
              />
              <el-button 
                type="danger" 
                size="small" 
                @click="removeHeader(key)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全配置 -->
      <div class="section">
        <div class="section-title">安全配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认证类型" prop="security.type">
              <el-select v-model="form.security.type" style="width: 100%">
                <el-option label="匿名访问" value="anonymous" />
                <el-option label="基本认证" value="basic" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.security.type === 'basic'">
            <el-form-item label="用户名" prop="security.username">
              <el-input v-model="form.security.username" placeholder="username" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.security.type === 'basic'">
            <el-form-item label="密码" prop="security.password">
              <el-input 
                v-model="form.security.password" 
                type="password" 
                placeholder="password"
                show-password 
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 数据转换器配置 -->
      <div class="section">
        <div class="section-title">数据转换器</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="转换器类型" prop="converter.type" required>
              <el-select v-model="form.converter.type" style="width: 100%">
                <el-option label="JSON转换器" value="json" />
                <el-option label="自定义转换器" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- JSON转换器配置 -->
        <template v-if="form.converter.type === 'json'">
          <!-- 设备信息配置 -->
          <div class="converter-section">
            <div class="converter-title">设备信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备名表达式" prop="converter.deviceInfo.deviceNameExpression" required>
                  <el-input 
                    v-model="form.converter.deviceInfo.deviceNameExpression" 
                    placeholder="${deviceName}"
                  />
                  <div class="field-hint">设备名称的表达式</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备类型表达式" prop="converter.deviceInfo.deviceTypeExpression">
                  <el-input 
                    v-model="form.converter.deviceInfo.deviceTypeExpression" 
                    placeholder="default"
                  />
                  <div class="field-hint">设备类型的表达式</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 属性配置 -->
          <div class="converter-section">
            <div class="converter-title">
              <span>属性配置</span>
              <el-button type="primary" size="small" @click="addAttribute">
                <el-icon><Plus /></el-icon>
                添加属性
              </el-button>
            </div>
            
            <div v-if="!form.converter.attributes?.length" class="empty-data">
              <el-empty description="暂无属性配置" :image-size="60">
                <el-button type="primary" size="small" @click="addAttribute">添加第一个属性</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-keys-list">
              <RestDataKey
                v-for="(attr, attrIndex) in form.converter.attributes"
                :key="`attr-${attrIndex}`"
                v-model="form.converter.attributes[attrIndex]"
                data-type="attribute"
                @delete="removeAttribute(attrIndex)"
              />
            </div>
          </div>

          <!-- 时序数据配置 -->
          <div class="converter-section">
            <div class="converter-title">
              <span>时序数据配置</span>
              <el-button type="primary" size="small" @click="addTimeseries">
                <el-icon><Plus /></el-icon>
                添加时序数据
              </el-button>
            </div>
            
            <div v-if="!form.converter.timeseries?.length" class="empty-data">
              <el-empty description="暂无时序数据配置" :image-size="60">
                <el-button type="primary" size="small" @click="addTimeseries">添加第一个时序数据</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-keys-list">
              <RestDataKey
                v-for="(ts, tsIndex) in form.converter.timeseries"
                :key="`ts-${tsIndex}`"
                v-model="form.converter.timeseries[tsIndex]"
                data-type="timeseries"
                @delete="removeTimeseries(tsIndex)"
              />
            </div>
          </div>
        </template>

        <!-- 自定义转换器配置 -->
        <template v-if="form.converter.type === 'custom'">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="扩展类名" prop="converter.extension" required>
                <el-input 
                  v-model="form.converter.extension" 
                  placeholder="CustomRestUplinkConverter"
                />
                <div class="field-hint">自定义转换器的类名</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import RestDataKey from './RestDataKey.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mapping: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const formRef = ref()
const saving = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑端点映射' : '添加端点映射'
})

// 默认表单结构
const defaultForm = {
  endpoint: '/api/v1/data',
  HTTPMethods: ['POST'],
  httpHeaders: {
    'Content-Type': 'application/json'
  },
  security: {
    type: 'anonymous'
  },
  converter: {
    type: 'json',
    deviceInfo: {
      deviceNameExpression: '${deviceName}',
      deviceTypeExpression: 'default'
    },
    attributes: [],
    timeseries: []
  }
}

const form = reactive({ ...defaultForm })

// 表单验证规则
const rules = reactive({
  endpoint: [
    { required: true, message: '请输入端点路径', trigger: 'blur' }
  ],
  HTTPMethods: [
    { required: true, message: '请选择HTTP方法', trigger: 'change' }
  ],
  'converter.type': [
    { required: true, message: '请选择转换器类型', trigger: 'change' }
  ],
  'converter.deviceInfo.deviceNameExpression': [
    { required: true, message: '请输入设备名表达式', trigger: 'blur' }
  ],
  'converter.extension': [
    {
      required: true,
      message: '请输入扩展类名',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (form.converter.type === 'custom' && !value) {
          callback(new Error('请输入扩展类名'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 监听映射数据变化
watch(() => props.mapping, (newMapping) => {
  if (newMapping && props.modelValue) {
    Object.assign(form, JSON.parse(JSON.stringify(newMapping)))
  }
}, { deep: true, immediate: true })

// 监听对话框状态
watch(() => props.modelValue, (newVal) => {
  if (newVal && !props.mapping) {
    // 新增时重置表单
    Object.assign(form, JSON.parse(JSON.stringify(defaultForm)))
  }
})

// 添加HTTP头
const addHeader = () => {
  if (!form.httpHeaders) {
    form.httpHeaders = {}
  }
  const newKey = `header-${Date.now()}`
  form.httpHeaders[newKey] = ''
}

// 删除HTTP头
const removeHeader = (key) => {
  delete form.httpHeaders[key]
}

// 更新HTTP头键名
const updateHeaderKey = (oldKey, newKey) => {
  if (oldKey !== newKey && newKey) {
    const value = form.httpHeaders[oldKey]
    delete form.httpHeaders[oldKey]
    form.httpHeaders[newKey] = value
  }
}

// 添加属性
const addAttribute = () => {
  if (!form.converter.attributes) {
    form.converter.attributes = []
  }
  form.converter.attributes.push({
    key: '',
    type: 'string',
    value: ''
  })
}

// 删除属性
const removeAttribute = (index) => {
  form.converter.attributes.splice(index, 1)
}

// 添加时序数据
const addTimeseries = () => {
  if (!form.converter.timeseries) {
    form.converter.timeseries = []
  }
  form.converter.timeseries.push({
    key: '',
    type: 'double',
    value: ''
  })
}

// 删除时序数据
const removeTimeseries = (index) => {
  form.converter.timeseries.splice(index, 1)
}

// 保存配置
const handleSave = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    saving.value = true

    // 深拷贝表单数据
    const mappingData = JSON.parse(JSON.stringify(form))
    
    emit('save', mappingData)
    
    ElMessage.success(props.isEdit ? '端点映射更新成功' : '端点映射添加成功')
    handleClose()
  } catch (error) {
    console.error('保存端点映射失败:', error)
    ElMessage.error('保存端点映射失败')
  } finally {
    saving.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.headers-config {
  .headers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 600;
    color: #606266;
  }

  .empty-headers {
    text-align: center;
    padding: 20px;
  }

  .headers-list {
    .header-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.converter-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  
  .converter-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #606266;
    margin-bottom: 12px;
  }
}

.empty-data {
  text-align: center;
  padding: 20px;
}

.data-keys-list {
  .data-key-item {
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}
</style> 