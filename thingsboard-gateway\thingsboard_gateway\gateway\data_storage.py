import sqlite3
import json
import time
import threading
import random
import string
from datetime import datetime, timedelta
import os
from .data_config import DB_FILE_PATH

class DeviceDataStorage:
    def __init__(self, db_path=None):
        if db_path is None:
            # 直接使用配置的数据库文件路径
            db_path = DB_FILE_PATH
            # 确保数据库目录存在
            db_dir = os.path.dirname(db_path)
            os.makedirs(db_dir, exist_ok=True)
        
        self.db_path = db_path
        self.lock = threading.Lock()
        self.init_database()
    
    def init_database(self):
        """初始化数据库表"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建设备数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS device_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_name TEXT NOT NULL,
                    connector_name TEXT,
                    data_type TEXT NOT NULL,
                    data_content TEXT NOT NULL,
                    timestamp REAL NOT NULL,
                    created_at REAL NOT NULL
                )
            ''')
            
            # 创建连接器活跃时间表（整合sql_local.py的功能）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS connector_activity (
                    connector_name TEXT PRIMARY KEY,
                    last_time TEXT NOT NULL,
                    status INTEGER NOT NULL
                )
            ''')
            
            # 为连接器活跃时间表添加索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_connector_activity_name 
                ON connector_activity(connector_name)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_connector_activity_time 
                ON connector_activity(last_time)
            ''')
            
            # 创建用户表（整合UserOperation功能）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_table (
                    name TEXT NOT NULL,
                    password TEXT NOT NULL,
                    time_stamp INTEGER NOT NULL,
                    token TEXT NOT NULL
                )
            ''')
            
            # 创建索引以提高查询性能
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_device_name 
                ON device_data(device_name)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_timestamp 
                ON device_data(timestamp)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_created_at 
                ON device_data(created_at)
            ''')
            
            # 添加复合索引优化常用查询
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_device_connector 
                ON device_data(device_name, connector_name)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_device_type_time 
                ON device_data(device_name, data_type, timestamp)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_connector_time 
                ON device_data(connector_name, timestamp)
            ''')
            
            conn.commit()
            conn.close()
    
    def _hash_password(self):
        """生成随机密码哈希"""
        random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
        return random_string
    
    # 用户管理功能
    def is_user_exist(self, user_name):
        """检查用户是否存在"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('SELECT name FROM user_table WHERE name = ?', (user_name,))
                exists = cursor.fetchone()
                return exists is not None
            except Exception as e:
                print(f"Error checking user existence: {e}")
                return False
            finally:
                conn.close()
    
    def is_token_exist(self, token):
        """检查token是否存在"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('SELECT token FROM user_table WHERE token = ?', (token,))
                exists = cursor.fetchone()
                return exists is not None
            except Exception as e:
                print(f"Error checking token existence: {e}")
                return False
            finally:
                conn.close()
    
    def register_user(self, user_name, password, time_stamp):
        """注册用户"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT INTO user_table (name, password, time_stamp, token)
                    VALUES (?, ?, ?, ?)
                ''', (user_name, str(password), time_stamp, ''))
                conn.commit()
                return True
            except Exception as e:
                print(f"Error registering user: {e}")
                return False
            finally:
                conn.close()
    
    def login_user(self, user_name):
        """用户登录"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 生成新token
                new_token = self._hash_password()
                cursor.execute('''
                    UPDATE user_table 
                    SET token = ?
                    WHERE name = ?
                ''', (new_token, user_name))
                
                # 获取用户信息
                cursor.execute('''
                    SELECT password, token 
                    FROM user_table 
                    WHERE name = ?
                ''', (user_name,))
                result = cursor.fetchone()
                
                conn.commit()
                return result
            except Exception as e:
                print(f"Error logging in user: {e}")
                return None
            finally:
                conn.close()
    
    def modify_user_password(self, user_name, new_password):
        """修改用户密码"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    UPDATE user_table 
                    SET password = ?
                    WHERE name = ?
                ''', (str(new_password), user_name))
                conn.commit()
                return True
            except Exception as e:
                print(f"Error modifying user password: {e}")
                return False
            finally:
                conn.close()
    
    def logout_user(self, token):
        """用户登出"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    UPDATE user_table 
                    SET token = ''
                    WHERE token = ?
                ''', (token,))
                conn.commit()
                return True
            except Exception as e:
                print(f"Error logging out user: {e}")
                return False
            finally:
                conn.close()
    
    def get_user_by_token(self, token):
        """根据token获取用户信息"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    SELECT name, password 
                    FROM user_table 
                    WHERE token = ?
                ''', (token,))
                result = cursor.fetchone()
                return result
            except Exception as e:
                print(f"Error getting user by token: {e}")
                return None
            finally:
                conn.close()
    
    def get_user_password(self, user_name):
        """获取用户密码"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    SELECT password, token 
                    FROM user_table 
                    WHERE name = ?
                ''', (user_name,))
                result = cursor.fetchone()
                return result
            except Exception as e:
                print(f"Error getting user password: {e}")
                return None
            finally:
                conn.close()

    def update_connector_activity(self, connector_name, status=0):
        """更新连接器活跃时间（整合SqlOperation.add_data功能）"""
        current_time = str(int(time.time()))
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 检查是否已存在记录
                cursor.execute('SELECT connector_name FROM connector_activity WHERE connector_name = ?', (connector_name,))
                exists = cursor.fetchone()
                
                if exists:
                    # 更新现有记录
                    cursor.execute('''
                        UPDATE connector_activity 
                        SET last_time = ?, status = ?
                        WHERE connector_name = ?
                    ''', (current_time, status, connector_name))
                else:
                    # 插入新记录
                    cursor.execute('''
                        INSERT INTO connector_activity (connector_name, last_time, status)
                        VALUES (?, ?, ?)
                    ''', (connector_name, current_time, status))
                
                conn.commit()
            except Exception as e:
                print(f"Error updating connector activity: {e}")
            finally:
                conn.close()
    
    def get_connector_activity_list(self):
        """获取所有连接器的活跃时间（整合SqlOperation.select_data功能）"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('SELECT connector_name, last_time, status FROM connector_activity')
                rows = cursor.fetchall()
                
                data_list = []
                for row in rows:
                    connector_name, last_time, status = row
                    # 过滤掉以"."开头的系统文件
                    if not connector_name.startswith("."):
                        data_list.append({
                            "connector_name": connector_name,
                            "last_time": last_time
                        })
                
                return data_list
            except Exception as e:
                print(f"Error getting connector activity list: {e}")
                return []
            finally:
                conn.close()

    def store_data(self, device_name, data_type, data_content, connector_name=None):
        """存储设备数据"""
        current_time = time.time()
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT INTO device_data 
                    (device_name, connector_name, data_type, data_content, timestamp, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (device_name, connector_name, data_type, json.dumps(data_content), current_time, current_time))
                
                conn.commit()
            except Exception as e:
                print(f"Error storing data: {e}")
            finally:
                conn.close()
    
    def store_device_data(self, data, connector_name):
        """存储设备数据，支持 ConvertedData 和 dict 格式"""
        try:
            if hasattr(data, 'device_name') and hasattr(data, 'telemetry') and hasattr(data, 'attributes'):
                # ConvertedData 格式
                device_name = data.device_name
                
                # 保存遥测数据
                if data.telemetry:
                    # 将 TelemetryEntry 对象转换为可序列化的字典
                    telemetry_data = []
                    for entry in data.telemetry:
                        if hasattr(entry, 'to_dict'):
                            telemetry_data.append(entry.to_dict())
                        else:
                            # 如果不是 TelemetryEntry 对象，直接使用
                            telemetry_data.append(entry)
                    
                    self.store_data(
                        device_name=device_name,
                        data_type="telemetry",
                        data_content=telemetry_data,
                        connector_name=connector_name
                    )
                
                # 保存属性数据
                if data.attributes:
                    # 将 Attributes 对象转换为可序列化的字典
                    if hasattr(data.attributes, 'to_dict'):
                        attributes_data = data.attributes.to_dict()
                    else:
                        # 如果不是 Attributes 对象，直接使用
                        attributes_data = data.attributes
                    
                    self.store_data(
                        device_name=device_name,
                        data_type="attributes",
                        data_content=attributes_data,
                        connector_name=connector_name
                    )
            else:
                # dict 格式
                device_name = data.get("deviceName", "unknown")
                
                # 保存遥测数据
                if data.get("telemetry"):
                    self.store_data(
                        device_name=device_name,
                        data_type="telemetry",
                        data_content=data["telemetry"],
                        connector_name=connector_name
                    )
                
                # 保存属性数据
                if data.get("attributes"):
                    self.store_data(
                        device_name=device_name,
                        data_type="attributes",
                        data_content=data["attributes"],
                        connector_name=connector_name
                    )
        except Exception as e:
            print(f"Error storing device data: {e}")
    
    def get_device_data(self, device_name, page=1, page_size=100):
        """获取指定设备的数据"""
        offset = (page - 1) * page_size
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 获取总记录数
                cursor.execute('''
                    SELECT COUNT(*) FROM device_data 
                    WHERE device_name = ?
                ''', (device_name,))
                total = cursor.fetchone()[0]
                
                # 获取分页数据
                cursor.execute('''
                    SELECT data_type, data_content, timestamp, connector_name
                    FROM device_data 
                    WHERE device_name = ?
                    ORDER BY timestamp DESC
                    LIMIT ? OFFSET ?
                ''', (device_name, page_size, offset))
                
                rows = cursor.fetchall()
                data = []
                
                for row in rows:
                    data_type, data_content, timestamp, connector_name = row
                    try:
                        parsed_content = json.loads(data_content)
                    except:
                        parsed_content = data_content
                    
                    data.append({
                        'deviceName': device_name,
                        'dataType': data_type,
                        'data': parsed_content,
                        'timestamp': timestamp,
                        'connectorName': connector_name,
                        'formattedTime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    })
                
                return {
                    'total': total,
                    'data': data
                }
            except Exception as e:
                print(f"Error getting device data: {e}")
                return {'total': 0, 'data': []}
            finally:
                conn.close()
    
    def get_all_data(self, page=1, page_size=100):
        """获取所有设备的数据"""
        offset = (page - 1) * page_size
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 获取总记录数
                cursor.execute('SELECT COUNT(*) FROM device_data')
                total = cursor.fetchone()[0]
                
                # 获取分页数据
                cursor.execute('''
                    SELECT device_name, data_type, data_content, timestamp, connector_name
                    FROM device_data 
                    ORDER BY timestamp DESC
                    LIMIT ? OFFSET ?
                ''', (page_size, offset))
                
                rows = cursor.fetchall()
                data = []
                
                for row in rows:
                    device_name, data_type, data_content, timestamp, connector_name = row
                    try:
                        parsed_content = json.loads(data_content)
                    except:
                        parsed_content = data_content
                    
                    data.append({
                        'deviceName': device_name,
                        'dataType': data_type,
                        'data': parsed_content,
                        'timestamp': timestamp,
                        'connectorName': connector_name,
                        'formattedTime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    })
                
                return {
                    'total': total,
                    'data': data
                }
            except Exception as e:
                print(f"Error getting all data: {e}")
                return {'total': 0, 'data': []}
            finally:
                conn.close()
    
    def cleanup_old_data(self, days=7):
        """清理超过指定天数的旧数据"""
        cutoff_time = time.time() - (days * 24 * 3600)
        
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    DELETE FROM device_data 
                    WHERE created_at < ?
                ''', (cutoff_time,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                print(f"Cleaned up {deleted_count} old records")
                return deleted_count
            except Exception as e:
                print(f"Error cleaning up old data: {e}")
                return 0
            finally:
                conn.close()
    
    def get_storage_stats(self):
        """获取存储统计信息"""
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            try:
                # 总记录数
                cursor.execute('SELECT COUNT(*) FROM device_data')
                total_records = cursor.fetchone()[0]
                
                # 设备数量
                cursor.execute('SELECT COUNT(DISTINCT device_name) FROM device_data')
                device_count = cursor.fetchone()[0]
                
                # 数据库大小
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                
                return {
                    'total_records': total_records,
                    'device_count': device_count,
                    'db_size_bytes': db_size,
                    'db_size_mb': round(db_size / (1024 * 1024), 2)
                }
            except Exception as e:
                print(f"Error getting storage stats: {e}")
                return {'total_records': 0, 'device_count': 0, 'db_size_bytes': 0, 'db_size_mb': 0}
            finally:
                conn.close()

# 全局存储实例
_data_storage = None

def get_data_storage():
    """获取全局数据存储实例"""
    global _data_storage
    if _data_storage is None:
        _data_storage = DeviceDataStorage()
    return _data_storage 