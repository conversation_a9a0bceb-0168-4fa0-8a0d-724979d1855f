import request from '@/utils/request'
import { getDefaultConfig } from '@/utils/defaultConfigs.js'

// 定义支持的连接器类型
export const SUPPORTED_TYPES = [
  'bacnet', 'ble', 'can', 'ftp', 'gpio', 'knx', 'modbus', 'mqtt',
  'ocpp', 'odbc', 'opcua', 'request', 'rest', 'serial', 'snmp', 'socket', 'xmpp'
]

// 连接器类型中文名称映射
export const CONNECTOR_TYPE_NAMES = {
  'mqtt': 'MQTT',
  'modbus': 'MODBUS',
  'opcua': 'OPC-UA',
  'ble': 'BLUETOOTH',
  'request': 'REQUEST',
  'can': 'CAN',
  'bacnet': 'BACNET',
  'knx': 'KNX',
  'odbc': 'ODBC',
  'rest': 'REST',
  'snmp': 'SNMP',
  'ftp': 'FTP',
  'socket': 'SOCKET',
  'xmpp': 'XMPP',
  'ocpp': 'OCPP',
  'gpio': 'GPIO',
  'serial': 'SERIAL'
}

// 获取连接器类型配置
export function getConnectorTypes() {
  return request({
    url: '/gateway/v1/connector_types',
    method: 'get'
  })
}



// 获取默认连接器配置 - 改为使用本地配置
export function getDefaultConnectorConfig(connectorType) {
  return new Promise((resolve, reject) => {
    try {
      const defaultConfig = getDefaultConfig(connectorType)
      
      if (defaultConfig) {
        resolve({
          msg: 'success',
          data: defaultConfig
        })
      } else {
        reject({
          msg: 'error',
          data: `不支持的连接器类型: ${connectorType}`
        })
      }
    } catch (error) {
      reject({
        msg: 'error',
        data: error.message || '获取默认配置失败'
      })
    }
  })
}

// 获取连接器类型的中文名称
export function getConnectorTypeName(type) {
  // 检查type是否为有效字符串
  if (!type || typeof type !== 'string') {
    return '未知类型'
  }
  return CONNECTOR_TYPE_NAMES[type] || type.toUpperCase()
}

// 获取所有支持的连接器类型信息
export function getSupportedConnectorTypes() {
  return SUPPORTED_TYPES.map(type => ({
    type,
    name: getConnectorTypeName(type),
    value: type
  }))
}

// 根据配置文件名获取连接器元数据
export function getConnectorMetadataByConfigFile(configFile, connectorTypesConfig) {
  if (!connectorTypesConfig || !connectorTypesConfig.connector_instances) {
    return null
  }

  // 从配置文件名提取连接器名称
  const connectorName = configFile.replace('.json', '')
  
  // 从 connector_instances 获取连接器元数据
  return connectorTypesConfig.connector_instances[connectorName] || null
}

// 更新连接器元数据
export function updateConnectorMetadata(connectorKey, metadata) {
  return request({
    url: '/gateway/v1/connector_metadata',
    method: 'post',
    data: {
      connector_key: connectorKey,
      metadata: metadata
    }
  })
}

// 删除连接器元数据
export function deleteConnectorMetadata(connectorKey) {
  return request({
    url: '/gateway/v1/connector_metadata',
    method: 'delete',
    params: { connector_key: connectorKey }
  })
}

// 更新连接器元数据
export function updateConnectorFullMetadata(connectorKey, type, name, isLocalOnly = false) {
  return updateConnectorMetadata(connectorKey, {
    type: type,
    name: name,
    isLocalOnly: isLocalOnly
  })
}

