<template>
  <div class="mqtt-request-mapping-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>请求映射配置</span>
          <div class="header-hint">配置MQTT连接器的请求响应机制</div>
        </div>
      </template>
      
      <el-tabs v-model="activeRequestType" type="card" class="request-tabs">
        <!-- 连接请求标签页 -->
        <el-tab-pane label="连接请求" name="connectRequests">
          <MqttRequestMappingSection
            v-model="requestsMapping.connectRequests"
            request-type="connectRequests"
            title="连接请求配置"
            description="配置设备连接时的请求处理"
          />
        </el-tab-pane>

        <!-- 断开连接请求标签页 -->
        <el-tab-pane label="断开连接请求" name="disconnectRequests">
          <MqttRequestMappingSection
            v-model="requestsMapping.disconnectRequests"
            request-type="disconnectRequests"
            title="断开连接请求配置"
            description="配置设备断开连接时的请求处理"
          />
        </el-tab-pane>

        <!-- 属性请求标签页 -->
        <el-tab-pane label="属性请求" name="attributeRequests">
          <MqttRequestMappingSection
            v-model="requestsMapping.attributeRequests"
            request-type="attributeRequests"
            title="属性请求配置"
            description="配置设备属性请求的处理"
          />
        </el-tab-pane>

        <!-- 属性更新标签页 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <MqttRequestMappingSection
            v-model="requestsMapping.attributeUpdates"
            request-type="attributeUpdates"
            title="属性更新配置"
            description="配置设备属性更新的处理"
          />
        </el-tab-pane>

        <!-- 服务端RPC标签页 -->
        <el-tab-pane label="服务端RPC" name="serverSideRpc">
          <MqttRequestMappingSection
            v-model="requestsMapping.serverSideRpc"
            request-type="serverSideRpc"
            title="服务端RPC配置"
            description="配置服务端RPC调用的处理"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps, nextTick } from 'vue'
import MqttRequestMappingSection from './MqttRequestMappingSection.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const activeRequestType = ref('connectRequests')

// 标准的isInternalUpdate模式
const isInternalUpdate = ref(false)

// 请求映射数据结构
const requestsMapping = reactive({
  connectRequests: [],
  disconnectRequests: [],
  attributeRequests: [],
  attributeUpdates: [],
  serverSideRpc: []
})

// 监听props变化 - 标准模式
watch(() => props.modelValue, (newValue) => {
  if (isInternalUpdate.value) return

  if (newValue && typeof newValue === 'object') {
    Object.assign(requestsMapping, {
      connectRequests: newValue.connectRequests || [],
      disconnectRequests: newValue.disconnectRequests || [],
      attributeRequests: newValue.attributeRequests || [],
      attributeUpdates: newValue.attributeUpdates || [],
      serverSideRpc: newValue.serverSideRpc || []
    })
  }
}, { deep: true, immediate: true })

// 监听请求映射变化 - 标准模式
watch(requestsMapping, (newMapping) => {
  isInternalUpdate.value = true
  nextTick(() => {
    isInternalUpdate.value = false
  })
  
  emit('update:modelValue', { ...newMapping })
}, { deep: true })
</script>

<style lang="scss" scoped>
.mqtt-request-mapping-config {
  padding: 20px;

  .config-card {
    .card-header {
      .header-hint {
        font-size: 13px;
        color: #909399;
        margin-top: 4px;
      }
    }
  }

  .request-tabs {
    margin-top: 20px;
    
    :deep(.el-tabs__header) {
      margin: 0 0 20px 0;
    }
    
    :deep(.el-tabs__content) {
      padding: 0;
    }
    
    :deep(.el-tab-pane) {
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
    }
  }
}

:deep(.el-card__header) {
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__body) {
  padding: 0;
}
</style> 