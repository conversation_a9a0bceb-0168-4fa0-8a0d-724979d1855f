{"passiveScanMode": true, "showMap": false, "scanner": {"timeout": 10000, "deviceName": "Device name"}, "devices": [{"name": "Temperature and humidity sensor", "MACAddress": "4C:65:A8:DF:85:C0", "pollPeriod": 500000, "showMap": false, "timeout": 10000, "connectRetry": 5, "connectRetryInSeconds": 0, "waitAfterConnectRetries": 10, "telemetry": [{"key": "temperature", "method": "notify", "characteristicUUID": "226CAA55-**************-66734470666D", "valueExpression": "[2]"}, {"key": "humidity", "method": "notify", "characteristicUUID": "226CAA55-**************-66734470666D", "valueExpression": "[0]"}], "attributes": [{"key": "name", "method": "read", "characteristicUUID": "00002A00-0000-1000-8000-00805F9B34FB", "valueExpression": "[0:2]cm [2:]A"}, {"key": "values", "method": "read", "characteristicUUID": "00002A00-0000-1000-8000-00805F9B34FB", "valueExpression": "All values: [:]"}], "attributeUpdates": [{"attributeOnThingsBoard": "sharedName", "characteristicUUID": "00002A00-0000-1000-8000-00805F9B34FB"}], "serverSideRpc": [{"methodRPC": "rpcMethod1", "withResponse": true, "characteristicUUID": "00002A00-0000-1000-8000-00805F9B34FB", "methodProcessing": "read"}, {"methodRPC": "rpcMethod2", "withResponse": true, "characteristicUUID": "00002A00-0000-1000-8000-00805F9B34FB", "methodProcessing": "write"}, {"methodRPC": "rpcMethod3", "withResponse": true, "methodProcessing": "scan"}]}]}