<template>
  <div class="mqtt-config">
    <el-tabs v-model="activeTab" class="mqtt-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- MQTT代理配置标签页 -->
      <el-tab-pane label="MQTT代理" name="broker">
        <MqttBrokerConfig 
          v-model="config.broker"
        />
      </el-tab-pane>

      <!-- 数据映射标签页 -->
      <el-tab-pane label="数据映射" name="dataMapping">
        <MqttDataMappingConfig 
          v-model="config.mapping"
        />
      </el-tab-pane>

      <!-- 请求映射标签页 -->
      <el-tab-pane label="请求映射" name="requestMapping">
        <MqttRequestMappingConfig 
          v-model="config.requestsMapping"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import MqttBrokerConfig from './components/MqttBrokerConfig.vue'
import MqttDataMappingConfig from './components/MqttDataMappingConfig.vue'
import MqttRequestMappingConfig from './components/MqttRequestMappingConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')

// 配置数据结构 - 提供基本默认结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  broker: {
    host: '127.0.0.1',
    port: 1883,
    clientId: 'IoTCloud_gateway',
    version: 5,
    maxMessageNumberPerWorker: 10,
    maxNumberOfWorkers: 100,
    sendDataOnlyOnChange: false,
    security: {
      type: 'anonymous'
    }
  },
  mapping: [],
  requestsMapping: {
    connectRequests: [],
    disconnectRequests: [],
    attributeRequests: [],
    attributeUpdates: [],
    serverSideRpc: []
  }
}

// 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 组件挂载时初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'mqtt', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
      
      console.log('MQTT 连接器配置初始化成功')
    } else {
      ElMessage.warning('MQTT 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('MQTT 连接器初始化失败:', error)
    ElMessage.error('MQTT 连接器初始化失败')
  }
})

// 暴露配置数据给父组件
defineExpose({
  mqtt: config
})
</script>

<style lang="scss" scoped>
.mqtt-config {
  .mqtt-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
}
</style>