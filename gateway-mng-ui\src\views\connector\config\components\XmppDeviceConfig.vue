<template>
  <div class="xmpp-device-config">
    <el-form :model="deviceConfig" :rules="configRules" ref="configFormRef" label-width="140px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="设备JID" prop="jid" required>
                <el-input v-model="deviceConfig.jid" placeholder="device@localhost/TMP_1101" />
                <div class="field-hint">设备的XMPP Jabber ID，包含资源标识符</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称表达式" prop="deviceNameExpression" required>
                <el-input v-model="deviceConfig.deviceNameExpression" placeholder="${serialNumber}" />
                <div class="field-hint">设备名称的表达式，支持变量替换</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备类型表达式" prop="deviceTypeExpression" required>
                <el-input v-model="deviceConfig.deviceTypeExpression" placeholder="default" />
                <div class="field-hint">设备类型的表达式，支持变量替换</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <div class="config-section">
            <div class="section-header">
              <h4>属性数据配置</h4>
              <p class="section-description">配置从XMPP消息中提取的属性数据</p>
              <el-button type="primary" @click="handleAddAttribute">
                <el-icon><Plus /></el-icon>添加属性
              </el-button>
            </div>
            
            <div v-if="deviceConfig.attributes.length === 0" class="empty-state">
              <el-empty description="暂无属性配置">
                <el-button type="primary" @click="handleAddAttribute">添加第一个属性</el-button>
              </el-empty>
            </div>
            
            <div v-else class="attribute-list">
              <el-card 
                v-for="(attr, index) in deviceConfig.attributes" 
                :key="index" 
                class="attribute-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span class="card-title">属性 {{ index + 1 }}: {{ attr.key || '未命名' }}</span>
                    <el-button type="danger" text @click="handleRemoveAttribute(index)">
                      <el-icon><Delete /></el-icon>删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item :label="'属性名称'" :prop="'attributes.' + index + '.key'" required>
                      <el-input v-model="attr.key" placeholder="temperature" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="'属性值表达式'" :prop="'attributes.' + index + '.value'" required>
                      <el-input v-model="attr.value" placeholder="${temp}" />
                      <div class="field-hint">支持变量表达式，如 ${temp} 或 ${temp}:${hum}</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="timeseries">
          <div class="config-section">
            <div class="section-header">
              <h4>遥测数据配置</h4>
              <p class="section-description">配置从XMPP消息中提取的时序数据</p>
              <el-button type="primary" @click="handleAddTimeseries">
                <el-icon><Plus /></el-icon>添加遥测
              </el-button>
            </div>
            
            <div v-if="deviceConfig.timeseries.length === 0" class="empty-state">
              <el-empty description="暂无遥测配置">
                <el-button type="primary" @click="handleAddTimeseries">添加第一个遥测</el-button>
              </el-empty>
            </div>
            
            <div v-else class="timeseries-list">
              <el-card 
                v-for="(ts, index) in deviceConfig.timeseries" 
                :key="index" 
                class="timeseries-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span class="card-title">遥测 {{ index + 1 }}: {{ ts.key || '未命名' }}</span>
                    <el-button type="danger" text @click="handleRemoveTimeseries(index)">
                      <el-icon><Delete /></el-icon>删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item :label="'遥测名称'" :prop="'timeseries.' + index + '.key'" required>
                      <el-input v-model="ts.key" placeholder="humidity" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="'遥测值表达式'" :prop="'timeseries.' + index + '.value'" required>
                      <el-input v-model="ts.value" placeholder="${hum}" />
                      <div class="field-hint">支持变量表达式，如 ${hum} 或组合表达式 ${temp}:${hum}</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- 属性更新配置 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <div class="config-section">
            <div class="section-header">
              <h4>属性更新配置</h4>
              <p class="section-description">配置向设备发送属性更新的操作</p>
              <el-button type="primary" @click="handleAddAttributeUpdate">
                <el-icon><Plus /></el-icon>添加属性更新
              </el-button>
            </div>
            
            <div v-if="deviceConfig.attributeUpdates.length === 0" class="empty-state">
              <el-empty description="暂无属性更新配置">
                <el-button type="primary" @click="handleAddAttributeUpdate">添加第一个属性更新</el-button>
              </el-empty>
            </div>
            
            <div v-else class="attribute-update-list">
              <el-card 
                v-for="(update, index) in deviceConfig.attributeUpdates" 
                :key="index" 
                class="attribute-update-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span class="card-title">属性更新 {{ index + 1 }}: {{ update.attributeOnThingsBoard || '未命名' }}</span>
                    <el-button type="danger" text @click="handleRemoveAttributeUpdate(index)">
                      <el-icon><Delete /></el-icon>删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item :label="'IoTCloud属性'" :prop="'attributeUpdates.' + index + '.attributeOnThingsBoard'" required>
                      <el-input v-model="update.attributeOnThingsBoard" placeholder="shared" />
                      <div class="field-hint">IoTCloud平台上的属性名称</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item :label="'值表达式'" :prop="'attributeUpdates.' + index + '.valueExpression'" required>
                      <el-input v-model="update.valueExpression" placeholder='{"${attributeKey}":"${attributeValue}"}' />
                      <div class="field-hint">发送给设备的值表达式，支持JSON格式</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>

        <!-- RPC配置 -->
        <el-tab-pane label="RPC配置" name="serverSideRpc">
          <div class="config-section">
            <div class="section-header">
              <h4>RPC方法配置</h4>
              <p class="section-description">配置设备的远程过程调用方法</p>
              <el-button type="primary" @click="handleAddRpc">
                <el-icon><Plus /></el-icon>添加RPC
              </el-button>
            </div>
            
            <div v-if="deviceConfig.serverSideRpc.length === 0" class="empty-state">
              <el-empty description="暂无RPC配置">
                <el-button type="primary" @click="handleAddRpc">添加第一个RPC</el-button>
              </el-empty>
            </div>
            
            <div v-else class="rpc-list">
              <el-card 
                v-for="(rpc, index) in deviceConfig.serverSideRpc" 
                :key="index" 
                class="rpc-card"
                shadow="hover"
              >
                <template #header>
                  <div class="card-header">
                    <span class="card-title">RPC {{ index + 1 }}: {{ rpc.methodRPC || '未命名' }}</span>
                    <el-button type="danger" text @click="handleRemoveRpc(index)">
                      <el-icon><Delete /></el-icon>删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item :label="'RPC方法名'" :prop="'serverSideRpc.' + index + '.methodRPC'" required>
                      <el-input v-model="rpc.methodRPC" placeholder="rpc1" />
                      <div class="field-hint">RPC方法的名称标识</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="'需要响应'">
                      <el-switch v-model="rpc.withResponse" />
                      <div class="field-hint">是否需要设备返回响应</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item :label="'值表达式'" :prop="'serverSideRpc.' + index + '.valueExpression'" required>
                      <el-input v-model="rpc.valueExpression" placeholder="${params}" />
                      <div class="field-hint">发送给设备的参数表达式</div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 表单引用和活动标签页
const configFormRef = ref(null)
const activeTab = ref('basic')

// 配置数据
const deviceConfig = reactive({
  jid: 'device@localhost/TMP_1101',
  deviceNameExpression: '${serialNumber}',
  deviceTypeExpression: 'default',
  attributes: [],
  timeseries: [],
  attributeUpdates: [],
  serverSideRpc: [],
  ...props.modelValue
})

// 表单验证规则
const configRules = {
  jid: [{ required: true, message: '请输入设备JID', trigger: 'blur' }],
  deviceNameExpression: [{ required: true, message: '请输入设备名称表达式', trigger: 'blur' }],
  deviceTypeExpression: [{ required: true, message: '请输入设备类型表达式', trigger: 'blur' }]
}

// 监听配置变化
watch(deviceConfig, (newVal) => {
  emit('update:modelValue', newVal)
}, { deep: true })

// 初始化数据时处理props变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    Object.assign(deviceConfig, newVal)
  }
}, { deep: true, immediate: true })

// 属性操作
const handleAddAttribute = () => {
  deviceConfig.attributes.push({
    key: '',
    value: ''
  })
}

const handleRemoveAttribute = (index) => {
  deviceConfig.attributes.splice(index, 1)
}

// 遥测操作
const handleAddTimeseries = () => {
  deviceConfig.timeseries.push({
    key: '',
    value: ''
  })
}

const handleRemoveTimeseries = (index) => {
  deviceConfig.timeseries.splice(index, 1)
}

// 属性更新操作
const handleAddAttributeUpdate = () => {
  deviceConfig.attributeUpdates.push({
    attributeOnThingsBoard: '',
    valueExpression: ''
  })
}

const handleRemoveAttributeUpdate = (index) => {
  deviceConfig.attributeUpdates.splice(index, 1)
}

// RPC操作
const handleAddRpc = () => {
  deviceConfig.serverSideRpc.push({
    methodRPC: '',
    withResponse: true,
    valueExpression: ''
  })
}

const handleRemoveRpc = (index) => {
  deviceConfig.serverSideRpc.splice(index, 1)
}
</script>

<style scoped>
.xmpp-device-config {
  padding: 0;
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.config-section {
  .section-header {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .section-description {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: #606266;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
}

.attribute-card,
.timeseries-card,
.attribute-update-card,
.rpc-card {
  margin-bottom: 16px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-weight: 600;
      color: #303133;
    }
  }
}

:deep(.el-tabs__content) {
  padding: 20px 0;
}

:deep(.el-card__header) {
  padding: 12px 20px;
  background: #f5f7fa;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 