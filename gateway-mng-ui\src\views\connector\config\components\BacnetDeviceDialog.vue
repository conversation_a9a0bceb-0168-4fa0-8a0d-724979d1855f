<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑设备' : '添加设备'"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="deviceForm" :rules="rules" ref="formRef" label-width="140px">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备名称表达式" prop="deviceInfo.deviceNameExpression" required>
                <el-input 
                  v-model="deviceForm.deviceInfo.deviceNameExpression" 
                  placeholder="BACnet Device ${objectName}"
                />
                <div class="field-hint">支持变量表达式，如 ${objectName}</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="名称表达式源" prop="deviceInfo.deviceNameExpressionSource">
                <el-select 
                  v-model="deviceForm.deviceInfo.deviceNameExpressionSource" 
                  placeholder="请选择表达式源类型"
                >
                  <el-option label="表达式" value="expression" />
                  <el-option label="常量" value="constant" />
                </el-select>
                <div class="field-hint">设备名称表达式的数据源类型</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备类型表达式" prop="deviceInfo.deviceProfileExpression">
                <el-input 
                  v-model="deviceForm.deviceInfo.deviceProfileExpression" 
                  placeholder="default"
                />
                <div class="field-hint">设备配置文件名称</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类型表达式源" prop="deviceInfo.deviceProfileExpressionSource">
                <el-select 
                  v-model="deviceForm.deviceInfo.deviceProfileExpressionSource" 
                  placeholder="请选择表达式源类型"
                >
                  <el-option label="表达式" value="expression" />
                  <el-option label="常量" value="constant" />
                </el-select>
                <div class="field-hint">设备类型表达式的数据源类型</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="设备地址" prop="host" required>
                <el-input 
                  v-model="deviceForm.host" 
                  placeholder="*************"
                />
                <div class="field-hint">BACnet设备的IP地址</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备端口" prop="port" required>
                <el-input-number 
                  v-model.number="deviceForm.port" 
                  :min="1" 
                  :max="65535"
                  controls-position="right"
                  placeholder="47808"
                />
                <div class="field-hint">BACnet通信端口</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="轮询周期" prop="pollPeriod" required>
                <el-input-number 
                  v-model.number="deviceForm.pollPeriod" 
                  :min="100" 
                  :step="100"
                  controls-position="right"
                  placeholder="10000"
                />
                <span class="unit-suffix">毫秒</span>
                <div class="field-hint">数据采集间隔时间</div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 高级配置 -->
          <el-card class="advanced-settings">
            <template #header>
              <div class="card-header">
                <span>高级配置</span>
                <el-button 
                  type="text" 
                  @click="showAdvanced = !showAdvanced"
                  :icon="showAdvanced ? ArrowUp : ArrowDown"
                >
                  {{ showAdvanced ? '收起' : '展开' }}
                </el-button>
              </div>
            </template>
            
            <el-collapse-transition>
              <div v-show="showAdvanced">
                <el-form-item label="备用响应地址">
                  <el-tag
                    v-for="(address, index) in deviceForm.altResponsesAddresses"
                    :key="index"
                    closable
                    @close="removeAltAddress(index)"
                    style="margin-right: 8px; margin-bottom: 8px;"
                  >
                    {{ address }}
                  </el-tag>
                  <el-input
                    v-if="showAddressInput"
                    ref="addressInputRef"
                    v-model="newAddress"
                    size="small"
                    style="width: 200px;"
                    @keyup.enter="addAltAddress"
                    @blur="addAltAddress"
                  />
                  <el-button
                    v-else
                    size="small"
                    @click="showAddressInput = true"
                  >
                    + 添加地址
                  </el-button>
                  <div class="field-hint">设备的备用响应地址列表</div>
                </el-form-item>
              </div>
            </el-collapse-transition>
          </el-card>
        </el-tab-pane>

        <!-- 属性配置 -->
        <el-tab-pane label="属性配置" name="attributes">
          <BacnetDataKeys
            v-model="deviceForm.attributes"
            data-type="attributes"
            title="属性数据配置"
            description="配置从设备读取的属性数据"
          />
        </el-tab-pane>

        <!-- 遥测配置 -->
        <el-tab-pane label="遥测配置" name="timeseries">
          <BacnetDataKeys
            v-model="deviceForm.timeseries"
            data-type="timeseries"
            title="遥测数据配置"
            description="配置从设备读取的遥测数据"
          />
        </el-tab-pane>

        <!-- 属性更新 -->
        <el-tab-pane label="属性更新" name="attributeUpdates">
          <BacnetDataKeys
            v-model="deviceForm.attributeUpdates"
            data-type="attributeUpdates"
            title="属性更新配置"
            description="配置向设备写入属性的操作"
          />
        </el-tab-pane>

        <!-- RPC配置 -->
        <el-tab-pane label="RPC配置" name="serverSideRpc">
          <BacnetDataKeys
            v-model="deviceForm.serverSideRpc"
            data-type="serverSideRpc"
            title="RPC方法配置"
            description="配置设备的远程过程调用方法"
          />
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, defineEmits, defineProps } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import BacnetDataKeys from './BacnetDataKeys.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  device: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const addressInputRef = ref()
const activeTab = ref('basic')
const showAdvanced = ref(false)
const showAddressInput = ref(false)
const newAddress = ref('')
const saving = ref(false)

// 设备表单数据
const deviceForm = reactive({
  deviceInfo: {
    deviceNameExpression: 'BACnet Device ${objectName}',
    deviceProfileExpression: 'default',
    deviceNameExpressionSource: 'expression',
    deviceProfileExpressionSource: 'constant'
  },
  host: '*************',
  port: 47808,
  pollPeriod: 10000,
  attributes: [],
  timeseries: [],
  attributeUpdates: [],
  serverSideRpc: [],
  altResponsesAddresses: []
})

// 表单验证规则
const rules = reactive({
  'deviceInfo.deviceNameExpression': [
    { required: true, message: '请输入设备名称表达式', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入设备地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入设备端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围为 1-65535', trigger: 'blur' }
  ],
  pollPeriod: [
    { required: true, message: '请输入轮询周期', trigger: 'blur' },
    { type: 'number', min: 100, message: '轮询周期不能小于100毫秒', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.device) {
    // 加载设备数据
    Object.assign(deviceForm, {
      deviceInfo: {
        deviceNameExpression: 'BACnet Device ${objectName}',
        deviceProfileExpression: 'default',
        deviceNameExpressionSource: 'expression',
        deviceProfileExpressionSource: 'constant'
      },
      host: '*************',
      port: 47808,
      pollPeriod: 10000,
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      serverSideRpc: [],
      altResponsesAddresses: [],
      ...props.device
    })
    
    // 确保端口是数字类型
    if (typeof deviceForm.port === 'string') {
      deviceForm.port = parseInt(deviceForm.port) || 47808
    }
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    // 重置表单
    activeTab.value = 'basic'
    showAdvanced.value = false
    showAddressInput.value = false
    newAddress.value = ''
  }
})

// 添加备用地址
const addAltAddress = () => {
  if (newAddress.value && !deviceForm.altResponsesAddresses.includes(newAddress.value)) {
    deviceForm.altResponsesAddresses.push(newAddress.value)
    newAddress.value = ''
  }
  showAddressInput.value = false
}

// 删除备用地址
const removeAltAddress = (index) => {
  deviceForm.altResponsesAddresses.splice(index, 1)
}

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据 - 保持数字类型
    const saveData = {
      ...deviceForm
    }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '设备更新成功' : '设备添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}

// 显示地址输入框时聚焦
watch(showAddressInput, (newValue) => {
  if (newValue) {
    nextTick(() => {
      addressInputRef.value?.focus()
    })
  }
})
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.unit-suffix {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.advanced-settings {
  margin-top: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .el-button {
      padding: 0;
      font-size: 14px;
      
      .el-icon {
        margin-left: 4px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-dialog__body) {
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  
  .el-input-number {
    width: 100%;
  }
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 