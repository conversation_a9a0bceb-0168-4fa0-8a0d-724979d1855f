<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="clearfix">
          <span>数据列表</span>
          <div style="float: right">
            <el-select v-model="selectedDevice" placeholder="选择设备" style="width: 200px; margin-right: 10px" @change="handleDeviceChange">
              <el-option
                label="全部"
                value="">
              </el-option>
              <el-option
                v-for="device in deviceList"
                :key="device.device_name"
                :label="device.device_name"
                :value="device.device_name">
              </el-option>
            </el-select>
            <el-button type="text" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="dataList"
        style="width: 100%">
        <el-table-column
          prop="deviceName"
          label="设备名称"
          width="360">
        </el-table-column>
        <el-table-column
          prop="dataType"
          label="数据类型"
          width="120">
        </el-table-column>
        <el-table-column
          prop="formattedTime"
          label="时间"
          width="180">
        </el-table-column>
        <el-table-column
          label="数据">
          <template #default="scope">
            <div>
              <div v-if="scope.row.dataType === 'attributes'">
                <pre>{{ JSON.stringify(scope.row.data, null, 2) }}</pre>
              </div>
              <div v-else-if="scope.row.dataType === 'telemetry'">
                <div v-for="(item, index) in scope.row.data" :key="index">
                  <pre>{{ JSON.stringify(item.values, null, 2) }}</pre>
                </div>
              </div>
              <div v-else>
                <pre>{{ JSON.stringify(scope.row.data, null, 2) }}</pre>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total">
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'DeviceData',
  data() {
    return {
      loading: false,
      deviceList: [],
      selectedDevice: '',
      dataList: [],
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  created() {
    this.getDeviceList()
    if (this.$route.query.deviceName) {
      this.selectedDevice = this.$route.query.deviceName
    }
    // 默认获取所有数据
    this.getDeviceData()
  },
  methods: {
    async getDeviceList() {
      try {
        const response = await request({
          url: '/gateway/v1/devices/active',
          method: 'get'
        })
        if (response.msg === 'success') {
          this.deviceList = response.data
        } else {
          this.$message.error(response.data)
        }
      } catch (error) {
        this.$message.error('获取设备列表失败')
        console.error(error)
      }
    },
    async getDeviceData() {
      this.loading = true
      try {
        let response
        if (this.selectedDevice) {
          // 获取指定设备的数据
          response = await request({
            url: '/gateway/v1/device/data',
            method: 'get',
            params: {
              device_name: this.selectedDevice,
              page: this.currentPage,
              page_size: this.pageSize
            }
          })
        } else {
          // 获取所有设备的数据
          response = await request({
            url: '/gateway/v1/all_device_data',
            method: 'get',
            params: {
              page: this.currentPage,
              page_size: this.pageSize
            }
          })
        }
        
        if (response.msg === 'success') {
          this.dataList = response.data.items || response.data.data
          this.total = response.data.total
        } else {
          this.$message.error(response.data)
        }
      } catch (error) {
        this.$message.error('获取设备数据失败')
        console.error(error)
      }
      this.loading = false
    },
    handleDeviceChange() {
      this.currentPage = 1
      this.getDeviceData()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getDeviceData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDeviceData()
    },
    refreshData() {
      this.getDeviceData()
    },
    formatTimestamp(timestamp) {
      if (!timestamp) return ''
      
      // 如果是数字，按Unix时间戳处理
      if (typeof timestamp === 'number') {
        const date = new Date(timestamp)
        return date.toLocaleString()
      }
      
      // 如果是字符串，直接返回
      return timestamp
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 