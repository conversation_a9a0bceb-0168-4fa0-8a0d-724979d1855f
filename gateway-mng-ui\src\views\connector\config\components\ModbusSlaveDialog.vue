<template>
  <div class="modbus-slave-dialog">
    <el-form :model="slaveConfig" :rules="slaveRules" ref="slaveFormRef" label-width="140px">
      <!-- 基础连接配置 -->
      <div class="config-section">
        <div class="section-title">连接配置</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="设备名称" prop="deviceName" required>
              <el-input v-model="slaveConfig.deviceName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="连接类型" prop="type" required>
              <el-select v-model="slaveConfig.type" @change="handleTypeChange">
                <el-option label="TCP" value="tcp" />
                <el-option label="UDP" value="udp" />
                <el-option label="串口" value="serial" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通信方法" prop="method">
              <el-input v-model="slaveConfig.method" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8" v-if="slaveConfig.type !== 'serial'">
            <el-form-item label="主机地址" prop="host" required>
              <el-input v-model="slaveConfig.host" placeholder="*************" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="slaveConfig.type === 'serial' ? '串口号' : '端口'" prop="port" required>
              <el-input v-model="slaveConfig.port" :placeholder="slaveConfig.type === 'serial' ? '/dev/ttyUSB0' : '502'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单元ID" prop="unitId" required>
              <el-input-number v-model="slaveConfig.unitId" :min="1" :max="255" controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 串口特有配置 -->
        <el-row :gutter="20" v-if="slaveConfig.type === 'serial'">
          <el-col :span="8">
            <el-form-item label="波特率" prop="baudrate">
              <el-select v-model="slaveConfig.baudrate">
                <el-option v-for="rate in baudrateOptions" :key="rate" :label="rate" :value="rate" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据位" prop="bytesize">
              <el-select v-model="slaveConfig.bytesize">
                <el-option v-for="size in byteSizeOptions" :key="size" :label="size" :value="size" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="停止位" prop="stopbits">
              <el-select v-model="slaveConfig.stopbits">
                <el-option label="1" value="1" />
                <el-option label="1.5" value="1.5" />
                <el-option label="2" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="slaveConfig.type === 'serial'">
          <el-col :span="8">
            <el-form-item label="奇偶校验" prop="parity">
              <el-select v-model="slaveConfig.parity">
                <el-option label="无校验 (N)" value="N" />
                <el-option label="偶校验 (E)" value="E" />
                <el-option label="奇校验 (O)" value="O" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="严格模式">
              <el-switch v-model="slaveConfig.strict" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重新打包">
              <el-switch v-model="slaveConfig.repack" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 设备信息配置 -->
      <div class="config-section">
        <div class="section-title">设备信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-input v-model="slaveConfig.deviceType" placeholder="default" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="超时时间(秒)" prop="timeout">
              <el-input-number v-model="slaveConfig.timeout" :min="1" :precision="1" controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="轮询周期(ms)" prop="pollPeriod">
              <el-input-number v-model="slaveConfig.pollPeriod" :min="100" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求间延迟(ms)" prop="delay_between_requests_ms">
              <el-input-number v-model="slaveConfig.delay_between_requests_ms" :min="0" controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 高级设置 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="数据处理设置" name="data">
          <div class="config-section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="字节顺序" prop="byteOrder">
                  <el-select v-model="slaveConfig.byteOrder">
                    <el-option label="LITTLE" value="LITTLE" />
                    <el-option label="BIG" value="BIG" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字顺序" prop="wordOrder">
                  <el-select v-model="slaveConfig.wordOrder">
                    <el-option label="LITTLE" value="LITTLE" />
                    <el-option label="BIG" value="BIG" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="重试连接">
                  <el-switch v-model="slaveConfig.retries" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="空数据重发">
                  <el-switch v-model="slaveConfig.retryOnEmpty" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="无效数据重发">
                  <el-switch v-model="slaveConfig.retryOnInvalid" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>

        <el-collapse-item title="报告策略配置" name="report">
          <div class="config-section">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="报告策略" prop="reportStrategy.type">
                  <el-select v-model="slaveConfig.reportStrategy.type">
                    <el-option label="收到数据时上报" value="ON_RECEIVED" />
                    <el-option label="数据变化时上报" value="ON_CHANGE" />
                    <el-option label="定期上报" value="ON_REPORT_PERIOD" />
                    <el-option label="变化或定期上报" value="ON_CHANGE_OR_REPORT_PERIOD" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="报告周期(ms)" prop="reportStrategy.reportPeriod" v-if="slaveConfig.reportStrategy.type === 'ON_REPORT_PERIOD' || slaveConfig.reportStrategy.type === 'ON_CHANGE_OR_REPORT_PERIOD'">
                  <el-input-number v-model="slaveConfig.reportStrategy.reportPeriod" :min="1000" controls-position="right" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="仅变化时发送">
                  <el-switch v-model="slaveConfig.sendDataOnlyOnChange" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>

        <el-collapse-item title="连接重试配置" name="retry">
          <div class="config-section">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="重连间隔(ms)" prop="connectAttemptTimeMs">
                  <el-input-number v-model="slaveConfig.connectAttemptTimeMs" :min="500" controls-position="right" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="最大重连次数" prop="connectAttemptCount">
                  <el-input-number v-model="slaveConfig.connectAttemptCount" :min="1" controls-position="right" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="失败后等待(ms)" prop="waitAfterFailedAttemptsMs">
                  <el-input-number v-model="slaveConfig.waitAfterFailedAttemptsMs" :min="30000" controls-position="right" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>

        <el-collapse-item title="TLS安全配置" name="security">
          <div class="config-section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="证书文件路径">
                  <el-input v-model="slaveConfig.security.certfile" placeholder="/path/to/cert.pem" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="私钥文件路径">
                  <el-input v-model="slaveConfig.security.keyfile" placeholder="/path/to/key.pem" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="密码">
                  <el-input v-model="slaveConfig.security.password" type="password" show-password />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="服务器主机名">
                  <el-input v-model="slaveConfig.security.server_hostname" placeholder="服务器主机名" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- 数据点配置 -->
      <div class="config-section">
        <div class="section-title">数据点配置</div>
        <ModbusDataValues 
          v-model="slaveDataPoints" 
          :is-slave="false" 
          @update:modelValue="handleDataPointsUpdate" />
      </div>
    </el-form>

    <!-- 对话框底部按钮 -->
    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        {{ isEdit ? '更新' : '添加' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import ModbusDataValues from './ModbusDataValues.vue'

// Props
const props = defineProps({
  slaveConfig: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['save', 'cancel'])

// 响应式数据
const slaveFormRef = ref(null)
const saving = ref(false)
const activeCollapse = ref(['data'])

// 数据点配置
const slaveDataPoints = reactive({
  attributes: [],
  timeseries: [],
  attributeUpdates: [],
  rpc: []
})

const slaveConfig = reactive({
  name: '',
  host: '*************',
  port: 502,
  type: 'tcp',
  method: 'socket',
  timeout: 35,
  byteOrder: 'LITTLE',
  wordOrder: 'LITTLE',
  retries: true,
  retryOnEmpty: true,
  retryOnInvalid: true,
  pollPeriod: 5000,
  unitId: 1,
  deviceName: '新建Modbus设备',
  deviceType: 'default',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  sendDataOnlyOnChange: true,
  connectAttemptTimeMs: 5000,
  connectAttemptCount: 5,
  waitAfterFailedAttemptsMs: 300000,
  delay_between_requests_ms: 0,
  attributes: [],
  timeseries: [],
  attributeUpdates: [],
  rpc: [],
  security: {
    certfile: '',
    keyfile: '',
    password: '',
    server_hostname: ''
  },
  // 串口特有配置
  baudrate: 9600,
  bytesize: 8,
  stopbits: '1',
  parity: 'N',
  strict: true,
  repack: false,
  ...props.slaveConfig
})

// 选项配置
const baudrateOptions = [4800, 9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]
const byteSizeOptions = [5, 6, 7, 8]

// 表单验证规则
const slaveRules = reactive({
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择连接类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' }
  ],
  unitId: [
    { required: true, message: '请输入单元ID', trigger: 'blur' },
    { type: 'number', min: 1, max: 255, message: '单元ID范围为1-255', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请输入超时时间', trigger: 'blur' },
    { type: 'number', min: 1, message: '超时时间不能小于1秒', trigger: 'blur' }
  ],
  pollPeriod: [
    { required: true, message: '请输入轮询周期', trigger: 'blur' },
    { type: 'number', min: 100, message: '轮询周期不能小于100ms', trigger: 'blur' }
  ]
})

// 方法
const handleTypeChange = (type) => {
  if (type === 'serial') {
    slaveConfig.method = 'rtu'
    slaveConfig.port = '/dev/ttyUSB0'
  } else {
    slaveConfig.method = 'socket'
    slaveConfig.port = 502
  }
}

const handleDataPointsUpdate = (updatedDataPoints) => {
  // 检查是否真的有变化，避免不必要的更新
  let hasChanges = false
  
  Object.keys(updatedDataPoints).forEach(key => {
    if (Array.isArray(updatedDataPoints[key])) {
      // 只在数据真正变化时才更新
      if (JSON.stringify(updatedDataPoints[key]) !== JSON.stringify(slaveDataPoints[key])) {
        slaveConfig[key] = [...updatedDataPoints[key]]
        slaveDataPoints[key] = [...updatedDataPoints[key]]
        hasChanges = true
      }
    }
  })
  
  // 只在有变化时输出调试信息
  if (hasChanges) {
    console.log('数据点配置已更新:', {
      attributes: slaveDataPoints.attributes?.length || 0,
      timeseries: slaveDataPoints.timeseries?.length || 0,
      attributeUpdates: slaveDataPoints.attributeUpdates?.length || 0,
      rpc: slaveDataPoints.rpc?.length || 0
    })
  }
}

const handleSave = async () => {
  try {
    await slaveFormRef.value.validate()
    saving.value = true
    
    // 清理不需要的字段
    const configToSave = { ...slaveConfig }
    if (configToSave.type !== 'serial') {
      delete configToSave.baudrate
      delete configToSave.bytesize
      delete configToSave.stopbits
      delete configToSave.parity
      delete configToSave.strict
      delete configToSave.repack
    }
    
    // 确保数据点配置是最新的，使用深拷贝
    configToSave.attributes = [...(slaveDataPoints.attributes || [])]
    configToSave.timeseries = [...(slaveDataPoints.timeseries || [])]
    configToSave.attributeUpdates = [...(slaveDataPoints.attributeUpdates || [])]
    configToSave.rpc = [...(slaveDataPoints.rpc || [])]
    
    // 调试信息
    console.log('保存前的数据点配置:', {
      attributes: configToSave.attributes.length,
      timeseries: configToSave.timeseries.length,
      attributeUpdates: configToSave.attributeUpdates.length,
      rpc: configToSave.rpc.length
    })
    
    emit('save', configToSave)
    ElMessage.success(props.isEdit ? '从机设备配置已更新' : '从机设备配置已添加')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单配置')
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听外部配置变化（避免深度监听导致循环更新）
watch(() => props.slaveConfig, (newConfig) => {
  if (!newConfig) return
  
  // 只在需要时更新配置，避免循环更新
  Object.assign(slaveConfig, newConfig)
  
  // 初始化数据点配置，使用条件检查避免不必要的更新
  if (newConfig.attributes && JSON.stringify(newConfig.attributes) !== JSON.stringify(slaveDataPoints.attributes)) {
    slaveDataPoints.attributes = [...newConfig.attributes]
  }
  if (newConfig.timeseries && JSON.stringify(newConfig.timeseries) !== JSON.stringify(slaveDataPoints.timeseries)) {
    slaveDataPoints.timeseries = [...newConfig.timeseries]
  }
  if (newConfig.attributeUpdates && JSON.stringify(newConfig.attributeUpdates) !== JSON.stringify(slaveDataPoints.attributeUpdates)) {
    slaveDataPoints.attributeUpdates = [...newConfig.attributeUpdates]
  }
  if (newConfig.rpc && JSON.stringify(newConfig.rpc) !== JSON.stringify(slaveDataPoints.rpc)) {
    slaveDataPoints.rpc = [...newConfig.rpc]
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.modbus-slave-dialog {
  .config-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
    margin-top: 20px;
  }

  :deep(.el-collapse) {
    border: none;
    
    .el-collapse-item__header {
      background: #f8f9fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 0 16px;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .el-collapse-item__content {
      padding: 16px 0;
      border: none;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input), :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-switch) {
    --el-switch-on-color: #409eff;
  }
}
</style> 