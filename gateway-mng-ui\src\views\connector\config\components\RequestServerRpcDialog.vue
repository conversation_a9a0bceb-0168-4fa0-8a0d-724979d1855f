<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <!-- 基础请求配置 -->
      <div class="section">
        <div class="section-title">请求配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="HTTP方法" prop="httpMethod" required>
              <el-select v-model="form.httpMethod">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="响应超时(秒)" prop="responseTimeout">
              <el-input-number 
                v-model="form.responseTimeout" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="超时(秒)" prop="timeout">
              <el-input-number 
                v-model="form.timeout" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重试次数" prop="tries">
              <el-input-number 
                v-model="form.tries" 
                :min="0" 
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 过滤器配置 -->
      <div class="section">
        <div class="section-title">过滤器配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名筛选" prop="deviceNameFilter" required>
              <el-input 
                v-model="form.deviceNameFilter" 
                placeholder=".*"
              />
              <div class="field-hint">正则表达式，用于匹配设备名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方法筛选" prop="methodFilter">
              <el-input 
                v-model="form.methodFilter" 
                placeholder="setValue"
              />
              <div class="field-hint">正则表达式，用于匹配RPC方法名</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 表达式配置 -->
      <div class="section">
        <div class="section-title">表达式配置</div>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请求URL表达式" prop="requestUrlExpression" required>
              <el-input 
                v-model="form.requestUrlExpression" 
                placeholder="sensor/${deviceName}/set"
              />
              <div class="field-hint">
                支持变量: ${deviceName}, ${methodName}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求值表达式" prop="requestValueExpression">
              <el-input 
                v-model="form.requestValueExpression" 
                type="textarea"
                :rows="3"
                placeholder='{"method":"${methodName}","params":${params}}'
              />
              <div class="field-hint">
                支持变量: ${methodName}, ${params}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="响应值表达式" prop="responseValueExpression">
              <el-input 
                v-model="form.responseValueExpression" 
                type="textarea"
                :rows="3"
                placeholder='${response}'
              />
              <div class="field-hint">
                从响应中提取返回值的表达式
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        <RequestHttpHeaders v-model="form.httpHeaders" />
      </div>

      <!-- 表达式帮助 -->
      <div class="section">
        <el-collapse>
          <el-collapse-item name="help" title="表达式变量说明">
            <div class="help-content">
              <div class="help-section">
                <h4>URL表达式变量</h4>
                <ul>
                  <li><code>${deviceName}</code> - 设备名称</li>
                  <li><code>${methodName}</code> - RPC方法名</li>
                </ul>
              </div>
              <div class="help-section">
                <h4>请求值表达式变量</h4>
                <ul>
                  <li><code>${methodName}</code> - RPC方法名</li>
                  <li><code>${params}</code> - RPC参数对象</li>
                </ul>
              </div>
              <div class="help-section">
                <h4>响应值表达式变量</h4>
                <ul>
                  <li><code>${response}</code> - 完整响应对象</li>
                  <li><code>${response.result}</code> - 响应结果字段</li>
                </ul>
              </div>
              <div class="help-section">
                <h4>示例配置</h4>
                <ul>
                  <li>URL: <code>api/device/${deviceName}/rpc/${methodName}</code></li>
                  <li>请求值: <code>{"method":"${methodName}","parameters":${params}}</code></li>
                  <li>响应值: <code>${response.data}</code></li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import RequestHttpHeaders from './RequestHttpHeaders.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  serverRpc: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const formRef = ref()
const saving = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  return props.isEdit ? '编辑RPC配置' : '添加RPC配置'
})

// 默认表单结构
const defaultForm = {
  deviceNameFilter: '.*',
  methodFilter: '',
  requestUrlExpression: '',
  responseTimeout: 1,
  httpMethod: 'GET',
  requestValueExpression: '',
  responseValueExpression: '',
  timeout: 0.5,
  tries: 3,
  httpHeaders: {
    'Content-Type': 'application/json'
  }
}

const form = reactive({ ...defaultForm })

// 表单验证规则
const rules = reactive({
  httpMethod: [
    { required: true, message: '请选择HTTP方法', trigger: 'change' }
  ],
  deviceNameFilter: [
    { required: true, message: '请输入设备名筛选', trigger: 'blur' }
  ],
  requestUrlExpression: [
    { required: true, message: '请输入请求URL表达式', trigger: 'blur' }
  ]
})

// 监听数据变化
watch(() => props.serverRpc, (newData) => {
  if (newData && props.modelValue) {
    Object.assign(form, JSON.parse(JSON.stringify(newData)))
  }
}, { deep: true, immediate: true })

// 监听对话框状态
watch(() => props.modelValue, (newVal) => {
  if (newVal && !props.serverRpc) {
    // 新增时重置表单
    Object.assign(form, JSON.parse(JSON.stringify(defaultForm)))
  }
})

// 保存配置
const handleSave = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    saving.value = true

    // 深拷贝表单数据
    const rpcData = JSON.parse(JSON.stringify(form))
    
    emit('save', rpcData)
    
    ElMessage.success(props.isEdit ? 'RPC配置更新成功' : 'RPC配置添加成功')
    handleClose()
  } catch (error) {
    console.error('保存RPC配置失败:', error)
    ElMessage.error('保存RPC配置失败')
  } finally {
    saving.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.section {
  margin-bottom: 24px;
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #409eff;
  }
}

.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.help-content {
  h4 {
    color: #303133;
    margin: 16px 0 8px;
    font-size: 14px;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin: 4px 0;
      color: #606266;
      
      code {
        background: #f1f1f1;
        padding: 2px 4px;
        border-radius: 2px;
        font-family: 'Courier New', monospace;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input), :deep(.el-select) {
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}
</style> 