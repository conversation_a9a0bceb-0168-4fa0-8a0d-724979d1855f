<template>
  <div class="odbc-config">
    <el-tabs v-model="activeTab" class="odbc-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- 数据库连接配置标签页 -->
      <el-tab-pane label="数据库连接" name="connection">
        <OdbcConnectionConfig 
          v-model="config.connection" 
        />
      </el-tab-pane>

      <!-- PYODBC配置标签页 -->
      <el-tab-pane label="PYODBC配置" name="pyodbc">
        <OdbcPyodbcConfig 
          v-model="config.pyodbc" 
        />
      </el-tab-pane>

      <!-- 轮询配置标签页 -->
      <el-tab-pane label="轮询配置" name="polling">
        <OdbcPollingConfig 
          v-model="config.polling" 
        />
      </el-tab-pane>

      <!-- 数据映射配置标签页 -->
      <el-tab-pane label="数据映射" name="mapping">
        <OdbcMappingConfig 
          v-model="config.mapping" 
        />
      </el-tab-pane>

      <!-- RPC配置标签页 -->
      <el-tab-pane label="RPC配置" name="rpc">
        <OdbcRpcConfig 
          v-model="config.serverSideRpc" 
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import OdbcConnectionConfig from './components/OdbcConnectionConfig.vue'
import OdbcPyodbcConfig from './components/OdbcPyodbcConfig.vue'
import OdbcPollingConfig from './components/OdbcPollingConfig.vue'
import OdbcMappingConfig from './components/OdbcMappingConfig.vue'
import OdbcRpcConfig from './components/OdbcRpcConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  connection: {
    driver: '',
    host: 'localhost',
    port: 1433,
    database: '',
    username: '',
    password: ''
  },
  pyodbc: {
    pooling: false
  },
  polling: {
    query: 'SELECT bool_v, str_v, dbl_v, long_v, entity_id, ts FROM ts_kv WHERE ts > ? ORDER BY ts ASC LIMIT 10',
    period: 10,
    iterator: {
      column: 'ts',
      query: 'SELECT MIN(ts) - 1 FROM ts_kv',
      persistent: false
    }
  },
  mapping: {
    device: {
      type: 'postgres',
      name: "'ODBC ' + entity_id"
    },
    sendDataOnlyOnChange: false,
    attributes: '*',
    timeseries: []
  },
  serverSideRpc: {
    enableUnknownRpc: false,
    overrideRpcConfig: true,
    methods: []
  },
  devices: []
}

// ODBC 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'odbc', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
        
      console.log('ODBC 连接器配置初始化成功')
    } else {
      ElMessage.warning('ODBC 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('ODBC 连接器初始化失败:', error)
    ElMessage.error('ODBC 连接器初始化失败')
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  odbc: config
})
</script>

<style lang="scss" scoped>
.odbc-config {
  .odbc-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
}
</style>