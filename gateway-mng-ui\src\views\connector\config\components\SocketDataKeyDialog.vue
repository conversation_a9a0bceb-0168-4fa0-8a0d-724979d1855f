<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="keyForm" :rules="rules" ref="formRef" label-width="140px">
      <!-- 属性和遥测数据键 -->
      <template v-if="dataType === 'attributes' || dataType === 'telemetry'">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="键名称" prop="key" required>
              <el-input 
                v-model="keyForm.key" 
                placeholder="temperature"
              />
              <div class="field-hint">数据键的名称标识</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="起始字节位置" prop="byteFrom" required>
              <el-input-number 
                v-model.number="keyForm.byteFrom" 
                :min="0"
                :step="1"
                controls-position="right"
                placeholder="0"
                style="width: 100%"
              />
              <div class="field-hint">从接收到的第几个字节开始读取</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束字节位置" prop="byteTo" required>
              <el-input-number 
                v-model.number="keyForm.byteTo" 
                :min="-1"
                :step="1"
                controls-position="right"
                placeholder="-1"
                style="width: 100%"
              />
              <div class="field-hint">读取到第几个字节结束，-1表示到末尾</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 属性请求数据键 -->
      <template v-if="dataType === 'attributeRequests'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求类型" prop="type" required>
              <el-select 
                v-model="keyForm.type" 
                placeholder="请选择请求类型"
              >
                <el-option label="共享属性" value="shared" />
                <el-option label="客户端属性" value="client" />
                <el-option label="服务端属性" value="server" />
              </el-select>
              <div class="field-hint">属性请求的类型</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求表达式源" prop="requestExpressionSource">
              <el-select 
                v-model="keyForm.requestExpressionSource" 
                placeholder="请选择表达式源"
              >
                <el-option label="常量" value="constant" />
                <el-option label="消息" value="message" />
              </el-select>
              <div class="field-hint">请求表达式的数据源</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请求表达式" prop="requestExpression" required>
              <el-input 
                v-model="keyForm.requestExpression" 
                placeholder="${[0:3]==atr}"
                type="textarea"
                :rows="2"
              />
              <div class="field-hint">用于匹配请求的表达式</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="属性名表达式源" prop="attributeNameExpressionSource">
              <el-select 
                v-model="keyForm.attributeNameExpressionSource" 
                placeholder="请选择表达式源"
              >
                <el-option label="常量" value="constant" />
                <el-option label="消息" value="message" />
              </el-select>
              <div class="field-hint">属性名表达式的数据源</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="属性名表达式" prop="attributeNameExpression" required>
              <el-input 
                v-model="keyForm.attributeNameExpression" 
                placeholder="[3:]"
              />
              <div class="field-hint">提取属性名称的表达式</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- 属性更新数据键 -->
      <template v-if="dataType === 'attributeUpdates'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="编码方式" prop="encoding" required>
              <el-select 
                v-model="keyForm.encoding" 
                placeholder="请选择编码方式"
              >
                <el-option label="UTF-8" value="utf-8" />
                <el-option label="UTF-16" value="utf-16" />
                <el-option label="ASCII" value="ascii" />
                <el-option label="Unicode" value="unicode" />
                <el-option label="GBK" value="gbk" />
                <el-option label="GB2312" value="gb2312" />
                <el-option label="ANSI" value="ansi" />
              </el-select>
              <div class="field-hint">数据传输使用的字符编码</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="IoTCloud属性" prop="attributeOnThingsBoard" required>
              <el-input 
                v-model="keyForm.attributeOnThingsBoard" 
                placeholder="sharedName"
              />
              <div class="field-hint">IoTCloud平台上的属性名称</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <!-- RPC方法数据键 -->
      <template v-if="dataType === 'serverSideRpc'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="RPC方法名称" prop="methodRPC" required>
              <el-input 
                v-model="keyForm.methodRPC" 
                placeholder="rpcMethod1"
              />
              <div class="field-hint">RPC方法的名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="需要响应">
              <el-switch 
                v-model="keyForm.withResponse"
              />
              <div class="field-hint">是否需要返回响应数据</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="操作类型" prop="methodProcessing">
              <el-select 
                v-model="keyForm.methodProcessing" 
                placeholder="请选择操作类型"
              >
                <el-option label="读取" value="read" />
                <el-option label="写入" value="write" />
              </el-select>
              <div class="field-hint">RPC方法的操作类型</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编码方式" prop="encoding" required>
              <el-select 
                v-model="keyForm.encoding" 
                placeholder="请选择编码方式"
              >
                <el-option label="UTF-8" value="utf-8" />
                <el-option label="UTF-16" value="utf-16" />
                <el-option label="ASCII" value="ascii" />
                <el-option label="Unicode" value="unicode" />
                <el-option label="GBK" value="gbk" />
                <el-option label="GB2312" value="gb2312" />
                <el-option label="ANSI" value="ansi" />
              </el-select>
              <div class="field-hint">数据传输使用的字符编码</div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dataKey: {
    type: Object,
    default: null
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'telemetry', 'attributeRequests', 'attributeUpdates', 'serverSideRpc'].includes(value)
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 数据键表单
const keyForm = reactive({
  key: '',
  byteFrom: 0,
  byteTo: -1,
  type: 'shared',
  requestExpressionSource: 'constant',
  attributeNameExpressionSource: 'constant',
  requestExpression: '',
  attributeNameExpression: '',
  encoding: 'utf-8',
  attributeOnThingsBoard: '',
  methodRPC: '',
  withResponse: true,
  methodProcessing: 'read'
})

// 表单验证规则
const rules = reactive({
  key: [
    { required: true, message: '请输入键名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  byteFrom: [
    { required: true, message: '请输入起始字节位置', trigger: 'blur' },
    { type: 'number', min: 0, message: '起始字节位置不能小于0', trigger: 'blur' }
  ],
  byteTo: [
    { required: true, message: '请输入结束字节位置', trigger: 'blur' },
    { type: 'number', min: -1, message: '结束字节位置不能小于-1', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择请求类型', trigger: 'change' }
  ],
  requestExpression: [
    { required: true, message: '请输入请求表达式', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  attributeNameExpression: [
    { required: true, message: '请输入属性名表达式', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  encoding: [
    { required: true, message: '请选择编码方式', trigger: 'change' }
  ],
  attributeOnThingsBoard: [
    { required: true, message: '请输入IoTCloud属性名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  methodRPC: [
    { required: true, message: '请输入RPC方法名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ]
})

// 监听数据键变化
watch(() => props.dataKey, (newValue) => {
  if (newValue) {
    Object.assign(keyForm, {
      // 重置为默认值
      key: '',
      byteFrom: 0,
      byteTo: -1,
      type: 'shared',
      requestExpressionSource: 'constant',
      requestExpression: '',
      attributeNameExpressionSource: 'constant',
      attributeNameExpression: '',
      encoding: 'utf-8',
      attributeOnThingsBoard: '',
      methodRPC: '',
      withResponse: true,
      methodProcessing: 'read',
      // 然后合并传入的值
      ...newValue
    })
  }
}, { deep: true, immediate: true })

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.dataKey) {
    // 根据数据类型初始化表单
    resetForm()
    if (props.dataKey) {
      Object.assign(keyForm, props.dataKey)
    }
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
  if (!newValue) {
    // 对话框关闭时重置表单
    resetForm()
  }
})

// 重置表单
const resetForm = () => {
  if (props.dataType === 'attributes' || props.dataType === 'telemetry') {
    Object.assign(keyForm, {
      key: '',
      byteFrom: 0,
      byteTo: -1
    })
  } else if (props.dataType === 'attributeRequests') {
    Object.assign(keyForm, {
      type: 'shared',
      requestExpressionSource: 'constant',
      requestExpression: '',
      attributeNameExpressionSource: 'constant',
      attributeNameExpression: ''
    })
  } else if (props.dataType === 'attributeUpdates') {
    Object.assign(keyForm, {
      encoding: 'utf-8',
      attributeOnThingsBoard: ''
    })
  } else if (props.dataType === 'serverSideRpc') {
    Object.assign(keyForm, {
      methodRPC: '',
      withResponse: true,
      encoding: 'utf-8',
      methodProcessing: 'read'
    })
  }
}

// 获取对话框标题
const getDialogTitle = () => {
  const typeNames = {
    attributes: '属性',
    telemetry: '遥测',
    attributeRequests: '属性请求',
    attributeUpdates: '属性更新',
    serverSideRpc: 'RPC方法'
  }
  const typeName = typeNames[props.dataType] || '数据键'
  return `${props.isEdit ? '编辑' : '添加'}${typeName}`
}

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...keyForm }
    
    // 根据数据类型清理不需要的字段
    if (props.dataType === 'attributes' || props.dataType === 'telemetry') {
      delete saveData.type
      delete saveData.requestExpressionSource
      delete saveData.attributeNameExpressionSource
      delete saveData.requestExpression
      delete saveData.attributeNameExpression
      delete saveData.encoding
      delete saveData.attributeOnThingsBoard
      delete saveData.methodRPC
      delete saveData.withResponse
      delete saveData.methodProcessing
    } else if (props.dataType === 'attributeRequests') {
      delete saveData.key
      delete saveData.byteFrom
      delete saveData.byteTo
      delete saveData.encoding
      delete saveData.attributeOnThingsBoard
      delete saveData.methodRPC
      delete saveData.withResponse
      delete saveData.methodProcessing
    } else if (props.dataType === 'attributeUpdates') {
      delete saveData.key
      delete saveData.byteFrom
      delete saveData.byteTo
      delete saveData.type
      delete saveData.requestExpressionSource
      delete saveData.attributeNameExpressionSource
      delete saveData.requestExpression
      delete saveData.attributeNameExpression
      delete saveData.methodRPC
      delete saveData.withResponse
      delete saveData.methodProcessing
    } else if (props.dataType === 'serverSideRpc') {
      delete saveData.key
      delete saveData.byteFrom
      delete saveData.byteTo
      delete saveData.type
      delete saveData.requestExpressionSource
      delete saveData.attributeNameExpressionSource
      delete saveData.requestExpression
      delete saveData.attributeNameExpression
      delete saveData.attributeOnThingsBoard
    }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '数据键更新成功' : '数据键添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
  
  .el-input-number {
    width: 100%;
  }
}
</style> 