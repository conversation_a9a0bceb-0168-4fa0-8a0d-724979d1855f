<template>
  <div class="can-config">
    <el-tabs v-model="activeTab" class="can-tabs">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- CAN接口配置标签页 -->
      <el-tab-pane label="CAN接口配置" name="interface">
        <CanInterfaceConfig 
          v-model="config.interface" 
          v-model:channel="config.channel"
          v-model:backend="config.backend"
          v-model:reconnectPeriod="config.reconnectPeriod"
        />
      </el-tab-pane>

      <!-- 设备配置标签页 -->
      <el-tab-pane label="设备配置" name="devices">
        <CanDevicesConfig 
          v-model="config.devices" 
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import CanInterfaceConfig from './components/CanInterfaceConfig.vue'
import CanDevicesConfig from './components/CanDevicesConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  interface: 'socketcan',
  channel: 'vcan0',
  backend: {
    fd: true
  },
  reconnectPeriod: 5,
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  devices: []
}

// 配置数据结构
const config = reactive({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config, 'can', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config)
        
      console.log('CAN 连接器配置初始化成功')
    } else {
      ElMessage.warning('CAN 连接器配置初始化部分失败，请检查配置')
    }
  } catch (error) {
    console.error('CAN 连接器初始化失败:', error)
    ElMessage.error('CAN 连接器初始化失败')
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  can: config
})
</script>

<style lang="scss" scoped>
.can-config {
  .can-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
}
</style>