#     Copyright 2025. ThingsBoard
#
#     Licensed under the Apache License, Version 2.0 (the "License");
#     you may not use this file except in compliance with the License.
#     You may obtain a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#     Unless required by applicable law or agreed to in writing, software
#     distributed under the License is distributed on an "AS IS" BASIS,
#     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#     See the License for the specific language governing permissions and
#     limitations under the License.

DEVICE_OBJECT_TYPE_CODE = '8'
OBJECT_TYPE_PARAMETER = 'object-type'
OBJECT_INSTANCE_PARAMETER = 'object-instance'
DEVICE_OBJ_INSTANCE_PARAMETER = 'device obj.-instance'
OBJECT_NAME_PARAMETER = 'object-name'
DEFAULT_POLL_PERIOD = 10000
EDE_FILE_PATH_PARAMETER = 'edeFilePath'
EDE_CONFIG_PARAMETER = 'edeConfig'
SUPPORTED_OBJECTS_TYPES = {
    '0': 'analogInput',
    '1': 'analogOutput',
    '2': 'analogValue',
    '3': 'binaryInput',
    '4': 'binaryOutput',
    '5': 'binaryValue',
}
