<template>
  <el-card class="rest-mapping-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="card-title">
          <span>端点映射 {{ index + 1 }}</span>
          <el-tag :type="getSecurityTagType(mappingData.security.type)" size="small">
            {{ getSecurityLabel(mappingData.security.type) }}
          </el-tag>
          <el-tag v-for="method in mappingData.HTTPMethods" :key="method" :type="getMethodTagType(method)" size="small">
            {{ method }}
          </el-tag>
        </div>
        <el-button type="danger" size="small" @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>
    
    <el-form :model="mappingData" label-width="120px">
      <!-- 基础配置 -->
      <div class="section">
        <div class="section-title">端点配置</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="端点路径" required>
              <el-input 
                v-model="mappingData.endpoint" 
                placeholder="/my_devices"
                @input="updateValue"
              />
              <div class="field-hint">REST API端点路径，如 /my_devices</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="HTTP方法" required>
              <el-select 
                v-model="mappingData.HTTPMethods" 
                multiple 
                placeholder="选择HTTP方法"
                @change="updateValue"
              >
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
              </el-select>
              <div class="field-hint">允许的HTTP请求方法</div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 安全配置 -->
      <div class="section">
        <div class="section-title">安全配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认证类型">
              <el-select v-model="mappingData.security.type" @change="handleSecurityTypeChange">
                <el-option label="匿名访问" value="anonymous" />
                <el-option label="基本认证" value="basic" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="mappingData.security.type === 'basic'">
            <el-form-item label="用户名">
              <el-input 
                v-model="mappingData.security.username" 
                placeholder="username"
                @input="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="mappingData.security.type === 'basic'">
            <el-form-item label="密码">
              <el-input 
                v-model="mappingData.security.password" 
                type="password"
                placeholder="password"
                show-password
                @input="updateValue"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 转换器配置 -->
      <div class="section">
        <div class="section-title">数据转换器</div>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="转换器类型">
              <el-select v-model="mappingData.converter.type" @change="handleConverterTypeChange">
                <el-option label="JSON转换器" value="json" />
                <el-option label="自定义转换器" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- JSON转换器配置 -->
        <div v-if="mappingData.converter.type === 'json'" class="converter-config">
          <!-- 设备信息配置 -->
          <div class="subsection">
            <div class="subsection-title">设备信息</div>
            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="设备名来源">
                  <el-select v-model="mappingData.converter.deviceInfo.deviceNameExpressionSource" @change="updateValue">
                    <el-option label="请求数据" value="request" />
                    <el-option label="常量" value="constant" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="设备名表达式">
                  <el-input 
                    v-model="mappingData.converter.deviceInfo.deviceNameExpression" 
                    :placeholder="mappingData.converter.deviceInfo.deviceNameExpressionSource === 'request' ? '${sensorName}' : 'Device 1'"
                    @input="updateValue"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="设备类型表达式">
                  <el-input 
                    v-model="mappingData.converter.deviceInfo.deviceProfileExpression" 
                    placeholder="default"
                    @input="updateValue"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 属性配置 -->
          <div class="subsection">
            <div class="subsection-header">
              <div class="subsection-title">属性配置</div>
              <el-button type="primary" size="small" @click="addAttribute">
                <el-icon><Plus /></el-icon>
                添加属性
              </el-button>
            </div>
            
            <div v-if="mappingData.converter.attributes.length === 0" class="empty-config">
              <el-empty description="暂无属性配置" :image-size="60">
                <el-button type="primary" size="small" @click="addAttribute">添加第一个属性</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-keys-list">
              <div 
                v-for="(attr, attrIndex) in mappingData.converter.attributes" 
                :key="attrIndex" 
                class="data-key-item"
              >
                <RestDataKey
                  v-model="mappingData.converter.attributes[attrIndex]"
                  :index="attrIndex"
                  @delete="deleteAttribute(attrIndex)"
                />
              </div>
            </div>
          </div>

          <!-- 遥测配置 -->
          <div class="subsection">
            <div class="subsection-header">
              <div class="subsection-title">遥测配置</div>
              <el-button type="primary" size="small" @click="addTimeseries">
                <el-icon><Plus /></el-icon>
                添加遥测
              </el-button>
            </div>
            
            <div v-if="mappingData.converter.timeseries.length === 0" class="empty-config">
              <el-empty description="暂无遥测配置" :image-size="60">
                <el-button type="primary" size="small" @click="addTimeseries">添加第一个遥测</el-button>
              </el-empty>
            </div>
            
            <div v-else class="data-keys-list">
              <div 
                v-for="(ts, tsIndex) in mappingData.converter.timeseries" 
                :key="tsIndex" 
                class="data-key-item"
              >
                <RestDataKey
                  v-model="mappingData.converter.timeseries[tsIndex]"
                  :index="tsIndex"
                  @delete="deleteTimeseries(tsIndex)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 自定义转换器配置 -->
        <div v-if="mappingData.converter.type === 'custom'" class="converter-config">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="扩展名称">
                <el-input 
                  v-model="mappingData.converter.extension" 
                  placeholder="CustomRestUplinkConverter"
                  @input="updateValue"
                />
                <div class="field-hint">自定义转换器的类名</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <RestExtensionConfig 
            v-model="mappingData.converter.extensionConfig" 
            @update:modelValue="updateValue"
          />
        </div>
      </div>

      <!-- 配置预览 -->
      <div class="section" v-if="mappingData.endpoint">
        <div class="section-title">配置预览</div>
        <div class="preview-content">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="端点路径">
              <code class="endpoint-code">{{ mappingData.endpoint }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="HTTP方法">
              <el-tag 
                v-for="method in mappingData.HTTPMethods" 
                :key="method" 
                :type="getMethodTagType(method)" 
                size="small"
                style="margin-right: 4px;"
              >
                {{ method }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="安全类型">
              <el-tag :type="getSecurityTagType(mappingData.security.type)" size="small">
                {{ getSecurityLabel(mappingData.security.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="转换器类型">
              <el-tag :type="mappingData.converter.type === 'json' ? 'success' : 'warning'" size="small">
                {{ mappingData.converter.type === 'json' ? 'JSON转换器' : '自定义转换器' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="属性数量">
              {{ mappingData.converter.attributes?.length || 0 }}个
            </el-descriptions-item>
            <el-descriptions-item label="遥测数量">
              {{ mappingData.converter.timeseries?.length || 0 }}个
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import RestDataKey from './RestDataKey.vue'
import RestExtensionConfig from './RestExtensionConfig.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

// 映射数据
const mappingData = computed({
  get: () => ({
    endpoint: '',
    HTTPMethods: ['POST'],
    security: {
      type: 'anonymous'
    },
    converter: {
      type: 'json',
      deviceInfo: {
        deviceNameExpressionSource: 'request',
        deviceNameExpression: '',
        deviceProfileExpressionSource: 'constant',
        deviceProfileExpression: 'default'
      },
      attributes: [],
      timeseries: []
    },
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 获取HTTP方法标签类型
const getMethodTagType = (method) => {
  const methodTagMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return methodTagMap[method] || 'info'
}

// 获取安全类型标签
const getSecurityTagType = (type) => {
  return type === 'anonymous' ? 'info' : 'success'
}

const getSecurityLabel = (type) => {
  return type === 'anonymous' ? '匿名' : '认证'
}

// 安全类型变化处理
const handleSecurityTypeChange = () => {
  const newData = { ...mappingData.value }
  if (newData.security.type === 'anonymous') {
    delete newData.security.username
    delete newData.security.password
  } else {
    newData.security.username = ''
    newData.security.password = ''
  }
  emit('update:modelValue', newData)
}

// 转换器类型变化处理
const handleConverterTypeChange = () => {
  const newData = { ...mappingData.value }
  if (newData.converter.type === 'json') {
    newData.converter = {
      type: 'json',
      deviceInfo: {
        deviceNameExpressionSource: 'request',
        deviceNameExpression: '',
        deviceProfileExpressionSource: 'constant',
        deviceProfileExpression: 'default'
      },
      attributes: [],
      timeseries: []
    }
  } else {
    newData.converter = {
      type: 'custom',
      deviceInfo: {
        deviceNameExpressionSource: 'constant',
        deviceNameExpression: 'CustomDevice',
        deviceProfileExpressionSource: 'constant',
        deviceProfileExpression: 'default'
      },
      extension: 'CustomRestUplinkConverter',
      extensionConfig: {
        key: 'value',
        datatype: 'float',
        fromByte: 0,
        toByte: 4,
        byteorder: 'big',
        signed: true,
        multiplier: 1
      }
    }
  }
  emit('update:modelValue', newData)
}

// 添加属性
const addAttribute = () => {
  const newData = { ...mappingData.value }
  if (!newData.converter.attributes) newData.converter.attributes = []
  newData.converter.attributes.push({
    type: 'string',
    key: 'attribute',
    value: '${value}'
  })
  emit('update:modelValue', newData)
}

// 删除属性
const deleteAttribute = (index) => {
  const newData = { ...mappingData.value }
  newData.converter.attributes.splice(index, 1)
  emit('update:modelValue', newData)
}

// 添加遥测
const addTimeseries = () => {
  const newData = { ...mappingData.value }
  if (!newData.converter.timeseries) newData.converter.timeseries = []
  newData.converter.timeseries.push({
    type: 'double',
    key: 'temperature',
    value: '${temp}'
  })
  emit('update:modelValue', newData)
}

// 删除遥测
const deleteTimeseries = (index) => {
  const newData = { ...mappingData.value }
  newData.converter.timeseries.splice(index, 1)
  emit('update:modelValue', newData)
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...mappingData.value })
}

// 删除配置
const handleDelete = () => {
  emit('delete')
}
</script>

<style lang="scss" scoped>
.rest-mapping-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  .subsection {
    margin: 20px 0;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    
    .subsection-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .subsection-title {
      font-size: 13px;
      font-weight: 600;
      color: #606266;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .empty-config {
    text-align: center;
    padding: 20px 0;
  }

  .data-keys-list {
    .data-key-item {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .preview-content {
    .endpoint-code {
      background: #f5f7fa;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
      color: #e6a23c;
      font-size: 12px;
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style> 