<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑RPC配置' : '添加RPC配置'"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="rpcForm" :rules="rules" ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备名筛选器" prop="deviceNameFilter" required>
            <el-input 
              v-model="rpcForm.deviceNameFilter" 
              placeholder=".*"
            />
            <div class="field-hint">正则表达式，用于匹配设备名称</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="方法筛选" prop="methodFilter" required>
            <el-select v-model="rpcForm.methodFilter" placeholder="请选择方法类型">
              <el-option label="读取操作" value="read" />
              <el-option label="写入操作" value="write" />
            </el-select>
            <div class="field-hint">RPC方法的类型筛选</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="值表达式" prop="valueExpression" required>
            <el-input 
              v-model="rpcForm.valueExpression" 
              placeholder="${params}"
            />
            <div class="field-hint">RPC参数的处理表达式</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 表达式语法帮助 -->
      <div class="expression-help">
        <el-alert
          title="表达式语法说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>变量替换:</strong></p>
            <ul>
              <li><code>${params}</code> - RPC调用的参数</li>
              <li><code>${method}</code> - RPC方法名称</li>
              <li><code>${deviceName}</code> - 设备名称</li>
            </ul>
            <p><strong>方法类型:</strong></p>
            <ul>
              <li><strong>read</strong> - 读取操作，通常用于获取数据</li>
              <li><strong>write</strong> - 写入操作，通常用于设置参数</li>
            </ul>
            <p><strong>示例:</strong> <code>${params}</code> 直接传递所有参数</p>
          </div>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  rpc: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// RPC表单数据
const rpcForm = reactive({
  deviceNameFilter: '.*',
  methodFilter: 'read',
  valueExpression: '${params}'
})

// 表单验证规则
const rules = reactive({
  deviceNameFilter: [
    { required: true, message: '请输入设备名筛选器', trigger: 'blur' }
  ],
  methodFilter: [
    { required: true, message: '请选择方法筛选', trigger: 'change' }
  ],
  valueExpression: [
    { required: true, message: '请输入值表达式', trigger: 'blur' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.rpc) {
    // 加载RPC数据
    Object.assign(rpcForm, {
      deviceNameFilter: '.*',
      methodFilter: 'read',
      valueExpression: '${params}',
      ...props.rpc
    })
  } else if (newValue) {
    // 重置为默认值
    Object.assign(rpcForm, {
      deviceNameFilter: '.*',
      methodFilter: 'read',
      valueExpression: '${params}'
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...rpcForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? 'RPC配置更新成功' : 'RPC配置添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.expression-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        code {
          background: #f1f2f3;
          padding: 2px 6px;
          border-radius: 3px;
          font-family: monospace;
          color: #e6a23c;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style> 