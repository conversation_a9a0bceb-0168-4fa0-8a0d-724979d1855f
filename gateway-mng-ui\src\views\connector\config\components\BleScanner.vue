<template>
  <div class="ble-scanner-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>扫描器配置</span>
          <el-tooltip content="BLE设备扫描器参数配置，用于发现和连接BLE设备" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="scannerConfig" :rules="rules" label-width="160px" ref="formRef">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="扫描超时时间" prop="timeout" required>
              <el-input-number 
                v-model="scannerConfig.timeout" 
                :min="1000" 
                :max="300000"
                :step="1000"
                controls-position="right"
                placeholder="10000"
                
              />
              <span class="unit-suffix">毫秒</span>
              <div class="field-hint">BLE设备扫描的超时时间 (1-300秒)</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指定设备名称" prop="deviceName">
              <el-input 
                v-model="scannerConfig.deviceName" 
                placeholder="Device name"
                clearable
                
              />
              <div class="field-hint">可选：扫描指定名称的设备，留空扫描所有设备</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick, defineEmits, defineProps } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      timeout: 10000,
      deviceName: ''
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()

// 防止循环更新的标志
const isInternalUpdate = ref(false)

// 确保数值字段为数字类型
const ensureNumberTypes = (config) => {
  const result = { ...config }
  if (result.timeout !== undefined) {
    result.timeout = typeof result.timeout === 'string' ? parseInt(result.timeout) || 10000 : result.timeout
  }
  return result
}

// 扫描器配置数据
const scannerConfig = reactive({
  timeout: 10000,
  deviceName: '',
  ...ensureNumberTypes(props.modelValue || {})
})

// 表单验证规则
const rules = reactive({
  timeout: [
    { required: true, message: '请输入扫描超时时间', trigger: 'blur' },
    { type: 'number', min: 1000, max: 300000, message: '超时时间范围为 1000-300000 毫秒', trigger: 'blur' }
  ]
})

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (!isInternalUpdate.value && newValue) {
    const normalizedValue = ensureNumberTypes(newValue)
    Object.assign(scannerConfig, normalizedValue)
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(scannerConfig, (newValue) => {
  isInternalUpdate.value = true
  emit('update:modelValue', { ...newValue })
  nextTick(() => {
    isInternalUpdate.value = false
  })
}, { deep: true })
</script>

<style lang="scss" scoped>
.ble-scanner-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .unit-suffix {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input-number) {
    width: 100%;
  }
}
</style> 