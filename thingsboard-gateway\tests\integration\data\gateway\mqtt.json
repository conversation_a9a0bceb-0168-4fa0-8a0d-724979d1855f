{"broker": {"name": "Default Local Broker", "host": "127.0.0.1", "port": 1884, "clientId": "ThingsBoard_gateway", "maxMessageNumberPerWorker": 10, "maxNumberOfWorkers": 100, "security": {"type": "anonymous", "username": "user", "password": "password"}}, "mapping": [{"topicFilter": "/sensor/data", "converter": {"type": "json", "deviceNameJsonExpression": "${serialNumber}", "deviceTypeJsonExpression": "default", "timeout": 60000, "attributes": [{"type": "string", "key": "model", "value": "${sensorModel}"}], "timeseries": [{"type": "double", "key": "temperature", "value": "${temp}"}, {"type": "double", "key": "humidity", "value": "${hum}"}]}}, {"topicFilter": "/sensor/+/data", "converter": {"type": "json", "deviceNameTopicExpression": "(?<=sensor/)(.*?)(?=/data)", "deviceTypeTopicExpression": "Thermometer", "timeout": 60000, "attributes": [{"type": "string", "key": "model", "value": "${sensorModel}"}], "timeseries": [{"type": "double", "key": "temperature", "value": "${temp}"}, {"type": "double", "key": "humidity", "value": "${hum}"}]}}, {"topicFilter": "/custom/sensors/+", "converter": {"type": "custom", "extension": "CustomMqttUplinkConverter", "extension-config": {"temperatureBytes": 2, "humidityBytes": 2, "batteryLevelBytes": 1}}}], "connectRequests": [{"topicFilter": "sensor/connect", "deviceNameJsonExpression": "${serialNumber}"}, {"topicFilter": "sensor/+/connect", "deviceNameTopicExpression": "(?<=sensor/)(.*?)(?=/connect)"}], "disconnectRequests": [{"topicFilter": "sensor/disconnect", "deviceNameJsonExpression": "${SerialNumber}"}, {"topicFilter": "sensor/+/disconnect", "deviceNameTopicExpression": "(?<=sensor/)(.*?)(?=/disconnect)"}], "attributeRequests": [{"retain": false, "topicFilter": "v1/devices/me/attributes/request", "deviceNameJsonExpression": "${serialNumber}", "attributeNameJsonExpression": "${temp}", "topicExpression": "devices/${deviceName}/attrs", "valueExpression": "${attributeKey}: ${attributeValue}"}], "attributeUpdates": [{"retain": true, "deviceNameFilter": ".*", "attributeFilter": "shared", "topicExpression": "sensor/${deviceName}/${attributeKey}", "valueExpression": "{\"${attributeKey}\":\"${attributeValue}\"}"}], "serverSideRpc": [{"deviceNameFilter": ".*", "methodFilter": "echo", "requestTopicExpression": "sensor/${deviceName}/request/${methodName}/${requestId}", "responseTopicExpression": "sensor/${deviceName}/response/${methodName}/${requestId}", "responseTimeout": 10000, "valueExpression": "${params}"}, {"deviceNameFilter": ".*", "methodFilter": "no-reply", "requestTopicExpression": "sensor/${deviceName}/request/${methodName}/${requestId}", "valueExpression": "${params}"}], "id": "d63ea5ca-ddaa-4361-b0b3-efa30152a9c4", "name": "MQTT Broker Connector"}