<template>
  <div class="mqtt-extension-config">
    <el-card>
      <template #header>
        <div class="card-header">
          <div class="header-info">
            <h4>扩展配置</h4>
            <p class="description">配置自定义转换器的扩展参数</p>
          </div>
          <el-button type="primary" size="small" @click="addConfigItem">
            <el-icon><Plus /></el-icon>
            添加配置项
          </el-button>
        </div>
      </template>

      <div v-if="configItems.length === 0" class="empty-state">
        <el-empty description="暂无扩展配置">
          <el-button type="primary" @click="addConfigItem">添加第一个配置项</el-button>
        </el-empty>
      </div>

      <div v-else class="config-items-list">
        <div 
          v-for="(item, index) in configItems" 
          :key="index"
          class="config-item"
        >
          <el-card shadow="hover">
            <template #header>
              <div class="config-item-header">
                <div class="config-item-info">
                  <span class="key-name">{{ item.key || '未设置键名' }}</span>
                  <el-tag :type="getTypeTagType(item.type)" size="small">
                    {{ getTypeLabel(item.type) }}
                  </el-tag>
                </div>
                <el-button 
                  type="danger" 
                  size="small" 
                  link
                  @click="removeConfigItem(index)"
                >
                  <el-icon><Delete /></el-icon>删除
                </el-button>
              </div>
            </template>
            
            <el-form :model="item" label-width="100px" size="small">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="键名" required>
                    <el-input 
                      v-model="item.key" 
                      placeholder="temperature"
                      @input="updateExtensionConfig"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="值类型" required>
                    <el-select v-model="item.type" @change="updateExtensionConfig" style="width: 100%">
                      <el-option label="字符串" value="string" />
                      <el-option label="数字" value="number" />
                      <el-option label="布尔值" value="boolean" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="值" required>
                    <el-input 
                      v-if="item.type === 'string'"
                      v-model="item.value" 
                      placeholder="配置值"
                      @input="updateExtensionConfig"
                    />
                    <el-input-number 
                      v-else-if="item.type === 'number'"
                      v-model="item.value" 
                      placeholder="配置值"
                      @change="updateExtensionConfig"
                      style="width: 100%"
                    />
                    <el-switch 
                      v-else-if="item.type === 'boolean'"
                      v-model="item.value"
                      @change="updateExtensionConfig"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </div>
      </div>

      <!-- JSON预览 -->
      <div class="json-preview" v-if="configItems.length > 0">
        <el-divider content-position="left">JSON预览</el-divider>
        <el-input 
          :model-value="JSON.stringify(extensionConfig, null, 2)" 
          type="textarea" 
          :rows="6"
          readonly
          placeholder='{"temperature": 2, "humidity": 2, "batteryLevel": 1}'
        />
        <div class="field-hint">扩展配置的JSON格式预览（只读）</div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const configItems = ref([])
const extensionConfig = reactive({})

// 数据类型标签颜色
const getTypeTagType = (type) => {
  const typeMap = {
    string: '',
    number: 'success',
    boolean: 'info'
  }
  return typeMap[type] || ''
}

// 数据类型标签
const getTypeLabel = (type) => {
  const typeMap = {
    string: '字符串',
    number: '数字',
    boolean: '布尔值'
  }
  return typeMap[type] || type
}

// 添加配置项
const addConfigItem = () => {
  const newItem = {
    key: '',
    type: 'string',
    value: ''
  }
  configItems.value.push(newItem)
  ElMessage.success('配置项添加成功')
}

// 移除配置项
const removeConfigItem = (index) => {
  configItems.value.splice(index, 1)
  updateExtensionConfig()
  ElMessage.success('配置项删除成功')
}

// 更新扩展配置
const updateExtensionConfig = () => {
  const newConfig = {}
  configItems.value.forEach(item => {
    if (item.key && item.key.trim()) {
      let value = item.value
      // 根据类型转换值
      if (item.type === 'number') {
        value = Number(value) || 0
      } else if (item.type === 'boolean') {
        value = Boolean(value)
      } else {
        value = String(value || '')
      }
      newConfig[item.key.trim()] = value
    }
  })
  
  // 更新extensionConfig
  Object.keys(extensionConfig).forEach(key => {
    delete extensionConfig[key]
  })
  Object.assign(extensionConfig, newConfig)
  
  emit('update:modelValue', { ...newConfig })
}

// 从配置对象生成配置项
const generateConfigItems = (config) => {
  return Object.entries(config || {}).map(([key, value]) => ({
    key,
    type: typeof value === 'number' ? 'number' : typeof value === 'boolean' ? 'boolean' : 'string',
    value
  }))
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && typeof newValue === 'object') {
    configItems.value = generateConfigItems(newValue)
    Object.keys(extensionConfig).forEach(key => {
      delete extensionConfig[key]
    })
    Object.assign(extensionConfig, newValue)
  }
}, { deep: true, immediate: true })

// 初始化默认配置
if (Object.keys(props.modelValue).length === 0) {
  configItems.value = [
    { key: 'temperature', type: 'number', value: 2 },
    { key: 'humidity', type: 'number', value: 2 },
    { key: 'batteryLevel', type: 'number', value: 1 }
  ]
  updateExtensionConfig()
}
</script>

<style lang="scss" scoped>
.mqtt-extension-config {
  .card-header {
    .header-info {
      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
      
      .description {
        margin: 0;
        font-size: 13px;
        color: #606266;
      }
    }
    
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
  }

  .config-items-list {
    .config-item {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .config-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .config-item-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .key-name {
            font-weight: 600;
            color: #303133;
          }
        }
      }
    }
  }

  .json-preview {
    margin-top: 20px;
    
    .field-hint {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
      line-height: 1.4;
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-switch) {
  --el-switch-on-color: #409eff;
}
</style> 