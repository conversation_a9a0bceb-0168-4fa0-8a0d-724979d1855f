{"broker": {"name": "Default Local Broker", "host": "127.0.0.1", "port": 1883, "clientId": "ThingsBoard_gateway", "version": 5, "maxMessageNumberPerWorker": 10, "maxNumberOfWorkers": 100, "sendDataOnlyOnChange": false, "security": {"type": "anonymous"}}, "mapping": [], "requestsMapping": {"serverSideRpc": {}, "connectRequests": {}, "disconnectRequests": {}, "attributeRequests": [{"retain": false, "qos": 0, "topicFilter": "v1/devices/me/attributes/request", "deviceInfo": {"deviceNameExpressionSource": "message", "deviceNameExpression": "${serialNumber}"}, "attributeNameExpressionSource": "message", "attributeNameExpression": "${versionAttribute}, ${pduAttribute}", "topicExpression": "devices/${deviceName}/attrs", "valueExpression": "${attributeKey}: ${attributeValue}"}], "attributeUpdates": {}}}