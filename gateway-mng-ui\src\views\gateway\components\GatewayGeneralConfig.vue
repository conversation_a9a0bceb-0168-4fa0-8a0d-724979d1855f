<template>
  <div class="gateway-general-config">
    <el-form :model="config" :rules="rules" ref="formRef" label-width="140px">
      <!-- 远程配置开关 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>远程管理</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="远程配置">
              <el-switch 
                v-model="config.remoteConfiguration"
                @change="handleChange"
              />
              <div class="field-hint">允许通过IoTCloud平台远程配置网关</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="远程Shell">
              <el-switch 
                v-model="config.remoteShell"
                @change="handleChange"
              />
              <div class="field-hint">允许通过IoTCloud平台远程执行Shell命令</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- IoTCloud连接配置 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>IoTCloud连接</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务器地址" prop="host" required>
              <el-input 
                v-model="config.host" 
                placeholder="localhost"
                @change="handleChange"
              />
              <div class="field-hint">IoTCloud服务器的IP地址或域名</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="MQTT端口" prop="port" required>
              <el-input-number 
                v-model="config.port" 
                :min="1" 
                :max="65535"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">IoTCloud MQTT服务端口，默认1883</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 安全认证配置 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>安全认证</span>
          </div>
        </template>
        <GatewaySecurityConfig 
          v-model="config.security"
          @change="handleChange"
        />
      </el-card>

      <!-- 报告策略配置 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>报告策略</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="策略类型" prop="reportStrategy.type" required>
              <el-select 
                v-model="config.reportStrategy.type" 
                placeholder="请选择策略类型"
                @change="handleChange"
              >
                <el-option label="收到数据时上报" value="ON_RECEIVED" />
                <el-option label="数据变化时上报" value="ON_CHANGE" />
                <el-option label="定期上报" value="ON_REPORT_PERIOD" />
                <el-option label="变化或定期上报" value="ON_CHANGE_OR_REPORT_PERIOD" />
              </el-select>
              <div class="field-hint">数据上报到IoTCloud的策略</div>
            </el-form-item>
          </el-col>
          <el-col 
            v-if="config.reportStrategy.type === 'ON_REPORT_PERIOD' || 
                  config.reportStrategy.type === 'ON_CHANGE_OR_REPORT_PERIOD'" 
            :span="12"
          >
            <el-form-item label="报告周期(毫秒)" prop="reportStrategy.reportPeriod">
              <el-input-number 
                v-model="config.reportStrategy.reportPeriod" 
                :min="1000"
                :precision="0"
                controls-position="right"
                style="width: 100%"
                @change="handleChange"
              />
              <div class="field-hint">定期上报的时间间隔</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'
import GatewaySecurityConfig from './GatewaySecurityConfig.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const config = ref({
  host: '',
  port: 1883,
  remoteShell: true,
  remoteConfiguration: true,
  security: {
    type: 'accessToken',
    accessToken: ''
  },
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  }
})

// 表单验证规则
const rules = {
  host: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入MQTT端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口范围 1-65535', trigger: 'blur' }
  ],
  'reportStrategy.type': [
    { required: true, message: '请选择报告策略类型', trigger: 'change' }
  ],
  'reportStrategy.reportPeriod': [
    { type: 'number', min: 1000, message: '报告周期不能小于1000毫秒', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(config.value, newValue)
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
  emit('change', { ...newConfig })
}, { deep: true })

// 处理配置变化
const handleChange = () => {
  // 触发验证
  formRef.value?.validateField(Object.keys(rules))
}

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.gateway-general-config {
  .config-card {
    margin-bottom: 24px;
    border: 1px solid #e4e7ed;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-header {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  :deep(.el-form-item) {
    margin-bottom: 20px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
  
  :deep(.el-card__body) {
    padding: 24px;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
}
</style> 