<template>
  <div class="modbus-slave-config">
    <el-form :model="slaveConfig" :rules="slaveRules" ref="slaveFormRef" label-width="140px">
      <!-- 基础连接配置 -->
      <div class="config-group">
        <div class="group-title">服务器连接配置</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="连接类型" prop="type">
              <el-select v-model="slaveConfig.type" @change="handleTypeChange">
                <el-option label="TCP" value="tcp" />
                <el-option label="UDP" value="udp" />
                <el-option label="串口" value="serial" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="slaveConfig.type !== 'serial'">
            <el-form-item label="监听地址" prop="host">
              <el-input v-model="slaveConfig.host" placeholder="0.0.0.0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="slaveConfig.type === 'serial' ? '串口号' : '监听端口'" prop="port">
              <el-input v-model="slaveConfig.port" :placeholder="slaveConfig.type === 'serial' ? '/dev/ttyUSB0' : '5020'" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="通信方法" prop="method">
              <el-input v-model="slaveConfig.method" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="slaveConfig.type === 'serial'">
            <el-form-item label="波特率" prop="baudrate">
              <el-select v-model="slaveConfig.baudrate">
                <el-option v-for="rate in baudrateOptions" :key="rate" :label="rate" :value="rate" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单元ID" prop="unitId">
              <el-input-number v-model="slaveConfig.unitId" :min="1" :max="255" controls-position="right" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 设备信息配置 -->
      <div class="config-group">
        <div class="group-title">设备信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input v-model="slaveConfig.deviceName" placeholder="网关从机设备" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-input v-model="slaveConfig.deviceType" placeholder="default" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="轮询周期(ms)" prop="pollPeriod">
              <el-input-number v-model="slaveConfig.pollPeriod" :min="100" controls-position="right" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发送数据到平台">
              <el-switch v-model="slaveConfig.sendDataToThingsBoard" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 高级设置 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="高级连接设置" name="advanced">
          <div class="config-group">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="字节顺序" prop="byteOrder">
                  <el-select v-model="slaveConfig.byteOrder">
                    <el-option label="LITTLE" value="LITTLE" />
                    <el-option label="BIG" value="BIG" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字顺序" prop="wordOrder">
                  <el-select v-model="slaveConfig.wordOrder">
                    <el-option label="LITTLE" value="LITTLE" />
                    <el-option label="BIG" value="BIG" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>

        <el-collapse-item title="厂商信息" name="identity">
          <div class="config-group">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="厂商名称">
                  <el-input v-model="slaveConfig.identity.vendorName" placeholder="厂商名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品代码">
                  <el-input v-model="slaveConfig.identity.productCode" placeholder="产品代码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="厂商网址">
                  <el-input v-model="slaveConfig.identity.vendorUrl" placeholder="https://example.com" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品名称">
                  <el-input v-model="slaveConfig.identity.productName" placeholder="产品名称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="型号名称">
                  <el-input v-model="slaveConfig.identity.modelName" placeholder="型号名称" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>

        <el-collapse-item title="TLS安全配置" name="security">
          <div class="config-group">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="证书文件路径">
                  <el-input v-model="slaveConfig.security.certfile" placeholder="/path/to/cert.pem" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="私钥文件路径">
                  <el-input v-model="slaveConfig.security.keyfile" placeholder="/path/to/key.pem" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="密码">
                  <el-input v-model="slaveConfig.security.password" type="password" show-password placeholder="密码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="服务器主机名">
                  <el-input v-model="slaveConfig.security.server_hostname" placeholder="服务器主机名" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-collapse-item>
      </el-collapse>

      <!-- 寄存器值配置 -->
      <div class="config-group">
        <div class="group-title">寄存器值配置</div>
        <ModbusRegisterValues v-model="slaveConfig.values" />
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import ModbusRegisterValues from './ModbusRegisterValues.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const slaveFormRef = ref(null)
const activeCollapse = ref(['advanced'])

const slaveConfig = reactive({
  enabled: false,
  type: 'tcp',
  host: '0.0.0.0',
  port: 5020,
  method: 'socket',
  unitId: 1,
  deviceName: 'Gateway Modbus Slave',
  deviceType: 'default',
  pollPeriod: 5000,
  sendDataToThingsBoard: true,
  byteOrder: 'LITTLE',
  wordOrder: 'LITTLE',
  identity: {
    vendorName: '',
    productCode: '',
    vendorUrl: '',
    productName: '',
    modelName: ''
  },
  values: {
    holding_registers: {
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      rpc: []
    },
    coils_initializer: {
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      rpc: []
    },
    input_registers: {
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      rpc: []
    },
    discrete_inputs: {
      attributes: [],
      timeseries: [],
      attributeUpdates: [],
      rpc: []
    }
  },
  security: {
    certfile: '',
    keyfile: '',
    password: '',
    server_hostname: ''
  },
  ...props.modelValue
})

// 波特率选项
const baudrateOptions = [4800, 9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600]

// 表单验证规则
const slaveRules = reactive({
  deviceName: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  deviceType: [
    { required: true, message: '请输入设备类型', trigger: 'blur' }
  ],
  pollPeriod: [
    { required: true, message: '请输入轮询周期', trigger: 'blur' },
    { type: 'number', min: 100, message: '轮询周期不能小于100ms', trigger: 'blur' }
  ],
  unitId: [
    { required: true, message: '请输入单元ID', trigger: 'blur' },
    { type: 'number', min: 1, max: 255, message: '单元ID范围为1-255', trigger: 'blur' }
  ]
})

// 方法
const handleTypeChange = (type) => {
  if (type === 'serial') {
    slaveConfig.method = 'rtu'
    slaveConfig.port = '/dev/ttyUSB0'
    slaveConfig.baudrate = 9600
  } else {
    slaveConfig.method = 'socket'
    slaveConfig.port = 5020
    delete slaveConfig.baudrate
  }
}

// 监听配置变化
watch(slaveConfig, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(slaveConfig, newValue)
}, { deep: true })

// 暴露验证方法
defineExpose({
  validate: () => slaveFormRef.value?.validate()
})
</script>

<style lang="scss" scoped>
.modbus-slave-config {
  .config-group {
    margin-bottom: 24px;
    
    .group-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  :deep(.el-collapse) {
    border: none;
    
    .el-collapse-item__header {
      background: #f8f9fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 0 16px;
      font-weight: 500;
    }
    
    .el-collapse-item__content {
      padding: 16px 0;
      border: none;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input), :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-switch) {
    --el-switch-on-color: #409eff;
  }
}
</style> 