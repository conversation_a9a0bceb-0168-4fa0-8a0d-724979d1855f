<template>
  <div class="gateway-logs-config">
    <el-form :model="config" :rules="rules" ref="formRef" label-width="140px">
      <!-- 全局日志配置 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>全局日志配置</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="日志级别" prop="logLevel" required>
              <el-select 
                v-model="config.logLevel" 
                placeholder="请选择日志级别"
                @change="handleChange"
              >
                <el-option label="DEBUG" value="DEBUG" />
                <el-option label="INFO" value="INFO" />
                <el-option label="WARNING" value="WARNING" />
                <el-option label="ERROR" value="ERROR" />
                <el-option label="CRITICAL" value="CRITICAL" />
              </el-select>
              <div class="field-hint">全局日志输出级别</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日期格式" prop="dateFormat" required>
              <el-input 
                v-model="config.dateFormat" 
                placeholder="%Y-%m-%d %H:%M:%S"
                @change="handleChange"
              />
              <div class="field-hint">日志中时间戳的格式</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日志格式" prop="logFormat" required>
              <el-input 
                v-model="config.logFormat" 
                placeholder="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                @change="handleChange"
              />
              <div class="field-hint">日志输出的格式模板</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 模块日志配置 -->
      <el-card class="config-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>模块日志配置</span>
          </div>
        </template>
        <div class="modules-config">
          <div 
            v-for="(moduleConfig, moduleName) in config.local" 
            :key="moduleName" 
            class="module-item"
          >
            <el-card shadow="hover">
              <template #header>
                <div class="module-header">
                  <span class="module-name">{{ getModuleName(moduleName) }}</span>
                  <el-tag :type="getLogLevelType(moduleConfig.logLevel)">
                    {{ moduleConfig.logLevel }}
                  </el-tag>
                </div>
              </template>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="`日志级别`" :prop="`local.${moduleName}.logLevel`">
                    <el-select 
                      v-model="moduleConfig.logLevel" 
                      placeholder="请选择日志级别"
                      @change="handleChange"
                      style="width: 100%"
                    >
                      <el-option label="DEBUG" value="DEBUG" />
                      <el-option label="INFO" value="INFO" />
                      <el-option label="WARNING" value="WARNING" />
                      <el-option label="ERROR" value="ERROR" />
                      <el-option label="CRITICAL" value="CRITICAL" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="`备份文件数量`" :prop="`local.${moduleName}.backupCount`">
                    <el-input-number 
                      v-model="moduleConfig.backupCount" 
                      :min="1"
                      :max="100"
                      :precision="0"
                      controls-position="right"
                      style="width: 100%"
                      @change="handleChange"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item :label="`日志文件路径`" :prop="`local.${moduleName}.filePath`">
                    <el-input 
                      v-model="moduleConfig.filePath" 
                      :placeholder="`./logs/${moduleName}.log`"
                      @change="handleChange"
                    />
                    <div class="field-hint">日志文件的完整路径，支持相对路径和绝对路径</div>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item :label="`轮转时间`" :prop="`local.${moduleName}.savingTime`">
                    <el-input-number 
                      v-model="moduleConfig.savingTime" 
                      :min="1"
                      :precision="0"
                      controls-position="right"
                      style="width: 100%"
                      @change="handleChange"
                    />
                    <div class="field-hint">日志文件轮转的时间间隔</div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item :label="`轮转周期`" :prop="`local.${moduleName}.savingPeriod`">
                    <el-select 
                      v-model="moduleConfig.savingPeriod" 
                      placeholder="请选择轮转周期"
                      @change="handleChange"
                      style="width: 100%"
                    >
                      <el-option label="小时 (H)" value="H" />
                      <el-option label="天 (D)" value="D" />
                      <el-option label="周 (W)" value="W" />
                      <el-option label="月 (M)" value="M" />
                    </el-select>
                    <div class="field-hint">日志文件轮转的周期单位</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </div>
        </div>
      </el-card>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const formRef = ref()
const config = ref({
  dateFormat: '%Y-%m-%d %H:%M:%S',
  logFormat: '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
  logLevel: 'INFO',
  local: {
    service: {
      logLevel: 'INFO',
      filePath: './logs/service.log',
      backupCount: 7,
      savingTime: 1,
      savingPeriod: 'D'
    },
    connector: {
      logLevel: 'INFO',
      filePath: './logs/connector.log',
      backupCount: 7,
      savingTime: 1,
      savingPeriod: 'D'
    },
    converter: {
      logLevel: 'INFO',
      filePath: './logs/converter.log',
      backupCount: 7,
      savingTime: 1,
      savingPeriod: 'D'
    },
    tb_connection: {
      logLevel: 'INFO',
      filePath: './logs/tb_connection.log',
      backupCount: 7,
      savingTime: 1,
      savingPeriod: 'D'
    },
    storage: {
      logLevel: 'INFO',
      filePath: './logs/storage.log',
      backupCount: 7,
      savingTime: 1,
      savingPeriod: 'D'
    },
    extension: {
      logLevel: 'INFO',
      filePath: './logs/extension.log',
      backupCount: 7,
      savingTime: 1,
      savingPeriod: 'D'
    }
  }
})

// 表单验证规则
const rules = {
  logLevel: [
    { required: true, message: '请选择日志级别', trigger: 'change' }
  ],
  dateFormat: [
    { required: true, message: '请输入日期格式', trigger: 'blur' }
  ],
  logFormat: [
    { required: true, message: '请输入日志格式', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(config.value, newValue)
  }
}, { deep: true, immediate: true })

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', { ...newConfig })
  emit('change', { ...newConfig })
}, { deep: true })

// 处理配置变化
const handleChange = () => {
  // 触发验证
  formRef.value?.validateField(['logLevel', 'dateFormat', 'logFormat'])
}

// 获取模块名称
const getModuleName = (moduleName) => {
  const moduleNames = {
    service: '服务模块',
    connector: '连接器模块',
    converter: '转换器模块',
    tb_connection: 'IoTCloud连接',
    storage: '存储模块',
    extension: '扩展模块'
  }
  return moduleNames[moduleName] || moduleName
}

// 获取日志级别类型
const getLogLevelType = (level) => {
  const types = {
    DEBUG: 'info',
    INFO: 'success',
    WARNING: 'warning',
    ERROR: 'danger',
    CRITICAL: 'danger'
  }
  return types[level] || 'info'
}

// 暴露验证方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields()
})
</script>

<style lang="scss" scoped>
.gateway-logs-config {
  .config-card {
    margin-bottom: 24px;
    border: 1px solid #e4e7ed;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .card-header {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .modules-config {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 24px;
    
    .module-item {
      .module-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .module-name {
          font-weight: 600;
          color: #303133;
          font-size: 16px;
        }
      }
      
      :deep(.el-card) {
        border: 1px solid #e4e7ed;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #409eff;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }
      }
    }
  }
  
  :deep(.el-form-item) {
    margin-bottom: 16px;
    
    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      font-size: 13px;
    }
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
  
  :deep(.el-input-number) {
    width: 100%;
  }
}

@media (max-width: 1200px) {
  .modules-config {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .gateway-logs-config {
    .modules-config {
      grid-template-columns: 1fr;
      gap: 16px;
      
      .module-item {
        :deep(.el-row) {
          .el-col {
            margin-bottom: 12px;
          }
        }
      }
    }
    
    :deep(.el-form-item) {
      margin-bottom: 12px;
      
      .el-form-item__label {
        font-size: 12px;
      }
    }
  }
}
</style> 