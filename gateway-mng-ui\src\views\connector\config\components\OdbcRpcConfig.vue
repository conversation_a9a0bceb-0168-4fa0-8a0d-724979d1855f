<template>
  <div class="odbc-rpc-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>RPC 配置</span>
          <el-tooltip content="配置从IoTCloud到数据库的RPC调用和存储过程" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="rpcConfig" :rules="rules" label-width="140px" ref="formRef">
        <!-- 基础RPC配置 -->
        <div class="config-section">
          <div class="section-title">基础配置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="允许未知RPC">
                <el-switch v-model="rpcConfig.enableUnknownRpc"  />
                <div class="field-hint">是否允许执行未在配置中定义的RPC方法</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="覆盖RPC配置">
                <el-switch v-model="rpcConfig.overrideRpcConfig"  />
                <div class="field-hint">是否允许通过RPC参数覆盖连接器配置</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- RPC方法配置 -->
        <div class="config-section">
          <div class="section-title">
            RPC方法配置
            <el-dropdown @command="handleAddMethod">
              <el-button type="primary" size="small">
                <el-icon><Plus /></el-icon>
                添加RPC方法
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="simple">添加简单方法</el-dropdown-item>
                  <el-dropdown-item command="complex">添加复杂方法</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div v-if="rpcConfig.methods.length === 0" class="empty-state">
            <el-empty description="暂无RPC方法配置" :image-size="60">
              <el-dropdown @command="handleAddMethod">
                <el-button type="primary">
                  添加第一个RPC方法
                  <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="simple">添加简单方法</el-dropdown-item>
                    <el-dropdown-item command="complex">添加复杂方法</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-empty>
          </div>
          
          <div v-else class="methods-list">
            <el-card 
              v-for="(method, index) in rpcConfig.methods" 
              :key="index" 
              class="method-card"
              shadow="hover"
            >
              <template #header>
                <div class="method-header">
                  <div class="method-info">
                    <span class="method-name">{{ getMethodDisplayName(method) }}</span>
                    <el-tag size="small" :type="getMethodTagType(method)">{{ getMethodTypeName(method) }}</el-tag>
                  </div>
                  <div class="method-actions">
                    <el-button type="primary" size="small" link @click="editRpcMethod(method, index)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button type="danger" size="small" link @click="deleteRpcMethod(index)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </template>
              
              <div class="method-summary">
                <div v-if="typeof method === 'string'" class="simple-method">
                  <div class="summary-item">
                    <span class="label">方法名:</span>
                    <span class="value">{{ method }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="label">类型:</span>
                    <span class="value">简单方法（使用默认SQL调用）</span>
                  </div>
                </div>
                <div v-else class="complex-method">
                  <el-row :gutter="16">
                    <el-col :span="8">
                      <div class="summary-item">
                        <span class="label">方法名:</span>
                        <span class="value">{{ method.name || '-' }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="summary-item">
                        <span class="label">SQL语句:</span>
                        <span class="value">{{ method.query || '默认调用' }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="summary-item">
                        <span class="label">参数数量:</span>
                        <span class="value">{{ (method.args || []).length }}</span>
                      </div>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16" v-if="method.result">
                    <el-col :span="12">
                      <div class="summary-item">
                        <span class="label">返回结果:</span>
                        <span class="value">是（SQL函数）</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- RPC帮助信息 -->
        <div class="info-section">
          <el-alert
            title="RPC 配置说明"
            type="info"
            :closable="false"
            show-icon
          >
            <div class="help-content">
              <p><strong>简单方法:</strong> 只需方法名，系统会自动调用同名的存储过程/函数</p>
              <p><strong>复杂方法:</strong> 包含自定义SQL语句和参数配置</p>
              <p><strong>参数顺序:</strong> 参数顺序必须与SQL存储过程/函数参数顺序一致</p>
              <p><strong>支持格式:</strong></p>
              <ul class="format-list">
                <li><strong>简单字符串:</strong> "procedureOne"</li>
                <li><strong>对象配置:</strong> {"name": "procedureTwo", "args": ["One", 2, 3.0]}</li>
                <li><strong>混合模式:</strong> 可以同时包含字符串和对象</li>
              </ul>
            </div>
          </el-alert>
        </div>
      </el-form>
    </el-card>

    <!-- RPC方法配置对话框 -->
    <OdbcRpcMethodDialog
      v-model="dialogVisible"
      :method="currentMethod"
      :is-edit="isEdit"
      @save="handleSaveMethod"
      @cancel="handleCancelMethod"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { QuestionFilled, Plus, Edit, Delete, ArrowDown } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import OdbcRpcMethodDialog from './OdbcRpcMethodDialog.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      enableUnknownRpc: false,
      overrideRpcConfig: true,
      methods: []
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const dialogVisible = ref(false)
const currentMethod = ref(null)
const isEdit = ref(false)
const currentIndex = ref(-1)

// RPC配置数据
const rpcConfig = reactive({
  enableUnknownRpc: false,
  overrideRpcConfig: true,
  methods: []
})

// 表单验证规则
const rules = reactive({
  // 基础验证规则可以在这里添加
})

// 获取方法显示名称
const getMethodDisplayName = (method) => {
  if (typeof method === 'string') {
    return method
  }
  return method.name || '未命名方法'
}

// 获取方法标签类型
const getMethodTagType = (method) => {
  return typeof method === 'string' ? 'success' : 'warning'
}

// 获取方法类型名称
const getMethodTypeName = (method) => {
  return typeof method === 'string' ? '简单方法' : '复杂方法'
}

// 添加RPC方法
const handleAddMethod = (type) => {
  currentMethod.value = null
  isEdit.value = false
  currentIndex.value = -1
  
  if (type === 'simple') {
    // 直接添加简单方法
    const methodName = `procedure${rpcConfig.methods.length + 1}`
    rpcConfig.methods.push(methodName)
    handleChange()
    ElMessage.success('简单方法添加成功')
  } else {
    // 打开对话框添加复杂方法
    dialogVisible.value = true
  }
}

// 编辑RPC方法
const editRpcMethod = (method, index) => {
  currentMethod.value = method
  isEdit.value = true
  currentIndex.value = index
  dialogVisible.value = true
}

// 删除RPC方法
const deleteRpcMethod = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个RPC方法吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    rpcConfig.methods.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 保存RPC方法
const handleSaveMethod = (methodData) => {
  if (isEdit.value && currentIndex.value >= 0) {
    // 编辑模式
    rpcConfig.methods.splice(currentIndex.value, 1, methodData)
    ElMessage.success('RPC方法更新成功')
  } else {
    // 添加模式
    rpcConfig.methods.push(methodData)
    ElMessage.success('RPC方法添加成功')
  }
  
  dialogVisible.value = false
  handleChange()
}

// 取消操作
const handleCancelMethod = () => {
  dialogVisible.value = false
}

// 处理配置变化
const handleChange = () => {
  emit('update:modelValue', { ...rpcConfig })
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(rpcConfig, {
      enableUnknownRpc: false,
      overrideRpcConfig: true,
      methods: [],
      ...newValue
    })
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(rpcConfig, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })
</script>

<style lang="scss" scoped>
.odbc-rpc-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .config-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f8f9fa;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }
  
  .methods-list {
    .method-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .method-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .method-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .method-name {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .method-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .method-summary {
        margin-top: 16px;
        
        .simple-method, .complex-method {
          .summary-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 8px;
            
            .label {
              font-size: 12px;
              color: #909399;
            }
            
            .value {
              font-size: 14px;
              color: #303133;
              font-weight: 500;
              word-break: break-all;
            }
          }
        }
      }
    }
  }

  .info-section {
    margin-top: 24px;
    
    .help-content {
      line-height: 1.6;
      
      p {
        margin: 8px 0;
        
        strong {
          color: #409eff;
        }
      }
      
      .format-list {
        margin: 8px 0 8px 20px;
        
        li {
          margin: 4px 0;
        }
      }
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }
}
</style> 