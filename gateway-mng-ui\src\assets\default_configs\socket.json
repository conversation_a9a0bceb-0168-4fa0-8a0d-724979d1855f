{"socket": {"address": "127.0.0.1", "type": "TCP", "port": 50000, "bufferSize": 1024}, "devices": [{"address": "*:*", "addressFilter": "*:*", "deviceName": "Device Example", "deviceType": "default", "encoding": "utf-8", "telemetry": [{"key": "temp", "byteFrom": 0, "byteTo": -1}, {"key": "hum", "byteFrom": 0, "byteTo": 2}], "attributes": [{"key": "name", "byteFrom": 0, "byteTo": -1}, {"key": "num", "byteFrom": 2, "byteTo": 4}], "attributeRequests": [{"type": "shared", "requestExpression": "${[0:3]==atr}", "attributeNameExpression": "[3:]"}], "attributeUpdates": [{"encoding": "utf-16", "attributeOnThingsBoard": "sharedName"}], "serverSideRpc": [{"methodRPC": "rpcMethod1", "withResponse": true, "encoding": "utf-8"}]}]}