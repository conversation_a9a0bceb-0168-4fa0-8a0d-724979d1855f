# coding=utf-8

# 标准库导入
import inspect
import json
import logging
import logging.handlers
import os
import re
import subprocess
import sys
import threading
import time
from multiprocessing.managers import BaseManager
from platform import system as platform_system

# 第三方库导入
import netifaces
import uvicorn
from fastapi import Depends, FastAPI, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from pydantic import BaseModel

# 本地模块导入
from gateway.data_config import CONNECTOR_TYPES_FILE, USER_NAME_DEAFULT, USER_PWD_DEAFULT
from thingsboard_gateway.gateway.data_storage import get_data_storage

# 路径设置
current_path = os.path.dirname(__file__)
sys.path.append(current_path)

# 常量定义
uri = "/gateway/v1"
DIR_NAME = "config"
configs_dir = os.path.join(current_path, DIR_NAME)
if not os.path.exists(configs_dir):
    os.makedirs(configs_dir)
current_log_path = os.path.join(current_path, 'logs')

# Path to default connector configurations
DEFAULT_CONFIGS_DIR = os.path.join(current_path, 'default_configs')


class GatewayManager(BaseManager):
    pass


def get_gateway():
    manager_address = '/tmp/gateway'
    if platform_system() == 'Windows':
        manager_address = ('127.0.0.1', 9999)

    GatewayManager.register('get_gateway')
    manager = GatewayManager(address=manager_address, authkey=b'gateway')

    try:
        manager.connect()
        gateway = manager.get_gateway()
        return gateway
    except Exception as e:
        logger_.error(f"Failed to connect to gateway manager: {e}")
        return None


class LogUtils:
    def __init__(self, log_name=None, log_path=None):
        caller_filename = inspect.stack()[1].filename.split('\\')[-1]
        self.log_name = log_name if log_name else caller_filename
        self.logfile_path = log_path if log_path else current_log_path
        if not os.path.exists(self.logfile_path):
            os.makedirs(self.logfile_path)

        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(level=logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s')
        self.log_name_path = os.path.join(self.logfile_path, "%s" % (self.log_name + ".log"))
        self.handler = logging.handlers.RotatingFileHandler(self.log_name_path, maxBytes=1024 * 1024, backupCount=5,
                                                          encoding="utf-8")
        self.handler.setLevel(logging.INFO)
        self.handler.setFormatter(formatter)

        if self.logger.hasHandlers():
            self.logger.handlers.clear()

        self.logger.addHandler(self.handler)
        self.console = logging.StreamHandler()
        self.console.setLevel(logging.INFO)
        self.console.setFormatter(formatter)
        self.logger.addHandler(self.console)
        self.console.close()

    def get_log(self):
        return self.logger


app = FastAPI()
# 创建一个锁对象
file_lock = threading.Lock()
logger_ = LogUtils("operation_python").get_log()


# ###############################################################################
# 用户认证
# 从数据库中读取令牌
security = HTTPBearer()


# 定义一个依赖函数来验证令牌
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    # logger_.info(f"验证token: {token}")
    
    # 使用data_storage验证token
    data_storage = get_data_storage()
    is_token_exist = data_storage.is_token_exist(token)
    
    if is_token_exist:
        return token
    else:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


# Models
class NetworkConfig(BaseModel):
    interface: str
    address: str
    netmask: str
    gateway: str
    dns1: str
    dns2: str = None

class RemoteAssistConfig(BaseModel):
    vkey: str

class RemoteAssistStatus(BaseModel):
    status: str = "stopped"  # running/stopped


class NetworkStatus(BaseModel):
    task_id: str


class DataSaveItem(BaseModel):
    file_name: str
    file_text: str
    file_type: str = "string"
    dirname: str = DIR_NAME


class FileStatusItem(BaseModel):
    file_list: list


class DeviceConnectorsItem(BaseModel):
    device_list: list # This is a list of connector names


class UserLoginItem(BaseModel):
    name: str
    password: str
    new_password: str = None
    confirm_password: str = None


class UserLoginOutItem(BaseModel):
    token: str


class WifiScanRequest(BaseModel):
    interface: str


class WifiConnectRequest(BaseModel):
    interface: str
    ssid: str
    password: str = None

# ###############################################################################
# 1. 网络配置相关API (Network Configuration APIs)
# ###############################################################################
@app.get(f"{uri}/network/interfaces")
async def get_network_interfaces(token: str = Depends(verify_token)):
    """获取所有网络接口及其状态"""
    try:
        interfaces = netifaces.interfaces()
        # 过滤掉lo接口
        interfaces = [iface for iface in interfaces if iface != 'lo']

        result = []
        for iface in interfaces:
            status = {}
            try:
                # 读取接口状态(up/down)
                with open(f"/sys/class/net/{iface}/operstate") as f:
                    status['state'] = f.read().strip()

                # 读取carrier状态
                with open(f"/sys/class/net/{iface}/carrier") as f:
                    status['carrier'] = f.read().strip() == "1"

                # 读取速率信息
                try:
                    with open(f"/sys/class/net/{iface}/speed") as f:
                        speed = f.read().strip()
                        status['speed'] = f"{speed}Mbps" if speed.isdigit() else "未知"
                except:
                    status['speed'] = "未知"

            except Exception as e:
                logger_.error(f"获取{iface}状态失败: {str(e)}")
                status = {
                    'state': 'unknown',
                    'carrier': False,
                    'speed': '未知'
                }

            result.append({
                'name': iface,
                'status': status
            })

        return {"msg": "success", "data": result}
    except Exception as e:
        logger_.error(f"获取网络接口失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.get(f"{uri}/network/config")
async def get_network_config(interface: str, token: str = Depends(verify_token)):
    """获取指定网络接口的配置"""
    try:
        # 获取IP地址信息
        addrs = netifaces.ifaddresses(interface)
        ipv4_info = addrs.get(netifaces.AF_INET, [{}])[0]

        # 获取默认网关
        gateways = netifaces.gateways()
        default_gateway = gateways.get('default', {}).get(netifaces.AF_INET, (None, None))[0]

        # 获取DNS服务器
        dns_servers = []
        try:
            with open('/etc/resolv.conf', 'r') as f:
                for line in f:
                    if line.startswith('nameserver'):
                        dns_servers.append(line.split()[1])
        except:
            pass

        config = {
            "interface": interface,
            "address": ipv4_info.get('addr', ''),
            "netmask": ipv4_info.get('netmask', ''),
            "gateway": default_gateway or '',
            "dns1": dns_servers[0] if len(dns_servers) > 0 else '',
            "dns2": dns_servers[1] if len(dns_servers) > 1 else ''
        }

        return {"msg": "success", "data": config}
    except Exception as e:
        logger_.error(f"获取网络配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.post(f"{uri}/network/config")
async def set_network_config(config: NetworkConfig, token: str = Depends(verify_token)):
    """设置网络配置"""
    try:
        # 生成任务ID
        task_id = str(int(time.time()))
        status_file = f"/tmp/network_status_{task_id}"

        # 创建状态文件,标记为正在执行
        with open(status_file, 'w') as f:
            f.write("running")

        # 创建配置脚本
        configure_script = f"""#!/bin/bash

# 更新状态为正在配置
echo "applying" > {status_file}

# 创建新的连接配置
CONN_NAME="wired-{config.interface}"

# 删除已存在的同名连接
nmcli connection delete "$CONN_NAME" 2>/dev/null || true

# 创建新的连接
nmcli connection add type ethernet con-name "$CONN_NAME" ifname {config.interface} \
    ipv4.method manual \
    ipv4.addresses {config.address}/{config.netmask} \
    ipv4.gateway {config.gateway}

# 配置DNS
if [ -n "{config.dns1}" ]; then
    nmcli connection modify "$CONN_NAME" ipv4.dns "{config.dns1}"
fi
if [ -n "{config.dns2}" ]; then
    nmcli connection modify "$CONN_NAME" ipv4.dns "{config.dns1} {config.dns2}"
fi

# 激活连接
nmcli connection up "$CONN_NAME"

# 等待连接建立
sleep 5

# 测试网络连通性
if ping -c 1 {config.gateway} > /dev/null 2>&1; then
    echo "success" > {status_file}
else
    # 配置失败,删除连接
    nmcli connection delete "$CONN_NAME"
    echo "failed" > {status_file}
fi
"""
        # 写入并执行脚本
        with open('/tmp/configure_network.sh', 'w') as f:
            f.write(configure_script)
        subprocess.run(['chmod', '+x', '/tmp/configure_network.sh'])

        # 在后台执行配置脚本
        subprocess.Popen(['sudo', '/tmp/configure_network.sh'],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        preexec_fn=os.setpgrp)

        return {"msg": "success", "data": {"task_id": task_id}}
    except Exception as e:
        logger_.error(f"设置网络配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.get(f"{uri}/network/status")
async def get_network_status(task_id: str, token: str = Depends(verify_token)):
    """获取网络配置任务状态"""
    try:
        status_file = f"/tmp/network_status_{task_id}"
        print(status_file)
        if not os.path.exists(status_file):
            return {"msg": "success", "data": {"status": "running"}}

        with open(status_file, 'r') as f:
            status = f.read().strip()
        print(status)
        if status == "success":
            subprocess.run(['rm', status_file])
            return {"msg": "success", "data": {"status": "success"}}
        elif status == "failed":
            subprocess.run(['rm', status_file])
            return {"msg": "fail", "data": "网络配置失败,已自动回滚"}

        return {"msg": "success", "data": {"status": "running"}}
    except Exception as e:
        logger_.error(f"获取配置状态失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.post(f"{uri}/network/wifi/scan")
async def scan_wifi_networks(request: WifiScanRequest, token: str = Depends(verify_token)):
    """扫描指定无线网卡可用的WiFi网络"""
    try:
        # 检查是否为无线网卡
        if not request.interface.startswith(('wlan', 'wifi', 'wlp')):
            return {"msg": "fail", "data": "指定的接口不是无线网卡"}

        # 使用nmcli扫描WiFi网络
        cmd = f"nmcli -f SSID,SIGNAL,SECURITY device wifi list ifname {request.interface}"
        result = subprocess.run(cmd.split(), capture_output=True, text=True)
        
        if result.returncode != 0:
            return {"msg": "fail", "data": f"扫描WiFi网络失败: {result.stderr}"}

        # 解析nmcli输出
        networks = []
        lines = result.stdout.strip().split('\n')
        headers = lines[0].split()
        
        for line in lines[1:]:
            parts = line.split()
            if len(parts) >= 3:
                networks.append({
                    "ssid": parts[0],
                    "signal": int(parts[1]),
                    "security": parts[2] if parts[2] != "--" else "开放"
                })
                # 只返回前10个网络
                if len(networks) >= 10:
                    break

        return {"msg": "success", "data": networks}
    except Exception as e:
        logger_.error(f"扫描WiFi网络失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.post(f"{uri}/network/wifi/connect")
async def connect_wifi_network(
    request: WifiConnectRequest,
    token: str = Depends(verify_token)
):
    """连接到指定的WiFi网络"""
    try:
        # 检查是否为无线网卡
        if not request.interface.startswith(('wlan', 'wifi', 'wlp')):
            return {"msg": "fail", "data": "指定的接口不是无线网卡"}

        # 生成任务ID
        task_id = str(int(time.time()))
        status_file = f"/tmp/wifi_status_{task_id}"

        # 创建状态文件,标记为正在执行
        with open(status_file, 'w') as f:
            f.write("running")

        # 创建连接脚本
        connect_script = f"""#!/bin/bash

# 更新状态为正在配置
echo "applying" > {status_file}

# 尝试连接WiFi网络
if [ -n "{request.password}" ]; then
    nmcli device wifi connect "{request.ssid}" password "{request.password}" ifname {request.interface}
else
    nmcli device wifi connect "{request.ssid}" ifname {request.interface}
fi

# 检查连接结果
if [ $? -eq 0 ]; then
    echo "success" > {status_file}
else
    echo "failed" > {status_file}
fi
"""
        # 写入并执行脚本
        with open('/tmp/connect_wifi.sh', 'w') as f:
            f.write(connect_script)
        subprocess.run(['chmod', '+x', '/tmp/connect_wifi.sh'])

        # 在后台执行连接脚本
        subprocess.Popen(['sudo', '/tmp/connect_wifi.sh'],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        preexec_fn=os.setpgrp)

        return {"msg": "success", "data": {"task_id": task_id}}
    except Exception as e:
        logger_.error(f"连接WiFi网络失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.get(f"{uri}/network/wifi/status")
async def get_wifi_status(task_id: str, token: str = Depends(verify_token)):
    """获取WiFi连接任务状态"""
    try:
        status_file = f"/tmp/wifi_status_{task_id}"
        if not os.path.exists(status_file):
            return {"msg": "success", "data": {"status": "running"}}

        with open(status_file, 'r') as f:
            status = f.read().strip()

        if status == "success":
            subprocess.run(['rm', status_file])
            return {"msg": "success", "data": {"status": "success"}}
        elif status == "failed":
            subprocess.run(['rm', status_file])
            return {"msg": "fail", "data": "WiFi连接失败"}

        return {"msg": "success", "data": {"status": "running"}}
    except Exception as e:
        logger_.error(f"获取WiFi连接状态失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.post("/gateway/v1/network/disconnect")
async def disconnect_network_connection(interface: str):
    """断开网络连接"""
    try:
        # 获取接口对应的连接名称
        result = subprocess.run(['nmcli', '-t', '-f', 'NAME,DEVICE', 'connection', 'show', '--active'], 
                              capture_output=True, text=True, check=True)
        
        # 查找对应接口的连接名称
        connection_name = None
        for line in result.stdout.splitlines():
            name, device = line.split(':')
            if device == interface:
                connection_name = name
                break
        
        if not connection_name:
            return {"msg": "error", "data": f"未找到接口 {interface} 的活动连接"}
        
        # 先停用连接
        subprocess.run(['nmcli', 'connection', 'down', connection_name], check=True)
        # 然后删除连接配置
        subprocess.run(['nmcli', 'connection', 'delete', connection_name], check=True)
        return {"msg": "success", "data": "网络连接已断开"}
    except subprocess.CalledProcessError as e:
        return {"msg": "error", "data": f"断开网络连接失败: {str(e)}"}
    except Exception as e:
        return {"msg": "error", "data": f"断开网络连接失败: {str(e)}"}


# ###############################################################################
# 2. 远程协助相关API (Remote Assistance APIs)
# ###############################################################################
@app.post(f"{uri}/remote_assist/config")
async def set_remote_assist_config(config: RemoteAssistConfig, token: str = Depends(verify_token)):
    """设置远程协助vkey配置"""
    try:
        # vkey配置文件路径
        config_file = os.path.join(current_path, 'db', 'remote_assist.json')

        # 检查是否已经配置过
        if os.path.exists(config_file):
            return {"msg": "fail", "data": "vkey已配置,不可修改"}

        # 写入配置文件
        with open(config_file, 'w') as f:
            json.dump({"vkey": config.vkey}, f)

        return {"msg": "success", "data": "vkey配置成功"}
    except Exception as e:
        logger_.error(f"设置远程协助配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.get(f"{uri}/remote_assist/config")
async def get_remote_assist_config(token: str = Depends(verify_token)):
    """获取远程协助vkey配置"""
    try:
        config_file = os.path.join(current_path, 'db', 'remote_assist.json')
        if not os.path.exists(config_file):
            return {"msg": "success", "data": {"vkey": "", "configured": False}}

        with open(config_file, 'r') as f:
            config = json.load(f)

        return {"msg": "success", "data": {"vkey": config["vkey"], "configured": True}}
    except Exception as e:
        logger_.error(f"获取远程协助配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.post(f"{uri}/remote_assist/start")
async def start_remote_assist(token: str = Depends(verify_token)):
    """启动远程协助"""
    try:
        # 检查是否已配置vkey
        config_file = os.path.join(current_path, 'db', 'remote_assist.json')
        if not os.path.exists(config_file):
            return {"msg": "fail", "data": "请先配置vkey"}

        # 读取vkey配置
        with open(config_file, 'r') as f:
            config = json.load(f)

        # 检查是否已经在运行
        pid_file = "/tmp/remote_assist.pid"
        if os.path.exists(pid_file):
            return {"msg": "fail", "data": "远程协助已在运行"}

        # 启动npc客户端
        cmd = f"npc -server=www.iotcloud.top:8024 -vkey={config['vkey']} -type=tcp"
        process = subprocess.Popen(cmd.split(),
                                stdout=subprocess.DEVNULL,
                                stderr=subprocess.DEVNULL)

        # 保存进程ID
        with open(pid_file, 'w') as f:
            f.write(str(process.pid))

        return {"msg": "success", "data": "远程协助已启动"}
    except Exception as e:
        logger_.error(f"启动远程协助失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.post(f"{uri}/remote_assist/stop")
async def stop_remote_assist(token: str = Depends(verify_token)):
    """停止远程协助"""
    try:
        pid_file = "/tmp/remote_assist.pid"
        if not os.path.exists(pid_file):
            return {"msg": "fail", "data": "远程协助未在运行"}

        # 读取进程ID
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        # 终止进程
        try:
            os.kill(pid, 9)
        except ProcessLookupError:
            pass

        # 删除pid文件
        os.remove(pid_file)

        return {"msg": "success", "data": "远程协助已停止"}
    except Exception as e:
        logger_.error(f"停止远程协助失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}

@app.get(f"{uri}/remote_assist/status")
async def get_remote_assist_status(token: str = Depends(verify_token)):
    """获取远程协助状态"""
    try:
        pid_file = "/tmp/remote_assist.pid"
        status = "running" if os.path.exists(pid_file) else "stopped"
        return {"msg": "success", "data": {"status": status}}
    except Exception as e:
        logger_.error(f"获取远程协助状态失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


# ###############################################################################
# 3. 配置文件管理API (File and Configuration Management APIs)
# ###############################################################################
@app.get(f"{uri}/read_filename")
async def read_filename_func(dirname=DIR_NAME, token: str = Depends(verify_token)):
    logger_.info(f"读取 {dirname} 下的第一级目录文件")
    filename_list = []
    try:
        files_dirname = os.path.join(current_path, dirname)
        for item in os.listdir(files_dirname):
            item_path = os.path.join(files_dirname, item)
            if os.path.isfile(item_path):
                if str(item).endswith(".json"):
                    filename_list.append(item)
        return {"msg": "success", "data": filename_list}
    except Exception as e:
        logger_.error(f"{dirname} 查询文件失败，失败原因:{str(e)}")
        return {"msg": "fail", "data": filename_list}


@app.get(f"{uri}/read_json")
async def read_json_func(file_name, dirname=DIR_NAME, token: str = Depends(verify_token)):
    logger_.info(f"{dirname} | 读取配置json {file_name}")
    data = {}
    try:
        files_dirname = os.path.join(current_path, dirname)
        with file_lock:
            with open(os.path.join(files_dirname, file_name), encoding="utf-8", mode="r") as fp:
                data = json.load(fp)
            return {"msg": "success", "data": data}
    except Exception as e:
        logger_.error(f"{dirname}/{file_name} 读取文件失败，失败原因:{str(e)}")
        return {"msg": "fail", "data": data}


@app.get(f"{uri}/delete_json")
async def delete_json_func(file_name, dirname=DIR_NAME, token: str = Depends(verify_token)):
    logger_.info(f"{dirname} | 删除文件 {file_name}")
    try:
        files_dirname = os.path.join(current_path, dirname)
        os.remove(os.path.join(files_dirname, file_name))
        return {"msg": "success", "data": "delete success"}
    except Exception as e:
        logger_.error(f"{dirname}/{file_name} 读取文件失败，失败原因:{str(e)}")
        return {"msg": "fail", "data": "delete fail"}


@app.post(f"{uri}/write_json")
async def write_json_func(dataitem: DataSaveItem, token: str = Depends(verify_token)):
    file_name = dataitem.file_name
    if ".json" not in file_name:
        file_name = file_name + ".json"

    file_text = dataitem.file_text
    dirname = dataitem.dirname
    files_dirname = os.path.join(os.path.join(current_path, dirname), file_name)
    logger_.info(f"{dirname} | 写入文件 {file_name}")

    try:
        json_data = json.loads(file_text)
        with file_lock:
            with open(files_dirname, "w", encoding="utf-8") as fp:
                json.dump(json_data, fp, indent=4)

        gateway = get_gateway()
        if gateway:
            if file_name == "tb_gateway.json":
                logger_.info("Reloading main gateway config...")
                result = gateway.reload_main_config()
                if not result.get("success"):
                    return {"msg": "fail", "data": f"Failed to reload main config: {result.get('error')}"}
            else:
                connector_name = file_name.replace(".json", "")
                
                # 检查连接器是否已在主配置中启用
                if _is_connector_enabled(connector_name):
                    logger_.info(f"Applying new config for connector {connector_name}...")
                    result = gateway.apply_connector_config(connector_name, json_data)
                    if not result.get("success"):
                        return {"msg": "fail", "data": f"Failed to apply connector config: {result.get('error')}"}
                else:
                    logger_.info(f"Connector {connector_name} is not enabled in main config, only saving file...")

            return {"msg": "success", "data": json_data}
        else:
            # 如果无法连接到网关服务，只保存文件
            logger_.warning("Could not connect to gateway service, only saving configuration file.")
            return {"msg": "success", "data": json_data}
    except Exception as e:
        logger_.error(f"Error in write_json_func: {e}", exc_info=True)
        return {"msg": "fail", "data": str(e)}


def _is_connector_enabled(connector_name: str) -> bool:
    """检查连接器是否已在主配置文件中启用"""
    try:
        config_file = os.path.join(current_path, 'config', 'tb_gateway.json')
        with open(config_file, 'r', encoding='utf-8') as f:
            main_config = json.load(f)
        
        connectors = main_config.get('connectors', [])
        for connector in connectors:
            if connector.get('name') == connector_name:
                return True
        return False
    except Exception as e:
        logger_.error(f"Error checking if connector {connector_name} is enabled: {e}")
        return False


def _process_connector_status(gateway, connector_name: str):
    """
    处理连接器状态，包含特殊逻辑处理
    特别是针对request连接器的特殊处理
    """
    try:
        # 获取基本连接器状态
        status = gateway.get_connector_status(connector_name)
        
        # 如果是错误情况，直接返回
        if isinstance(status, str):
            return status
        
        # 特殊处理：对于request连接器，检查设备活跃状态
        if "request" in connector_name:
            # 获取所有设备信息
            active_devices = gateway.get_active_devices()
            
            # 查找属于该连接器的设备
            connector_devices = [
                device for device in active_devices 
                if device.get("connector_name") == connector_name
            ]
            
            # 如果找到设备，检查是否有活跃数据
            if connector_devices:
                has_active_data = any(
                    device.get("last_receiving_data") is not None 
                    for device in connector_devices
                )
                return {'connected': has_active_data}
        
        # 对于其他连接器，返回基本状态
        return status
        
    except Exception as e:
        logger_.error(f"Error processing connector status for {connector_name}: {e}")
        return f'Error processing connector {connector_name}: {str(e)}'


@app.get(f"{uri}/connector_types")
async def get_connector_types(token: str = Depends(verify_token)):
    """获取连接器实例类型映射配置"""
    try:
        if not os.path.exists(CONNECTOR_TYPES_FILE):
            return {"msg": "success", "data": {"connector_instances": {}}}

        with open(CONNECTOR_TYPES_FILE, 'r') as f:
            config = json.load(f)

        return {"msg": "success", "data": config}
    except Exception as e:
        logger_.error(f"获取连接器类型配置失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}





@app.post(f"{uri}/connector_metadata")
async def update_connector_metadata(data: dict, token: str = Depends(verify_token)):
    """更新连接器元数据（包括类型、名称、isLocalOnly等）"""
    try:
        connector_key = data.get('connector_key')
        metadata = data.get('metadata', {})
        
        if not connector_key:
            return {"msg": "fail", "data": "connector_key is required"}

        # 确保目录存在
        os.makedirs(os.path.dirname(CONNECTOR_TYPES_FILE), exist_ok=True)

        # 读取现有配置
        config = {"connector_instances": {}}
        if os.path.exists(CONNECTOR_TYPES_FILE):
            with open(CONNECTOR_TYPES_FILE, 'r') as f:
                config = json.load(f)

        # 更新连接器元数据
        if 'connector_instances' not in config:
            config['connector_instances'] = {}
        
        config['connector_instances'][connector_key] = metadata

        # 写入配置文件
        with open(CONNECTOR_TYPES_FILE, 'w') as f:
            json.dump(config, f, indent=4)

        return {"msg": "success", "data": "连接器元数据更新成功"}
    except Exception as e:
        logger_.error(f"更新连接器元数据失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.delete(f"{uri}/connector_metadata")
async def delete_connector_metadata(connector_key: str, token: str = Depends(verify_token)):
    """删除连接器元数据"""
    try:
        if not connector_key:
            return {"msg": "fail", "data": "connector_key is required"}

        # 读取现有配置
        if not os.path.exists(CONNECTOR_TYPES_FILE):
            return {"msg": "success", "data": "连接器元数据删除成功"}

        with open(CONNECTOR_TYPES_FILE, 'r') as f:
            config = json.load(f)

        # 删除连接器元数据
        if 'connector_instances' in config and connector_key in config['connector_instances']:
            del config['connector_instances'][connector_key]

            # 写入配置文件
            with open(CONNECTOR_TYPES_FILE, 'w') as f:
                json.dump(config, f, indent=4)

        return {"msg": "success", "data": "连接器元数据删除成功"}
    except Exception as e:
        logger_.error(f"删除连接器元数据失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}








# ###############################################################################
# 4. 网关和连接器状态API (Gateway and Connector Status APIs)
# ###############################################################################
@app.get(f"{uri}/file_status")
async def read_file_status(type_: str, connector_name: str = None, token: str = Depends(verify_token)):
    """获取网关或连接器的状态"""
    logger_.info(f"Getting status for type: {type_}, connector: {connector_name}")
    gateway = get_gateway()
    if not gateway:
        return {"msg": "fail", "data": "Gateway service not running or accessible"}

    try:
        if type_ == "gateway_s":
            status = gateway.get_status()
            # 保持原有数据格式以兼容前端
            return {"msg": "success", "data": [json.dumps(status)]}
        elif type_ == "connector_l":
            connectors = gateway.get_available_connectors()
            # 返回连接器名称列表
            return {"msg": "success", "data": list(connectors.values())}
        elif type_ == "connector_s":
            if not connector_name:
                return {"msg": "fail", "data": "connector_name is required for type connector_s"}
            status = _process_connector_status(gateway, connector_name)
            if isinstance(status, str):  # 错误情况
                return {"msg": "fail", "data": status}
            # 保持原有数据格式以兼容前端
            return {"msg": "success", "data": [json.dumps(status)]}
        else:
            logger_.warning(f"Invalid type parameter in file_status: {type_}")
            return {"msg": "fail", "data": "Invalid 'type' parameter. Use 'gateway_s', 'connector_l', or 'connector_s'."}
    except Exception as e:
        logger_.error(f"Error getting status: {e}", exc_info=True)
        return {"msg": "fail", "data": "An error occurred while getting status."}


@app.post(f"{uri}/file_status/list")
async def read_file_status_list(filestatusitem: FileStatusItem, token: str = Depends(verify_token)):
    """批量获取连接器状态"""
    connector_list = filestatusitem.file_list
    logger_.info(f"Getting status for multiple connectors: {connector_list}")

    gateway = get_gateway()
    if not gateway:
        return {"msg": "fail", "data": "Gateway service not running or accessible"}

    status_list = []
    try:
        for connector_name in connector_list:
            connector_name = str(connector_name)
            status = _process_connector_status(gateway, connector_name)
            if isinstance(status, str): # 错误情况
                status_list.append({connector_name: status})
            else:
                # 保持原有数据格式以兼容前端
                status_list.append({connector_name: json.dumps(status)})
        return {"msg": "success", "data": status_list}
    except Exception as e:
        logger_.error(f"Error in read_file_status_list: {e}", exc_info=True)
        return {"msg": "fail", "data": "An error occurred while getting status list."}


@app.get(f"{uri}/file_lasttime/list")
async def read_file_time(token: str = Depends(verify_token)):
    logger_.info(f"读取本地数据库存储操作（新：data_storage）")
    result = get_data_storage().get_connector_activity_list()
    return {"msg": "success", "data": result}


@app.get(f"{uri}/file_config/list")
async def read_file_config_list(token: str = Depends(verify_token)):
    connectors_list = []
    logger_.info(f"读取connector列表")
    json_path_ = os.path.join(current_path, 'config')
    with open(os.path.join(json_path_, "tb_gateway.json"), encoding="utf-8", mode="r") as fp:
        data = json.load(fp)
    try:
        connectors = data.get("connectors")
        for connector_ in connectors:
            connectors_list.append(connector_)
    except Exception as e:
        logger_.error(f"json 格式有问题")
    return {"msg": "success", "data": connectors_list}


@app.post(f"{uri}/devices/connectors")
async def read_device_connectors(devicelistitem: DeviceConnectorsItem, token: str = Depends(verify_token)):
    """获取指定连接器下的设备列表"""
    connector_list = devicelistitem.device_list
    logger_.info(f"Reading devices for connectors: {connector_list}")

    gateway = get_gateway()
    if not gateway:
        return {"msg": "fail", "data": "Gateway service not running or accessible"}

    status_list = []
    try:
        for connector_name in connector_list:
            connector_name = str(connector_name)
            devices = gateway.get_connectors_by_name(connector_name)
            if isinstance(devices, str):  # 错误情况
                status_list.append({connector_name: []})
            else:
                # 返回设备名称列表
                status_list.append({connector_name: list(devices.values())})
        return {"msg": "success", "data": status_list}
    except Exception as e:
        logger_.error(f"Error getting device connectors: {e}", exc_info=True)
        return {"msg": "fail", "data": "An error occurred while getting device connectors."}

@app.get(f"{uri}/devices/active")
async def get_all_connected_devices(token: str = Depends(verify_token)):
    """
    获取所有已连接设备及其最后一次正确获取数据的活跃时间
    """
    gateway = get_gateway()
    if not gateway:
        return {"msg": "fail", "data": "Gateway service not running or accessible"}

    try:
        # 使用新的get_active_devices函数获取设备活跃信息
        active_devices = gateway.get_active_devices()
        
        # 处理时间格式
        devices = []
        for device_info in active_devices:
            last_time = device_info.get("last_receiving_data")
            if last_time:
                # 转换为可读时间
                try:
                    last_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(last_time))
                except Exception:
                    last_time_str = str(last_time)
            else:
                last_time_str = None
            
            devices.append({
                "device_name": device_info.get("device_name"),
                "last_active": last_time_str,
                "connector_type": device_info.get("connector_type"),
                "connector_name": device_info.get("connector_name"),
                "device_type": device_info.get("device_type")
            })
        
        return {"msg": "success", "data": devices}
    except Exception as e:
        logger_.error(f"Error getting connected devices: {e}", exc_info=True)
        return {"msg": "fail", "data": "An error occurred while getting connected devices."}

@app.get(f"{uri}/device/data")
async def get_device_data(
    device_name: str,
    page: int = 1,
    page_size: int = 100,
    token: str = Depends(verify_token)
):
    """
    获取指定设备已获取的数据记录
    参数:
        device_name: 设备名称
        page: 页码，从1开始
        page_size: 每页数据条数，默认100
    """
    gateway = get_gateway()
    if not gateway:
        return {"msg": "fail", "data": "Gateway service not running or accessible"}

    try:
        # 从本地数据库获取数据
        result = gateway.get_local_device_data(device_name, page, page_size)
        
        return {
            "msg": "success",
            "data": {
                "total": result['total'],
                "page": page,
                "page_size": page_size,
                "total_pages": (result['total'] + page_size - 1) // page_size,
                "items": result['data']
            }
        }
    except Exception as e:
        logger_.error(f"Error getting data for device {device_name}: {e}", exc_info=True)
        return {"msg": "fail", "data": f"An error occurred while getting data for device {device_name}."}

@app.get(f"{uri}/all_device_data")
async def get_all_device_data(
    page: int = 1,
    page_size: int = 100,
    token: str = Depends(verify_token)
):
    """
    获取所有曾经获取的数据
    参数:
        page: 页码，从1开始
        page_size: 每页数据条数，默认100
    """
    gateway = get_gateway()
    if not gateway:
        return {"msg": "fail", "data": "Gateway service not running or accessible"}

    try:
        # 从本地数据库获取数据
        result = gateway.get_local_all_data(page, page_size)
        
        return {
            "msg": "success",
            "data": {
                "total": result['total'],
                "page": page,
                "page_size": page_size,
                "total_pages": (result['total'] + page_size - 1) // page_size,
                "items": result['data']
            }
        }
    except Exception as e:
        logger_.error(f"Error getting all device data: {e}", exc_info=True)
        return {"msg": "fail", "data": "An error occurred while getting all device data."}

# ###############################################################################
# 5. 用户管理API (User Management APIs)
# ###############################################################################
# @app.post(f"{uri}/user/register")
# async def user_register(useritem: UserLoginItem):
#     name = useritem.name
#     password = useritem.password
#     confirm_password = useritem.confirm_password
#     if password != confirm_password:
#         return {"msg": "fail", "data": "两次输入的密码不相同"}
#     logger_.info(f"{name}|{password}用户注册")

#     pattern = r'^[A-Za-z0-9]{4,12}$'
#     if not re.match(pattern, name):
#         return {"msg": "fail", "data": f"{name}名字不符合规范，只允许密码只包含字母和数字，并且长度在4到12个字符之间"}
#     is_name_exist = \
#         UserOperation.is_data_exist(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME, condition_name=name)
#     if is_name_exist:
#         return {"mag": "fail", "data": f"不允许起相同的名字{name}"}
#     try:
#         if len(password) == 0:
#             return {"msg": "fail", "data": f"密码长度不能为0"}
#         else:
#             if re.match(pattern, password):
#                 UserOperation.register_user(db_name=USER_DB_DIR, table_name=USER_TABLE_NAME,
#                                           condition_name=name, password=password, time_stamp=int(time.time()))
#                 return {"msg": "success", "data": f"{name}创建成功"}
#             else:
#                 return {"msg": "fail", "data": f"{password}不符合规范，只允许密码只包含字母和数字，并且长度在4到12个字符之间"}
#     except Exception as e:
#         return {"mag": "fail", "data": f"创建用户失败: {str(e)}"}
#

@app.post(f"{uri}/user/login")
async def user_login(useritem: UserLoginItem):
    name = useritem.name
    password = useritem.password
    logger_.info(f"{name}|{password}用户登录")
    
    # 使用data_storage检查用户是否存在
    data_storage = get_data_storage()
    is_name_exist = data_storage.is_user_exist(name)
    
    if not is_name_exist:
        return {"msg": "fail", "data": f"用户{name}不存在"}

    try:
        new_password = password[16:-16]
    except Exception as e:
        return {"msg": "fail", "data": "密码构建错误"}
    
    get_password = data_storage.login_user(name)

    if new_password != get_password[0]:
        return {"msg": "fail", "data": "密码错误"}
    return {"msg": "success", "data": get_password[1]}


@app.post(f"{uri}/user/update_pwd")
async def user_update(useritem: UserLoginItem):
    name = useritem.name
    password = useritem.password
    new_password = useritem.new_password
    confirm_password = useritem.confirm_password
    
    data_storage = get_data_storage()
    
    get_password = data_storage.get_user_password(name)
    if password != get_password[0]:
        return {"msg": "fail", "data": "原密码输入错误"}
    if new_password != confirm_password:
        return {"msg": "fail", "data": "两次输入的密码不相同"}
    logger_.info(f"{name}| 用户账号密码修改")

    if len(new_password) == 0:
        return {"msg": "fail", "data": f"密码长度不能为0"}
    else:
        pattern = r'^[A-Za-z0-9]{4,12}$'
        if re.match(pattern, new_password):
            try:
                data_storage.modify_user_password(name, new_password)
                return {"msg": "success", "data": "修改密码成功"}
            except Exception as e:
                return {"msg": "fail", "data": "修改密码失败"}
        else:
            return {"msg": "fail", "data": f"{new_password}不符合规范，只允许密码只包含字母和数字，并且长度在4到12个字符之间"}


@app.get(f"{uri}/user/login_out")
async def user_login_out(token: str = Depends(verify_token)):
    logger_.info("用户登出")
    try:
        data_storage = get_data_storage()
        data_storage.logout_user(token)
    except Exception as e:
        return {"msg": "fail", "data": f"登出失败: {str(e)}"}
    return {"msg": "success", "data": "登出成功"}


@app.get(f"{uri}/user/detail")
async def user_detail(token: str = Depends(verify_token)):
    logger_.info(f"用户查看详情")
    try:
        data_storage = get_data_storage()
        rows = data_storage.get_user_by_token(token)
        return {"msg": "success", "data": {"name": rows[0], "password": rows[1]}}
    except Exception as e:
        return {"msg": "fail", "data": "获取用户详情失败"}


class UserDefault:
    @staticmethod
    def create_user():
        data_storage = get_data_storage()
        is_name_exist = data_storage.is_user_exist(USER_NAME_DEAFULT)
        if is_name_exist:
            pass
        else:
            data_storage.register_user(USER_NAME_DEAFULT, USER_PWD_DEAFULT, int(time.time()))

# Create default admin user on startup
UserDefault.create_user()

# ###############################################################################
# 系统监控相关API (System Monitoring APIs)
# ###############################################################################
@app.get(f"{uri}/system/temperature")
async def get_system_temperature(token: str = Depends(verify_token)):
    """获取系统温度信息"""
    try:
        result = subprocess.run(['sensors', '-j'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            sensors_data = json.loads(result.stdout)
            
            # 优先提取coretemp的CPU核心温度
            temperatures = []
            cpu_temps = []
            
            for device_name, device_data in sensors_data.items():
                # 优先处理coretemp设备（CPU温度）
                if 'coretemp' in device_name.lower():
                    for key, value in device_data.items():
                        if key.startswith('Core') and isinstance(value, dict):
                            for temp_key, temp_value in value.items():
                                if temp_key.endswith('_input'):
                                    cpu_temps.append(temp_value)
                                    temperatures.append({
                                        'device': f"{device_name}-{key}",
                                        'temperature': temp_value,
                                        'unit': '°C',
                                        'type': 'cpu_core'
                                    })
                        elif key.startswith('Package') and isinstance(value, dict):
                            for temp_key, temp_value in value.items():
                                if temp_key.endswith('_input'):
                                    temperatures.append({
                                        'device': f"{device_name}-{key}",
                                        'temperature': temp_value,
                                        'unit': '°C',
                                        'type': 'cpu_package'
                                    })
                
                # 处理其他温度传感器
                elif 'temp1' in device_data and 'temp1_input' in device_data['temp1']:
                    temp = device_data['temp1']['temp1_input']
                    temperatures.append({
                        'device': device_name,
                        'temperature': temp,
                        'unit': '°C',
                        'type': 'other'
                    })
                
                # 检查其他可能的温度传感器
                for key, value in device_data.items():
                    if key.startswith('Core') and isinstance(value, dict) and 'coretemp' not in device_name.lower():
                        for temp_key, temp_value in value.items():
                            if temp_key.endswith('_input'):
                                temperatures.append({
                                    'device': f"{device_name}-{key}",
                                    'temperature': temp_value,
                                    'unit': '°C',
                                    'type': 'other'
                                })
            
            # 计算CPU核心平均温度
            cpu_avg_temp = None
            if cpu_temps:
                cpu_avg_temp = round(sum(cpu_temps) / len(cpu_temps), 1)
            
            return {
                "msg": "success", 
                "data": {
                    "temperatures": temperatures,
                    "cpu_average": cpu_avg_temp,
                    "cpu_cores": cpu_temps
                }
            }
        else:
            return {"msg": "fail", "data": "Failed to get temperature data"}
    except Exception as e:
        logger_.error(f"获取温度信息失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.get(f"{uri}/system/cpu")
async def get_system_cpu(token: str = Depends(verify_token)):
    """获取CPU使用率信息"""
    try:
        # 读取/proc/stat获取CPU信息
        with open('/proc/stat', 'r') as f:
            cpu_line = f.readline().strip()
        
        # 解析CPU时间
        parts = cpu_line.split()[1:]
        user, nice, system, idle, iowait, irq, softirq, steal, guest, guest_nice = map(int, parts[:10])
        
        # 计算总时间和空闲时间
        total_time = user + nice + system + idle + iowait + irq + softirq + steal
        idle_time = idle + iowait
        
        # 计算CPU使用率
        cpu_usage = round((1 - idle_time / total_time) * 100, 2)
        
        # 获取CPU核心数
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpu_info = f.read()
            cpu_cores = cpu_info.count('processor')
        except:
            cpu_cores = 1
        
        return {
            "msg": "success", 
            "data": {
                "usage_percent": cpu_usage,
                "cores": cpu_cores,
                "unit": "%"
            }
        }
    except Exception as e:
        logger_.error(f"获取CPU信息失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.get(f"{uri}/system/memory")
async def get_system_memory(token: str = Depends(verify_token)):
    """获取内存使用情况"""
    try:
        with open('/proc/meminfo', 'r') as f:
            lines = f.readlines()
        
        mem_info = {}
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                mem_info[key.strip()] = int(value.strip().split()[0]) * 1024  # 转换为字节
        
        total = mem_info.get('MemTotal', 0)
        available = mem_info.get('MemAvailable', 0)
        used = total - available
        
        if total > 0:
            usage_percent = round((used / total) * 100, 2)
        else:
            usage_percent = 0
        
        return {
            "msg": "success",
            "data": {
                "total": total,
                "used": used,
                "available": available,
                "usage_percent": usage_percent,
                "unit": "bytes"
            }
        }
    except Exception as e:
        logger_.error(f"获取内存信息失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.get(f"{uri}/system/disk")
async def get_system_disk(token: str = Depends(verify_token)):
    """获取硬盘使用情况"""
    try:
        result = subprocess.run(['df', '-h', '--output=source,size,used,avail,pcent,target'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            disk_info = []
            
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 6:
                        source, size, used, avail, pcent, target = parts[:6]
                        # 移除百分号
                        usage_percent = pcent.replace('%', '')
                        
                        disk_info.append({
                            'device': source,
                            'mount_point': target,
                            'total': size,
                            'used': used,
                            'available': avail,
                            'usage_percent': int(usage_percent) if usage_percent.isdigit() else 0
                        })
            
            return {"msg": "success", "data": disk_info}
        else:
            return {"msg": "fail", "data": "Failed to get disk information"}
    except Exception as e:
        logger_.error(f"获取硬盘信息失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


@app.get(f"{uri}/system/overview")
async def get_system_overview(token: str = Depends(verify_token)):
    """获取系统概览信息（包含所有监控数据）"""
    try:
        # 获取温度信息
        temp_result = await get_system_temperature(token)
        temperature_data = temp_result.get("data", {}) if temp_result.get("msg") == "success" else {}
        
        # 获取CPU信息
        cpu_result = await get_system_cpu(token)
        cpu_data = cpu_result.get("data", {}) if cpu_result.get("msg") == "success" else {}
        
        # 获取内存信息
        memory_result = await get_system_memory(token)
        memory_data = memory_result.get("data", {}) if memory_result.get("msg") == "success" else {}
        
        # 获取硬盘信息
        disk_result = await get_system_disk(token)
        disk_data = disk_result.get("data", []) if disk_result.get("msg") == "success" else []
        
        return {
            "msg": "success",
            "data": {
                "temperature": temperature_data,
                "cpu": cpu_data,
                "memory": memory_data,
                "disk": disk_data,
                "timestamp": int(time.time())
            }
        }
    except Exception as e:
        logger_.error(f"获取系统概览失败: {str(e)}")
        return {"msg": "fail", "data": str(e)}


if __name__ == '__main__':
    uvicorn.run(app="front_interface:app", host="0.0.0.0", port=20400, workers=3)