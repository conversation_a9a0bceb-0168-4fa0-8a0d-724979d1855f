<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="70%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="methodForm" :rules="rules" ref="formRef" label-width="120px">
      <!-- 方法名称 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="方法名称" prop="name" required>
            <el-input 
              v-model="methodForm.name" 
              placeholder="procedureOne"
            />
            <div class="field-hint">存储过程或函数的名称</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="配置类型">
            <el-radio-group v-model="isSimpleMethod">
              <el-radio :label="true">简单方法</el-radio>
              <el-radio :label="false">复杂方法</el-radio>
            </el-radio-group>
            <div class="field-hint">简单方法只需要方法名，复杂方法可以配置参数和SQL</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 复杂方法配置 -->
      <div v-if="!isSimpleMethod" class="complex-config">
        <!-- SQL语句 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="SQL语句">
              <el-input 
                v-model="methodForm.query" 
                type="textarea" 
                :rows="3"
                placeholder="CALL procedureOne(?,?,?) 或 SELECT functionOne(?)"
              />
              <div class="field-hint">
                可选：自定义SQL语句。如果不填写，将使用默认的存储过程/函数调用
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 参数配置 -->
        <div class="params-section">
          <div class="section-title">
            参数配置
            <el-button type="primary" size="small" @click="addParameter">
              <el-icon><Plus /></el-icon>
              添加参数
            </el-button>
          </div>
          
          <div v-if="methodForm.args.length === 0" class="empty-params">
            <el-empty description="暂无参数配置" :image-size="60">
              <el-button type="primary" @click="addParameter">添加第一个参数</el-button>
            </el-empty>
          </div>
          
          <div v-else class="params-list">
            <div 
              v-for="(param, index) in methodForm.args" 
              :key="index" 
              class="param-item"
            >
              <el-card shadow="never" class="param-card">
                <template #header>
                  <div class="param-header">
                    <span class="param-title">参数 {{ index + 1 }}</span>
                    <el-button type="danger" size="small" link @click="deleteParameter(index)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </template>
                
                <el-row :gutter="16">
                  <el-col :span="8">
                    <el-form-item label="参数类型" required>
                      <el-select v-model="param.type" placeholder="选择参数类型">
                        <el-option label="字符串 (string)" value="string" />
                        <el-option label="数字 (number)" value="number" />
                        <el-option label="布尔值 (boolean)" value="boolean" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="16">
                    <el-form-item label="参数值">
                      <el-input 
                        v-model="param.value" 
                        :placeholder="getDefaultValuePlaceholder(param.type)"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </div>
        </div>

        <!-- 函数结果配置 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="返回结果">
              <el-switch v-model="methodForm.result" />
              <div class="field-hint">
                如果这是一个SQL函数且需要处理返回结果，请启用此选项
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 配置预览 -->
      <div class="config-preview">
        <el-alert
          title="配置预览"
          type="success"
          :closable="false"
          show-icon
        >
          <div class="preview-content">
            <pre>{{ getConfigPreview() }}</pre>
          </div>
        </el-alert>
      </div>

      <!-- 帮助信息 -->
      <div class="help-section">
        <el-alert
          title="RPC方法配置说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>支持的配置模式:</strong></p>
            <ul>
              <li><strong>简单方法:</strong> 字符串格式，如 "procedureOne"</li>
              <li><strong>复杂方法:</strong> 对象格式，包含name、args、query、result等配置</li>
              <li><strong>混合模式:</strong> 可以同时包含简单字符串和复杂对象</li>
            </ul>
            <p><strong>配置项说明:</strong></p>
            <ul>
              <li><strong>name:</strong> RPC方法或SQL存储过程/函数名称（必填）</li>
              <li><strong>args:</strong> 存储过程/函数参数列表（可选）</li>
              <li><strong>query:</strong> 自定义SQL查询语句（可选）</li>
              <li><strong>result:</strong> 是否处理函数返回结果（可选，仅用于SQL函数）</li>
            </ul>
            <p><strong>重要提示:</strong></p>
            <ul>
              <li>参数顺序必须与SQL存储过程/函数的参数顺序一致</li>
              <li>如果启用了"允许未知RPC"，RPC参数必须包含所有必需的配置项</li>
              <li>如果启用了"覆盖RPC配置"，RPC参数可以覆盖这里的配置</li>
            </ul>
          </div>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  method: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)
const isSimpleMethod = ref(true)

// 方法表单数据
const methodForm = reactive({
  name: '',
  query: '',
  args: [],
  result: false
})

// 获取对话框标题
const getDialogTitle = () => {
  return props.isEdit ? '编辑RPC方法' : '添加RPC方法'
}

// 获取默认值占位符
const getDefaultValuePlaceholder = (type) => {
  switch (type) {
    case 'string':
      return '例如: "hello"'
    case 'number':
      return '例如: 123'
    case 'boolean':
      return '例如: true'
    default:
      return '参数值'
  }
}

// 获取配置预览
const getConfigPreview = () => {
  if (isSimpleMethod.value) {
    return `"${methodForm.name}"`
  } else {
    const config = {
      name: methodForm.name
    }
    
    if (methodForm.args && methodForm.args.length > 0) {
      config.args = methodForm.args.map(arg => {
        switch (arg.type) {
          case 'number':
            const num = parseFloat(arg.value)
            return isNaN(num) ? 0 : num
          case 'boolean':
            return arg.value === 'true' || arg.value === true
          default:
            return arg.value || ''
        }
      })
    }
    
    if (methodForm.query && methodForm.query.trim()) {
      config.query = methodForm.query
    }
    
    if (methodForm.result) {
      config.result = true
    }
    
    return JSON.stringify(config, null, 2)
  }
}

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入方法名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '方法名称必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ]
})

// 添加参数
const addParameter = () => {
  methodForm.args.push({
    type: 'string',
    value: ''
  })
}

// 删除参数
const deleteParameter = (index) => {
  methodForm.args.splice(index, 1)
}

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.method) {
    // 加载方法数据
    if (typeof props.method === 'string') {
      // 简单方法
      isSimpleMethod.value = true
      Object.assign(methodForm, {
        name: props.method,
        query: '',
        args: [],
        result: false
      })
    } else {
      // 复杂方法
      isSimpleMethod.value = false
      Object.assign(methodForm, {
        name: props.method.name || '',
        query: props.method.query || '',
        args: (props.method.args || []).map(arg => {
          return {
            type: typeof arg === 'number' ? 'number' : typeof arg === 'boolean' ? 'boolean' : 'string',
            value: String(arg)
          }
        }),
        result: props.method.result || false
      })
    }
  } else if (newValue) {
    // 重置为默认值
    isSimpleMethod.value = false
    Object.assign(methodForm, {
      name: '',
      query: '',
      args: [],
      result: false
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    // 验证复杂方法的参数
    if (!isSimpleMethod.value) {
      // 检查SQL中的占位符数量与参数数量是否匹配（如果有SQL语句）
      if (methodForm.query && methodForm.query.trim()) {
        const placeholderCount = (methodForm.query.match(/\?/g) || []).length
        if (placeholderCount !== methodForm.args.length) {
          ElMessage.error(`SQL中有${placeholderCount}个占位符，但配置了${methodForm.args.length}个参数，数量不匹配`)
          return
        }
      }
    }
    
    saving.value = true
    
    // 准备保存数据
    let saveData
    
    if (isSimpleMethod.value) {
      // 简单方法，直接保存为字符串
      saveData = methodForm.name
    } else {
      // 复杂方法，保存为对象
      saveData = {
        name: methodForm.name
      }
      
      // 处理参数数据，转换为标准格式
      if (methodForm.args && methodForm.args.length > 0) {
        saveData.args = methodForm.args.map(arg => {
          switch (arg.type) {
            case 'number':
              const num = parseFloat(arg.value)
              return isNaN(num) ? 0 : num
            case 'boolean':
              return arg.value === 'true' || arg.value === true
            default:
              return arg.value || ''
          }
        })
      }
      
      if (methodForm.query && methodForm.query.trim()) {
        saveData.query = methodForm.query
      }
      
      if (methodForm.result) {
        saveData.result = true
      }
    }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? 'RPC方法更新成功' : 'RPC方法添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.complex-config {
  margin-top: 20px;
}

.params-section {
  margin-top: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f8f9fa;
  
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .empty-params {
    text-align: center;
    padding: 20px;
  }
  
  .params-list {
    .param-item {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .param-card {
        border: 1px solid #e4e7ed;
        
        .param-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .param-title {
            font-weight: 500;
            color: #303133;
          }
        }
      }
    }
  }
}

.config-preview {
  margin-top: 20px;
  
  .preview-content {
    pre {
      background: #f8f9fa;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      line-height: 1.4;
      margin: 0;
    }
  }
}

.help-section {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
      
      strong {
        color: #409eff;
      }
    }
    
    ul {
      margin: 8px 0 8px 20px;
      
      li {
        margin: 4px 0;
      }
    }
  }
}

.dialog-footer {
  padding: 20px 0 0;
  text-align: right;
}
</style> 