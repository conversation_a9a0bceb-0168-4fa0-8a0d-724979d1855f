<template>
  <div class="modbus-register-values">
    <el-tabs v-model="activeRegister" type="card">
      <el-tab-pane 
        v-for="(register, key) in registerTypes" 
        :key="key"
        :label="register.label" 
        :name="key">
        <div class="register-config">
          <div class="register-header">
            <div class="register-title">{{ register.label }}配置</div>
            <div class="register-description">{{ register.description }}</div>
          </div>

          <div class="register-content">
            <ModbusDataValues 
              v-model="registerValues[key]" 
              :register-type="key"
              :is-slave="true"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import ModbusDataValues from './ModbusDataValues.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      holding_registers: {
        attributes: [],
        timeseries: [],
        attributeUpdates: [],
        rpc: []
      },
      coils_initializer: {
        attributes: [],
        timeseries: [],
        attributeUpdates: [],
        rpc: []
      },
      input_registers: {
        attributes: [],
        timeseries: [],
        attributeUpdates: [],
        rpc: []
      },
      discrete_inputs: {
        attributes: [],
        timeseries: [],
        attributeUpdates: [],
        rpc: []
      }
    })
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const activeRegister = ref('holding_registers')

const registerValues = reactive({
  holding_registers: {
    attributes: [],
    timeseries: [],
    attributeUpdates: [],
    rpc: []
  },
  coils_initializer: {
    attributes: [],
    timeseries: [],
    attributeUpdates: [],
    rpc: []
  },
  input_registers: {
    attributes: [],
    timeseries: [],
    attributeUpdates: [],
    rpc: []
  },
  discrete_inputs: {
    attributes: [],
    timeseries: [],
    attributeUpdates: [],
    rpc: []
  },
  ...props.modelValue
})

// 寄存器类型配置
const registerTypes = {
  holding_registers: {
    label: '保持寄存器',
    description: '可读写的寄存器，用于存储配置参数和状态信息'
  },
  coils_initializer: {
    label: '线圈寄存器',
    description: '可读写的布尔值，用于控制开关状态'
  },
  input_registers: {
    label: '输入寄存器',
    description: '只读寄存器，用于读取模拟量输入'
  },
  discrete_inputs: {
    label: '离散输入',
    description: '只读布尔值，用于读取数字量输入状态'
  }
}

// 监听配置变化
watch(registerValues, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(registerValues, newValue)
}, { deep: true })
</script>

<style lang="scss" scoped>
.modbus-register-values {
  .register-config {
    .register-header {
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;

      .register-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .register-description {
        font-size: 13px;
        color: #606266;
      }
    }

    .register-content {
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
    }
  }

  :deep(.el-tabs__header) {
    margin-bottom: 16px;
  }

  :deep(.el-tabs__item) {
    font-size: 14px;
    padding: 8px 16px;
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }
}
</style> 