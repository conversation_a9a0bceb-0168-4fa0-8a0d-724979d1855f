<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="dataPointForm" :rules="rules" ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="消息类型筛选" prop="messageTypeFilter" required>
            <el-select v-model="dataPointForm.messageTypeFilter" placeholder="请选择消息类型">
              <el-option label="MeterValues - 计量值" value="MeterValues" />
              <el-option label="DataTransfer - 数据传输" value="DataTransfer" />
              <el-option label="StatusNotification - 状态通知" value="StatusNotification" />
              <el-option label="Heartbeat - 心跳" value="Heartbeat" />
              <el-option label="BootNotification - 启动通知" value="BootNotification" />
              <el-option label="StartTransaction - 开始事务" value="StartTransaction" />
              <el-option label="StopTransaction - 结束事务" value="StopTransaction" />
              <el-option label="Authorize - 授权" value="Authorize" />
              <el-option label="自定义" value="custom" />
            </el-select>
            <div class="field-hint">指定要处理的OCPP消息类型</div>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="dataPointForm.messageTypeFilter === 'custom'">
          <el-form-item label="自定义消息类型" prop="customMessageType">
            <el-input v-model="dataPointForm.customMessageType" placeholder="CustomMessage" />
            <div class="field-hint">输入自定义的OCPP消息类型</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="键名" prop="key" required>
            <el-input 
              v-model="dataPointForm.key" 
              :placeholder="getKeyPlaceholder()"
            />
            <div class="field-hint">{{ getKeyHint() }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="值表达式" prop="value" required>
            <el-input 
              v-model="dataPointForm.value" 
              :placeholder="getValuePlaceholder()"
            />
            <div class="field-hint">{{ getValueHint() }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- OCPP表达式语法说明 -->
      <div class="expression-help">
        <el-alert
          title="OCPP数据表达式语法说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>消息类型说明:</strong></p>
            <ul>
              <li><strong>MeterValues</strong> - 充电桩计量数据，包含功率、电流、电压等</li>
              <li><strong>DataTransfer</strong> - 厂商自定义数据传输</li>
              <li><strong>StatusNotification</strong> - 充电桩状态变化通知</li>
              <li><strong>StartTransaction/StopTransaction</strong> - 充电事务开始/结束</li>
            </ul>
            
            <p><strong>常用表达式模式:</strong></p>
            <ul>
              <li><strong>${meter_value[:].sampled_value[:].value}</strong> - 提取所有计量值</li>
              <li><strong>${meter_value[0].sampled_value[0].value}</strong> - 提取第一个计量值</li>
              <li><strong>${connector_id}</strong> - 连接器ID</li>
              <li><strong>${transaction_id}</strong> - 事务ID</li>
              <li><strong>${data.temp}</strong> - 数据传输中的温度（DataTransfer消息）</li>
              <li><strong>${status}</strong> - 充电桩状态</li>
              <li><strong>${error_code}</strong> - 错误代码</li>
              <li><strong>${vendor_id}</strong> - 厂商ID</li>
            </ul>
            
            <p><strong>配置示例:</strong></p>
            <ul>
              <li>温度传感器: key="temp1", value="${meter_value[:].sampled_value[:].value}", filter="MeterValues"</li>
              <li>连接器状态: key="vendorId", value="${connector_id}", filter="StatusNotification"</li>
              <li>自定义数据: key="temp", value="${data.temp}", filter="DataTransfer"</li>
            </ul>
          </div>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dataPoint: {
    type: Object,
    default: null
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries'].includes(value)
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 数据点表单数据
const dataPointForm = reactive({
  messageTypeFilter: '',
  customMessageType: '',
  key: '',
  value: ''
})

// 获取对话框标题
const getDialogTitle = () => {
  const action = props.isEdit ? '编辑' : '添加'
  const type = props.dataType === 'attributes' ? '属性' : '遥测'
  return `${action}${type}数据点`
}

// 获取键名占位符
const getKeyPlaceholder = () => {
  if (props.dataType === 'attributes') {
    return 'temp1'
  } else {
    return 'temperature'
  }
}

// 获取值表达式占位符
const getValuePlaceholder = () => {
  return '${meter_value[:].sampled_value[:].value}'
}

// 获取键名提示
const getKeyHint = () => {
  if (props.dataType === 'attributes') {
    return 'OCPP充电桩属性的键名标识'
  } else {
    return 'OCPP充电桩遥测数据的键名标识'
  }
}

// 获取值表达式提示
const getValueHint = () => {
  return '用于从OCPP消息中提取数据的表达式'
}

// 表单验证规则
const rules = reactive({
  messageTypeFilter: [
    { required: true, message: '请选择消息类型筛选', trigger: 'change' }
  ],
  key: [
    { required: true, message: '请输入键名', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '请输入值表达式', trigger: 'blur' }
  ],
  customMessageType: [
    { 
      required: true, 
      message: '请输入自定义消息类型', 
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (dataPointForm.messageTypeFilter === 'custom' && !value) {
          callback(new Error('请输入自定义消息类型'))
        } else {
          callback()
        }
      }
    }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.dataPoint) {
    // 加载数据点数据
    const defaultKey = props.dataType === 'attributes' ? 'temp1' : 'temperature'
    const defaultValue = '${meter_value[:].sampled_value[:].value}'
    const defaultFilter = props.dataType === 'attributes' ? 'MeterValues' : 'DataTransfer'
    
    Object.assign(dataPointForm, {
      messageTypeFilter: defaultFilter,
      customMessageType: '',
      key: defaultKey,
      value: defaultValue,
      ...props.dataPoint
    })
  } else if (newValue) {
    // 重置为默认值
    const defaultKey = props.dataType === 'attributes' ? 'temp1' : 'temperature'
    const defaultValue = '${meter_value[:].sampled_value[:].value}'
    const defaultFilter = props.dataType === 'attributes' ? 'MeterValues' : 'DataTransfer'
    
    Object.assign(dataPointForm, {
      messageTypeFilter: defaultFilter,
      customMessageType: '',
      key: defaultKey,
      value: defaultValue
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = {
      messageTypeFilter: dataPointForm.messageTypeFilter === 'custom' ? 
        dataPointForm.customMessageType : 
        dataPointForm.messageTypeFilter,
      key: dataPointForm.key,
      value: dataPointForm.value
    }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '数据点更新成功' : '数据点添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.expression-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style> 