<template>
  <el-card class="server-rpc-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <div class="card-title">
          <span>RPC配置 {{ index + 1 }}</span>
          <el-tag :type="hasResponse ? 'success' : 'info'" size="small">
            {{ hasResponse ? '有响应' : '无响应' }}
          </el-tag>
          <el-tag :type="getMethodTagType(rpcData.httpMethod)" size="small">
            {{ rpcData.httpMethod || 'GET' }}
          </el-tag>
        </div>
        <el-button type="danger" size="small" @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>
    
    <el-form :model="rpcData" label-width="120px">
      <!-- 基础配置 -->
      <div class="section">
        <div class="section-title">基础配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="设备名筛选" required>
              <el-input 
                v-model="rpcData.deviceNameFilter" 
                placeholder=".*"
                @input="updateValue"
              />
              <div class="field-hint">正则表达式，匹配设备名称</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="方法筛选" required>
              <el-input 
                v-model="rpcData.methodFilter" 
                placeholder="echo"
                @input="updateValue"
              />
              <div class="field-hint">正则表达式，匹配RPC方法名</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="HTTP方法" required>
              <el-select v-model="rpcData.httpMethod" @change="updateValue">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- URL和值表达式配置 -->
      <div class="section">
        <div class="section-title">表达式配置</div>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="请求URL表达式" required>
              <el-input 
                v-model="rpcData.requestUrlExpression" 
                placeholder="sensor/${deviceName}/request/${methodName}/${requestId}"
                @input="updateValue"
              />
              <div class="field-hint">
                支持变量: ${deviceName}, ${methodName}, ${requestId}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="请求值表达式" required>
              <el-input 
                v-model="rpcData.requestValueExpression" 
                type="textarea"
                :rows="3"
                placeholder="${params}"
                @input="updateValue"
              />
              <div class="field-hint">
                支持变量: ${params}, ${params.key}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="hasResponse">
            <el-form-item label="响应值表达式">
              <el-input 
                v-model="rpcData.responseValueExpression" 
                type="textarea"
                :rows="3"
                placeholder="${temp}"
                @input="updateValue"
              />
              <div class="field-hint">
                从响应中提取返回值的表达式
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 超时和重试配置 -->
      <div class="section">
        <div class="section-title">超时和重试配置</div>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="请求超时(秒)">
              <el-input-number 
                v-model="rpcData.timeout" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="hasResponse">
            <el-form-item label="响应超时(秒)">
              <el-input-number 
                v-model="rpcData.responseTimeout" 
                :min="0" 
                :precision="2"
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="重试次数">
              <el-input-number 
                v-model="rpcData.tries" 
                :min="0" 
                controls-position="right"
                style="width: 100%"
                @change="updateValue"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- HTTP头配置 -->
      <div class="section">
        <div class="section-title">HTTP头配置</div>
        <RequestHttpHeaders v-model="rpcData.httpHeaders" @update:modelValue="updateValue" />
      </div>

      <!-- RPC类型配置 -->
      <div class="section">
        <div class="section-title">
          <span>RPC类型配置</span>
          <el-switch 
            v-model="hasResponse" 
            @change="toggleResponseType"
            active-text="有响应"
            inactive-text="无响应"
            size="small"
          />
        </div>
        
        <div class="rpc-type-info">
          <el-alert 
            :type="hasResponse ? 'success' : 'info'" 
            :title="hasResponse ? '有响应RPC' : '无响应RPC'"
            :description="hasResponse ? 
              '发送请求后等待响应，并将响应返回给ThingsBoard' : 
              '发送请求后不等待响应，适用于控制类操作'"
            show-icon
            :closable="false"
          />
        </div>
      </div>

      <!-- 表达式帮助 -->
      <div class="section">
        <el-collapse size="small">
          <el-collapse-item name="help" title="表达式变量说明">
            <div class="help-content">
              <div class="help-section">
                <h4>URL表达式变量</h4>
                <ul>
                  <li><code>${deviceName}</code> - 设备名称</li>
                  <li><code>${methodName}</code> - RPC方法名</li>
                  <li><code>${requestId}</code> - 请求ID</li>
                </ul>
              </div>
              <div class="help-section">
                <h4>请求值表达式变量</h4>
                <ul>
                  <li><code>${params}</code> - 完整的参数对象</li>
                  <li><code>${params.key}</code> - 参数对象中的特定字段</li>
                  <li><code>${params.hum}</code> - 获取湿度参数</li>
                </ul>
              </div>
              <div class="help-section">
                <h4>响应值表达式变量</h4>
                <ul>
                  <li><code>${temp}</code> - 响应中的温度值</li>
                  <li><code>${result}</code> - 响应结果</li>
                  <li><code>${status}</code> - 响应状态</li>
                </ul>
              </div>
              <div class="help-section">
                <h4>示例配置</h4>
                <ul>
                  <li>有响应: methodFilter = "echo", responseValueExpression = "${temp}"</li>
                  <li>无响应: methodFilter = "no-reply", 无需响应表达式</li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!-- 配置预览 -->
      <div class="section" v-if="rpcData.deviceNameFilter && rpcData.methodFilter">
        <div class="section-title">配置预览</div>
        <div class="preview-content">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="RPC类型">
              <el-tag :type="hasResponse ? 'success' : 'info'" size="small">
                {{ hasResponse ? '有响应' : '无响应' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="HTTP方法">
              <el-tag :type="getMethodTagType(rpcData.httpMethod)" size="small">
                {{ rpcData.httpMethod }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="设备筛选">
              <code class="filter-code">{{ rpcData.deviceNameFilter }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="方法筛选">
              <code class="filter-code">{{ rpcData.methodFilter }}</code>
            </el-descriptions-item>
            <el-descriptions-item label="超时设置">
              请求{{ rpcData.timeout }}秒{{ hasResponse ? `，响应${rpcData.responseTimeout}秒` : '' }}
            </el-descriptions-item>
            <el-descriptions-item label="重试次数">
              {{ rpcData.tries }}次
            </el-descriptions-item>
            <el-descriptions-item label="URL模板" span="2">
              <code class="expression-code">{{ rpcData.requestUrlExpression }}</code>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-form>
  </el-card>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import RequestHttpHeaders from './RequestHttpHeaders.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

const hasResponse = ref(true)

// RPC数据
const rpcData = computed({
  get: () => ({
    deviceNameFilter: '.*',
    methodFilter: '',
    requestUrlExpression: '',
    responseTimeout: 1,
    httpMethod: 'GET',
    requestValueExpression: '',
    responseValueExpression: '',
    timeout: 0.5,
    tries: 3,
    httpHeaders: {
      'Content-Type': 'application/json'
    },
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 获取HTTP方法标签类型
const getMethodTagType = (method) => {
  const methodTagMap = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'PATCH': 'info',
    'DELETE': 'danger'
  }
  return methodTagMap[method] || 'primary'
}

// 切换响应类型
const toggleResponseType = (withResponse) => {
  const newData = { ...rpcData.value }
  
  if (withResponse) {
    // 有响应RPC
    newData.responseTimeout = 1
    newData.responseValueExpression = '${temp}'
    if (!newData.methodFilter || newData.methodFilter === 'no-reply') {
      newData.methodFilter = 'echo'
    }
  } else {
    // 无响应RPC
    delete newData.responseTimeout
    delete newData.responseValueExpression
    if (!newData.methodFilter || newData.methodFilter === 'echo') {
      newData.methodFilter = 'no-reply'
    }
  }
  
  emit('update:modelValue', newData)
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...rpcData.value })
}

// 删除配置
const handleDelete = () => {
  emit('delete')
}

// 监听数据变化，判断是否有响应
watch(() => props.modelValue, (newValue) => {
  hasResponse.value = !!(newValue.responseTimeout !== undefined || newValue.responseValueExpression)
}, { immediate: true })
</script>

<style lang="scss" scoped>
.server-rpc-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .section {
    margin-bottom: 24px;
    
    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .rpc-type-info {
    margin-top: 12px;
  }
  
  .help-content {
    .help-section {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #606266;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 4px;
          font-size: 13px;
          color: #606266;
          
          code {
            background: #f5f7fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
            color: #e6a23c;
          }
        }
      }
    }
  }
  
  .preview-content {
    .filter-code,
    .expression-code {
      background: #f5f7fa;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
      color: #e6a23c;
      font-size: 12px;
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .el-collapse-item__content {
    padding: 12px 0 0 0;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}

:deep(.el-switch__label) {
  font-size: 12px;
}
</style> 