<template>
  <el-card class="data-key-card" shadow="never">
    <template #header>
      <div class="key-header">
        <div class="key-info">
          <el-tag :type="dataType === 'attribute' ? 'success' : 'info'" size="small">
            {{ dataType === 'attribute' ? '属性' : '遥测' }}
          </el-tag>
          <span class="key-name">{{ dataKey.key || '未命名' }}</span>
        </div>
        <el-button type="danger" size="small" link @click="handleDelete">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
      </div>
    </template>

    <el-form :model="dataKey" label-width="80px">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="键名称" required>
            <el-input 
              v-model="dataKey.key" 
              placeholder="serialNumber"
              @input="updateValue"
            />
            <div class="field-hint">{{ dataType === 'attribute' ? '属性' : '遥测' }}数据的键名称</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据类型" required>
            <el-select v-model="dataKey.type" @change="updateValue">
              <el-option label="字符串" value="string" />
              <el-option label="整数" value="int" />
              <el-option label="浮点数" value="float" />
              <el-option label="布尔值" value="boolean" />
              <el-option label="长整数" value="long" />
              <el-option label="双精度" value="double" />
            </el-select>
            <div class="field-hint">数据值的类型</div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="值表达式" required>
            <el-input 
              v-model="dataKey.value" 
              placeholder="${serial}"
              @input="updateValue"
            />
            <div class="field-hint">JSON路径表达式或常量</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 表达式帮助 -->
      <el-row>
        <el-col :span="24">
          <div class="expression-help">
            <el-collapse size="small">
              <el-collapse-item name="help" title="表达式说明">
                <div class="help-content">
                  <div class="help-section">
                    <h4>JSON路径表达式</h4>
                    <ul>
                      <li><code>${data.temperature}</code> - 获取JSON中data对象的temperature字段</li>
                      <li><code>${sensors[0].value}</code> - 获取数组第一个元素的value字段</li>
                      <li><code>${status}</code> - 获取根级别的status字段</li>
                    </ul>
                  </div>
                  <div class="help-section">
                    <h4>常量值</h4>
                    <ul>
                      <li><code>固定值</code> - 直接使用固定文本作为值</li>
                      <li><code>123</code> - 使用固定数字</li>
                      <li><code>true</code> - 使用布尔值</li>
                    </ul>
                  </div>
                  <div class="help-section">
                    <h4>组合表达式</h4>
                    <ul>
                      <li><code>${prefix}_${id}</code> - 组合多个表达式</li>
                      <li><code>Device ${name}</code> - 混合常量和表达式</li>
                    </ul>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-col>
      </el-row>

      <!-- 预览和验证 -->
      <el-row v-if="dataKey.key && dataKey.value">
        <el-col :span="24">
          <div class="key-preview">
            <div class="preview-title">配置预览</div>
            <div class="preview-content">
              <el-descriptions :column="3" size="small" border>
                <el-descriptions-item label="键名称">
                  <el-tag size="small">{{ dataKey.key }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="数据类型">
                  <el-tag type="info" size="small">{{ getTypeDisplay(dataKey.type) }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="值表达式">
                  <code class="expression-code">{{ dataKey.value }}</code>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>

<script setup>
import { computed, watch } from 'vue'
import { Delete } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      key: '',
      type: 'string',
      value: ''
    })
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attribute', 'telemetry'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'delete'])

// 数据键配置
const dataKey = computed({
  get: () => ({
    key: '',
    type: 'string',
    value: '',
    ...props.modelValue
  }),
  set: (value) => emit('update:modelValue', value)
})

// 获取数据类型显示名称
const getTypeDisplay = (type) => {
  const typeMap = {
    'string': '字符串',
    'int': '整数',
    'float': '浮点数',
    'boolean': '布尔值',
    'long': '长整数',
    'double': '双精度'
  }
  return typeMap[type] || type
}

// 更新值
const updateValue = () => {
  emit('update:modelValue', { ...dataKey.value })
}

// 处理删除
const handleDelete = () => {
  emit('delete')
}

// 确保默认值
watch(() => props.modelValue, (newValue) => {
  if (!newValue.type) {
    updateValue()
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.data-key-card {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  
  &:hover {
    border-color: #c0c4cc;
  }
  
  .key-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .key-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .key-name {
        font-weight: 600;
        color: #303133;
      }
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .expression-help {
    margin-top: 16px;
    
    .help-content {
      .help-section {
        margin-bottom: 16px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h4 {
          margin: 0 0 8px 0;
          font-size: 14px;
          font-weight: 600;
          color: #606266;
        }
        
        ul {
          margin: 0;
          padding-left: 20px;
          
          li {
            margin-bottom: 4px;
            font-size: 13px;
            color: #606266;
            
            code {
              background: #f5f7fa;
              padding: 2px 4px;
              border-radius: 3px;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
              color: #e6a23c;
            }
          }
        }
      }
    }
  }
  
  .key-preview {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
    
    .preview-title {
      font-size: 14px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 12px;
    }
    
    .expression-code {
      background: #f5f7fa;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
      color: #e6a23c;
      font-size: 12px;
    }
  }
}

:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f2f5;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-collapse) {
  border: none;
  
  .el-collapse-item__header {
    background: #fafafa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .el-collapse-item__content {
    padding: 12px 0 0 0;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style> 