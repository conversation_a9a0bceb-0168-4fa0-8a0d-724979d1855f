---
name: Bug report
about: Create a report to help us improve
title: "[BUG]"
labels: bug
assignees: imbeacon, samson0v

---

**Describe the bug**
Clear and concise description of what the bug is.

**Connector name (If bug in the some connector):**
[e.g. OPC-UA Connector]

**Error traceback (If available):**
```
'deviceName'
Traceback (most recent call last):
  File "<input>", line 2, in <module>
KeyError: 'deviceName'
```

**Versions (please complete the following information):**
 - OS: [e.g. Ubuntu 18.04]
 - Thingsboard IoT Gateway version [e.g. 2.0]
 - Python version[e.g. 3.7]
