<template>
  <div class="ftp-config">
    <el-tabs v-model="activeTab" class="ftp-tabs" v-if="configInitialized">
      <!-- 通用配置标签页 -->
      <el-tab-pane label="通用配置" name="general">
        <CommonGeneralConfig 
          v-model="config" 
        />
      </el-tab-pane>

      <!-- FTP服务器配置标签页 -->
      <el-tab-pane label="FTP服务器配置" name="server">
        <FtpServerConfig 
          v-model="config.server" 
          v-if="config.server"
        />
      </el-tab-pane>

      <!-- 文件路径配置标签页 -->
      <el-tab-pane label="文件路径配置" name="paths">
        <FtpPathsConfig 
          v-model="config.paths" 
          v-if="config.paths"
        />
      </el-tab-pane>

      <!-- 属性更新配置标签页 -->
      <el-tab-pane label="属性更新配置" name="attributeUpdates">
        <FtpAttributeUpdatesConfig 
          v-model="config.attributeUpdates" 
          v-if="config.attributeUpdates"
        />
      </el-tab-pane>

      <!-- RPC配置标签页 -->
      <el-tab-pane label="RPC配置" name="serverSideRpc">
        <FtpRpcConfig 
          v-model="config.serverSideRpc" 
          v-if="config.serverSideRpc"
        />
      </el-tab-pane>
    </el-tabs>
    <div v-else class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { initializeConnectorConfig, ensureConfigId } from '@/utils/connectorInit.js'
import FtpServerConfig from './components/FtpServerConfig.vue'
import FtpPathsConfig from './components/FtpPathsConfig.vue'
import FtpAttributeUpdatesConfig from './components/FtpAttributeUpdatesConfig.vue'
import FtpRpcConfig from './components/FtpRpcConfig.vue'
import CommonGeneralConfig from './components/CommonGeneralConfig.vue'

const route = useRoute()
const activeTab = ref('general')
const configInitialized = ref(false)

// 默认配置结构
const defaultConfigStructure = {
  logLevel: 'INFO',
  enableRemoteLogging: false,
  id: '',
  reportStrategy: {
    type: 'ON_RECEIVED',
    reportPeriod: 60000
  },
  server: {
    host: 'ftp.example.com',
    port: 21,
    username: 'admin',
    password: '',
    path: '/',
    fileNamePattern: '*.csv'
  },
  paths: [],
  attributeUpdates: [],
  serverSideRpc: []
}

// 配置数据结构 - 使用ref而不是reactive
const config = ref({ ...defaultConfigStructure })

// 初始化配置
onMounted(async () => {
  try {
    // 使用统一的初始化系统
    const success = await initializeConnectorConfig(config.value, 'ftp', route, defaultConfigStructure)
    
    if (success) {
      // 确保有ID
      ensureConfigId(config.value)
      
      // 标记配置已初始化
      configInitialized.value = true
        
      console.log('FTP 连接器配置初始化成功')
    } else {
      ElMessage.warning('FTP 连接器配置初始化部分失败，请检查配置')
      configInitialized.value = true // 即使失败也要显示界面
    }
  } catch (error) {
    console.error('FTP 连接器初始化失败:', error)
    ElMessage.error('FTP 连接器初始化失败')
    configInitialized.value = true // 即使失败也要显示界面
  }
})

// 暴露配置数据给父组件
defineExpose({ 
  ftp: config
})
</script>

<style lang="scss" scoped>
.ftp-config {
  .ftp-tabs {
  :deep(.el-tabs__content) {
    padding: 20px 0;
  }
  }
  
  .loading-state {
    padding: 20px;
  }
}
</style>