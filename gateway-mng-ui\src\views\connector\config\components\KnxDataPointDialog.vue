<template>
  <el-dialog
    v-model="dialogVisible"
    :title="getDialogTitle()"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form :model="dataPointForm" :rules="rules" ref="formRef" label-width="140px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="键名" prop="key" required>
            <el-input 
              v-model="dataPointForm.key" 
              :placeholder="dataType === 'attributes' ? 'temperature' : 'humidity'"
            />
            <div class="field-hint">数据点的键名标识</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="KNX群组地址" prop="groupAddress" required>
            <el-input 
              v-model="dataPointForm.groupAddress" 
              placeholder="1/0/6"
            />
            <div class="field-hint">KNX总线上的群组地址</div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="数据类型" prop="type" required>
            <el-select v-model="dataPointForm.type" placeholder="请选择数据类型">
              <!-- 通用数据类型 -->
              <el-option-group label="基础数据类型">
                <el-option label="布尔值 (bool)" value="bool" />
                <el-option label="整数 (int)" value="int" />
                <el-option label="浮点数 (float)" value="float" />
                <el-option label="字符串 (string)" value="string" />
              </el-option-group>
              
              <!-- KNX专用数据类型 -->
              <el-option-group label="KNX数据类型">
                <el-option label="温度 (temperature)" value="temperature" />
                <el-option label="湿度 (humidity)" value="humidity" />
                <el-option label="亮度 (brightness)" value="brightness" />
                <el-option label="百分比 U8 (precent_U8)" value="precent_U8" />
                <el-option label="百分比 V8 (precent_V8)" value="precent_V8" />
                <el-option label="角度 (angle)" value="angle" />
                <el-option label="色彩 (color)" value="color" />
                <el-option label="时间 (time)" value="time" />
                <el-option label="日期 (date)" value="date" />
              </el-option-group>
            </el-select>
            <div class="field-hint">{{ getDataTypeHint() }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- KNX地址格式说明 -->
      <div class="address-help">
        <el-alert
          title="KNX群组地址格式说明"
          type="info"
          :closable="false"
          show-icon
        >
          <div class="help-content">
            <p><strong>群组地址格式:</strong></p>
            <ul>
              <li><strong>长格式</strong>: x/y/z (例如: 1/0/6) - 推荐使用</li>
              <li><strong>短格式</strong>: xyz (例如: 106)</li>
            </ul>
            <p><strong>常用数据类型:</strong></p>
            <ul>
              <li><strong>temperature</strong> - 温度传感器数据</li>
              <li><strong>humidity</strong> - 湿度传感器数据</li>
              <li><strong>brightness</strong> - 亮度控制</li>
              <li><strong>precent_U8</strong> - 0-100%的无符号百分比</li>
              <li><strong>bool</strong> - 开关状态</li>
            </ul>
            <p><strong>示例配置:</strong></p>
            <ul>
              <li>温度传感器: key="temperature", groupAddress="1/0/6", type="temperature"</li>
              <li>湿度传感器: key="humidity", groupAddress="1/0/7", type="humidity"</li>
              <li>亮度控制: key="brightness", groupAddress="1/0/9", type="precent_U8"</li>
            </ul>
          </div>
        </el-alert>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  dataPoint: {
    type: Object,
    default: null
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ['attributes', 'timeseries'].includes(value)
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const dialogVisible = ref(false)
const formRef = ref()
const saving = ref(false)

// 数据点表单数据
const dataPointForm = reactive({
  key: '',
  groupAddress: '',
  type: ''
})

// 获取对话框标题
const getDialogTitle = () => {
  const action = props.isEdit ? '编辑' : '添加'
  const type = props.dataType === 'attributes' ? '属性' : '遥测'
  return `${action}${type}数据点`
}

// 获取数据类型提示
const getDataTypeHint = () => {
  if (props.dataType === 'attributes') {
    return 'KNX设备属性的数据类型'
  } else {
    return 'KNX设备遥测数据的类型'
  }
}

// 表单验证规则
const rules = reactive({
  key: [
    { required: true, message: '请输入键名', trigger: 'blur' }
  ],
  groupAddress: [
    { required: true, message: '请输入KNX群组地址', trigger: 'blur' },
    { 
      pattern: /^(\d+\/\d+\/\d+|\d+)$/,
      message: '群组地址格式：x/y/z 或 xyz',
      trigger: 'blur'
    }
  ],
  type: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ]
})

// 监听对话框显示状态
watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue
  if (newValue && props.dataPoint) {
    // 加载数据点数据
    const defaultKey = props.dataType === 'attributes' ? 'temperature' : 'humidity'
    const defaultType = props.dataType === 'attributes' ? 'temperature' : 'humidity'
    
    Object.assign(dataPointForm, {
      key: defaultKey,
      groupAddress: '1/0/6',
      type: defaultType,
      ...props.dataPoint
    })
  } else if (newValue) {
    // 重置为默认值
    const defaultKey = props.dataType === 'attributes' ? 'temperature' : 'humidity'
    const defaultType = props.dataType === 'attributes' ? 'temperature' : 'humidity'
    
    Object.assign(dataPointForm, {
      key: defaultKey,
      groupAddress: '1/0/6',
      type: defaultType
    })
  }
}, { immediate: true })

// 监听对话框关闭
watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理保存
const handleSave = async () => {
  try {
    // 验证表单
    await formRef.value?.validate()
    
    saving.value = true
    
    // 准备保存数据
    const saveData = { ...dataPointForm }
    
    emit('save', saveData)
    
    ElMessage.success(props.isEdit ? '数据点更新成功' : '数据点添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    saving.value = false
  }
}

// 处理关闭
const handleClose = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped>
.field-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.address-help {
  margin-top: 20px;
  
  .help-content {
    line-height: 1.6;
    
    p {
      margin: 8px 0;
    }
    
    ul {
      margin: 8px 0;
      padding-left: 20px;
      
      li {
        margin: 4px 0;
        
        strong {
          color: #409eff;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
  
  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}
</style> 