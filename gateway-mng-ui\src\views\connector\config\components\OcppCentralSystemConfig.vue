<template>
  <div class="ocpp-central-system-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>中央系统配置</span>
          <el-tooltip content="配置OCPP中央系统连接参数和安全设置" placement="top">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </div>
      </template>
      
      <el-form :model="centralSystemConfig" :rules="rules" label-width="140px" ref="formRef">
        <!-- 基础信息配置 -->
        <div class="connection-section">
          <div class="section-title">基础信息</div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="系统名称" prop="name" required>
                <el-input v-model="centralSystemConfig.name" placeholder="Central System"  />
                <div class="field-hint">中央系统的显示名称</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="服务器地址" prop="host" required>
                <el-input v-model="centralSystemConfig.host" placeholder="127.0.0.1"  />
                <div class="field-hint">OCPP中央系统的IP地址或域名</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="端口" prop="port" required>
                <el-input-number 
                  v-model="centralSystemConfig.port" 
                  :min="1" 
                  :max="65535"
                  :precision="0"
                  controls-position="right"
                  
                />
                <div class="field-hint">OCPP服务端口，默认9000</div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 连接配置 -->
        <div class="connection-section">
          <div class="section-title">连接配置</div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="连接类型" prop="connection.type" required>
                <el-select v-model="centralSystemConfig.connection.type" placeholder="请选择连接类型" >
                  <el-option label="不安全连接 (insecure)" value="insecure" />
                  <el-option label="TLS安全连接 (tls)" value="tls" />
                </el-select>
                <div class="field-hint">WebSocket连接的安全类型</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <!-- TLS配置 -->
          <div v-if="centralSystemConfig.connection.type === 'tls'" class="tls-config">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="私钥路径" prop="connection.key">
                  <el-input v-model="centralSystemConfig.connection.key" placeholder="/path/to/server.key"  />
                  <div class="field-hint">TLS私钥文件路径</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="证书路径" prop="connection.cert">
                  <el-input v-model="centralSystemConfig.connection.cert" placeholder="/path/to/server.crt"  />
                  <div class="field-hint">TLS证书文件路径</div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 安全认证配置 -->
        <div class="security-section">
          <div class="section-title">
            安全认证
            <el-button type="primary" size="small" @click="addSecurity">
              <el-icon><Plus /></el-icon>
              添加认证
            </el-button>
          </div>
          
          <div v-if="centralSystemConfig.security.length === 0" class="empty-state">
            <el-empty description="暂无安全认证配置" :image-size="60">
              <el-button type="primary" @click="addSecurity">添加第一个认证方式</el-button>
            </el-empty>
          </div>
          
          <div v-else class="security-list">
            <el-card 
              v-for="(security, index) in centralSystemConfig.security" 
              :key="index" 
              class="security-card"
              shadow="hover"
            >
              <template #header>
                <div class="security-header">
                  <div class="security-info">
                    <span class="security-type">{{ getSecurityTypeName(security.type) }}</span>
                    <el-tag size="small" :type="getSecurityTagType(security.type)">{{ security.type }}</el-tag>
                  </div>
                  <div class="security-actions">
                    <el-button type="danger" size="small" link @click="deleteSecurity(index)">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </template>
              
              <div class="security-config">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="认证类型">
                      <el-select v-model="security.type" >
                        <el-option label="匿名认证" value="anonymous" />
                        <el-option label="Token认证" value="token" />
                        <el-option label="基础认证" value="basic" />
                      </el-select>
                      <div class="field-hint">选择安全认证方式</div>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <!-- Token认证配置 -->
                <div v-if="security.type === 'token'" class="token-config">
                  <el-form-item label="Token列表">
                    <div class="token-list">
                      <div v-for="(token, tokenIndex) in security.tokens" :key="tokenIndex" class="token-item">
                        <el-input 
                          v-model="security.tokens[tokenIndex]" 
                          placeholder="Bearer ACCESS_TOKEN"
                          
                        >
                          <template #append>
                            <el-button @click="deleteToken(security, tokenIndex)" :icon="Delete" />
                          </template>
                        </el-input>
                      </div>
                      <el-button type="primary" plain @click="addToken(security)">
                        <el-icon><Plus /></el-icon>
                        添加Token
                      </el-button>
                    </div>
                    <div class="field-hint">配置允许的访问令牌</div>
                  </el-form-item>
                </div>
                
                <!-- 基础认证配置 -->
                <div v-if="security.type === 'basic'" class="basic-config">
                  <el-form-item label="用户凭证">
                    <div class="credentials-list">
                      <div v-for="(credential, credIndex) in security.credentials" :key="credIndex" class="credential-item">
                        <el-row :gutter="12">
                          <el-col :span="10">
                            <el-input 
                              v-model="credential.username" 
                              placeholder="用户名"
                              
                            />
                          </el-col>
                          <el-col :span="10">
                            <el-input 
                              v-model="credential.password" 
                              placeholder="密码"
                              type="password"
                              show-password
                              
                            />
                          </el-col>
                          <el-col :span="4">
                            <el-button @click="deleteCredential(security, credIndex)" :icon="Delete" />
                          </el-col>
                        </el-row>
                      </div>
                      <el-button type="primary" plain @click="addCredential(security)">
                        <el-icon><Plus /></el-icon>
                        添加凭证
                      </el-button>
                    </div>
                    <div class="field-hint">配置用户名和密码组合</div>
                  </el-form-item>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 连接测试 -->
        <div class="test-section">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-button 
                type="primary" 
                :loading="testing" 
                @click="testConnection"
                :disabled="!isConfigValid"
              >
                <el-icon><Connection /></el-icon>
                测试连接
              </el-button>
              <span class="test-hint">点击测试OCPP中央系统连接是否正常</span>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, defineEmits, defineProps } from 'vue'
import { QuestionFilled, Plus, Delete, Connection } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      name: 'Central System',
      host: '127.0.0.1',
      port: 9000,
      connection: {
        type: 'insecure'
      },
      security: []
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const formRef = ref()
const testing = ref(false)

// 中央系统配置数据
const centralSystemConfig = reactive({
  name: 'Central System',
  host: '127.0.0.1',
  port: 9000,
  connection: {
    type: 'insecure'
  },
  security: []
})

// 表单验证规则
const rules = reactive({
  name: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' },
    { 
      pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^[a-zA-Z0-9.-]+$/,
      message: '请输入有效的IP地址或域名',
      trigger: 'blur'
    }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号范围：1-65535', trigger: 'blur' }
  ],
  'connection.type': [
    { required: true, message: '请选择连接类型', trigger: 'change' }
  ]
})

// 获取安全类型名称
const getSecurityTypeName = (type) => {
  const names = {
    'anonymous': '匿名认证',
    'token': 'Token认证',
    'basic': '基础认证'
  }
  return names[type] || type
}

// 获取安全类型标签类型
const getSecurityTagType = (type) => {
  const types = {
    'anonymous': 'info',
    'token': 'success',
    'basic': 'warning'
  }
  return types[type] || 'info'
}

// 检查配置是否有效
const isConfigValid = computed(() => {
  return !!(centralSystemConfig.host && centralSystemConfig.port && centralSystemConfig.name)
})

// 添加安全认证
const addSecurity = () => {
  centralSystemConfig.security.push({
    type: 'anonymous',
    tokens: [],
    credentials: []
  })
  handleChange()
}

// 删除安全认证
const deleteSecurity = async (index) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个安全认证配置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    centralSystemConfig.security.splice(index, 1)
    handleChange()
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

// 添加Token
const addToken = (security) => {
  if (!security.tokens) {
    security.tokens = []
  }
  security.tokens.push('Bearer ACCESS_TOKEN')
  handleChange()
}

// 删除Token
const deleteToken = (security, index) => {
  security.tokens.splice(index, 1)
  handleChange()
}

// 添加凭证
const addCredential = (security) => {
  if (!security.credentials) {
    security.credentials = []
  }
  security.credentials.push({
    username: '',
    password: ''
  })
  handleChange()
}

// 删除凭证
const deleteCredential = (security, index) => {
  security.credentials.splice(index, 1)
  handleChange()
}

// 测试连接
const testConnection = async () => {
  try {
    await formRef.value?.validate()
    testing.value = true
    
    // 模拟连接测试
    setTimeout(() => {
      testing.value = false
      ElMessage.success('OCPP中央系统连接测试成功！')
    }, 2000)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查配置参数')
  }
}

// 处理配置变化
const handleChange = () => {
  emit('update:modelValue', { ...centralSystemConfig })
}

// 监听外部配置变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    Object.assign(centralSystemConfig, {
      name: 'Central System',
      host: '127.0.0.1',
      port: 9000,
      connection: {
        type: 'insecure'
      },
      security: [],
      ...newValue
    })
    
    // 确保安全配置的数据结构正确
    if (centralSystemConfig.security) {
      centralSystemConfig.security.forEach(security => {
        if (security.type === 'token' && !security.tokens) {
          security.tokens = []
        }
        if (security.type === 'basic' && !security.credentials) {
          security.credentials = []
        }
      })
    }
  }
}, { deep: true, immediate: true })

// 监听内部配置变化
watch(centralSystemConfig, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })
</script>

<style lang="scss" scoped>
.ocpp-central-system-config {
  .config-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .field-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
    line-height: 1.4;
  }

  .connection-section,
  .security-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f8f9fa;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .tls-config {
    margin-top: 16px;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background: #fafafa;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
  }

  .security-list {
    .security-card {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .security-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .security-info {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .security-type {
            font-weight: 500;
            color: #303133;
          }
        }
        
        .security-actions {
          display: flex;
          gap: 8px;
        }
      }
      
      .security-config {
        margin-top: 16px;
      }
    }
  }

  .token-config, .basic-config {
    margin-top: 16px;
    
    .token-list, .credentials-list {
      .token-item, .credential-item {
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 16px;
        }
      }
    }
  }

  .test-section {
    margin-top: 24px;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #f0f9ff;
    
    .test-hint {
      margin-left: 12px;
      font-size: 12px;
      color: #909399;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
  }

  :deep(.el-input-number) {
    width: 100%;
  }

  :deep(.el-select) {
    width: 100%;
  }
}
</style> 